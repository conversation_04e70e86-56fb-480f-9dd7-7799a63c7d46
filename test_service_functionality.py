#!/usr/bin/env python3
"""
Test MixpanelService Functionality

This script tests our updated MixpanelService to validate all 5 required data points
are working and returning real data.

The 5 Data Points:
1. New site activation rate broken down by site
2. Enabled permissions % (first time run)
3. Weekly active Safari extension users
4. Weekly active users since launch
5. Weekly retention metrics
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from dags.dependencies.mixpanel.mixpanel_service import MixpanelService

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise
logger = logging.getLogger(__name__)

def main():
    """Test all 5 data points using our MixpanelService."""
    print("🧪 Testing MixpanelService - All 5 Data Points")
    print("="*60)
    
    # Load credentials
    load_dotenv()
    
    import os
    credentials = {
        'project_id': os.getenv('MIXPANEL_PROJECT_ID'),
        'service_account_username': os.getenv('MIXPANEL_SERVICE_USERNAME'),
        'service_account_secret': os.getenv('MIXPANEL_SERVICE_SECRET'),
        'workspace_id': os.getenv('MIXPANEL_WORKSPACE_ID')
    }
    
    print(f"Project ID: {credentials['project_id']}")
    print(f"Workspace ID: {credentials['workspace_id']}")
    
    # Initialize service (no rate limiting for testing)
    service = MixpanelService(
        project_id=credentials['project_id'],
        service_account_username=credentials['service_account_username'],
        service_account_secret=credentials['service_account_secret'],
        workspace_id=credentials['workspace_id'],
        respect_rate_limits=False  # Disable for testing
    )
    
    # Test date ranges
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)  # Last 7 days
    from_date = start_date.strftime('%Y-%m-%d')
    to_date = end_date.strftime('%Y-%m-%d')
    
    # Cohort dates for retention
    cohort_start = "2024-06-19"
    cohort_end = "2024-06-25"
    
    print(f"Main date range: {from_date} to {to_date}")
    print(f"Retention cohort: {cohort_start} to {cohort_end}")
    
    # Test results tracking
    results = {}
    
    # Test 1: Connection
    print(f"\n{'='*60}")
    print("TEST 1: CONNECTION")
    print("="*60)
    try:
        if service.test_connection():
            print("✅ Connection successful!")
            results["connection"] = "✅ PASS"
        else:
            print("❌ Connection failed!")
            results["connection"] = "❌ FAIL"
            return
    except Exception as e:
        print(f"❌ Connection error: {e}")
        results["connection"] = f"❌ ERROR: {e}"
        return
    
    # Test 2: Site Activation Metrics
    print(f"\n{'='*60}")
    print("TEST 2: SITE ACTIVATION RATE BY DOMAIN")
    print("="*60)
    print("Testing: phia_shown + phia_clicked + mobile extension rates")
    
    try:
        metrics = service.get_site_activation_metrics(from_date, to_date, limit=5)
        
        if metrics and len(metrics) > 0:
            print(f"✅ SUCCESS! Found {len(metrics)} sites with activation data")
            results["site_activation"] = "✅ PASS"
            
            print("\nTop sites:")
            for i, metric in enumerate(metrics[:3]):
                print(f"  {i+1}. {metric.site_domain}")
                print(f"     Phia Shown: {metric.phia_shown_count:,}")
                print(f"     Phia Clicked: {metric.phia_clicked_count:,}")
                print(f"     Click Rate: {metric.click_rate:.2f}%")
                print(f"     Mobile Shown: {metric.mobile_extension_shown:,}")
                print(f"     Mobile Activated: {metric.mobile_extension_activated:,}")
                print(f"     Mobile Rate: {metric.mobile_activation_rate:.2f}%")
                print(f"     Combined Rate: {metric.combined_activation_rate:.2f}%")
        else:
            print("❌ No site activation data returned")
            results["site_activation"] = "❌ FAIL - No data"
            
    except Exception as e:
        print(f"❌ Site activation failed: {e}")
        results["site_activation"] = f"❌ ERROR: {e}"
    
    # Test 3: Permission Funnel
    print(f"\n{'='*60}")
    print("TEST 3: ENABLED PERMISSIONS % (FIRST TIME RUN)")
    print("="*60)
    print("Testing: page_view domain:phia.com pathnames")
    
    try:
        funnel = service.get_permission_funnel_metrics(from_date, to_date)
        
        print(f"✅ Permission funnel data retrieved!")
        results["permission_funnel"] = "✅ PASS"
        
        print(f"  Enable Phia Views: {funnel.enable_phia_views:,}")
        print(f"  Almost Finished Views: {funnel.almost_finished_views:,}")
        print(f"  Conversion Rate: {funnel.conversion_rate:.2f}%")
        print(f"  Funnel Completion: {funnel.funnel_completion_rate:.2f}%")
        
        if funnel.enable_phia_views > 0:
            print("✅ Has actual funnel data")
        else:
            print("⚠️  No funnel activity in date range")
            
    except Exception as e:
        print(f"❌ Permission funnel failed: {e}")
        results["permission_funnel"] = f"❌ ERROR: {e}"
    
    # Test 4: Safari Extension Users
    print(f"\n{'='*60}")
    print("TEST 4: WEEKLY ACTIVE SAFARI EXTENSION USERS")
    print("="*60)
    print("Testing: phia_clicked Platform=IOS_SAFARI_EXTENSION, no tutorial URLs")
    
    try:
        safari = service.get_safari_extension_metrics(from_date, to_date)
        
        print(f"✅ Safari extension data retrieved!")
        results["safari_extension"] = "✅ PASS"
        
        print(f"  Safari Active Users: {safari.active_users:,}")
        print(f"  Total Clicks: {safari.total_clicks:,}")
        print(f"  Filtered Clicks: {safari.filtered_clicks:,}")
        
        print(f"  Platform Breakdown:")
        for platform, count in safari.platform_breakdown.items():
            print(f"    {platform}: {count:,}")
            
        if safari.active_users > 0:
            print("✅ Has Safari extension users")
        else:
            print("⚠️  No Safari extension activity")
            
    except Exception as e:
        print(f"❌ Safari extension failed: {e}")
        results["safari_extension"] = f"❌ ERROR: {e}"
    
    # Test 5: Weekly Active Users
    print(f"\n{'='*60}")
    print("TEST 5: WEEKLY ACTIVE USERS SINCE LAUNCH")
    print("="*60)
    print("Testing: heartbeat distinct phia_id, permission_state=All, OS breakdown")
    
    try:
        weekly = service.get_weekly_active_users_metrics(from_date, to_date)
        
        print(f"✅ Weekly active users data retrieved!")
        results["weekly_active_users"] = "✅ PASS"
        
        print(f"  Total Active Users: {weekly.total_active_users:,}")
        print(f"  Heartbeat Events: {weekly.heartbeat_events:,}")
        
        print(f"  OS Breakdown:")
        for os, count in weekly.os_breakdown.items():
            print(f"    {os}: {count:,}")
            
        print(f"  Permission State Breakdown:")
        for state, count in weekly.permission_state_breakdown.items():
            print(f"    {state}: {count:,}")
            
        if weekly.total_active_users > 0:
            print("✅ Has active user data")
        else:
            print("⚠️  No active user data")
            
    except Exception as e:
        print(f"❌ Weekly active users failed: {e}")
        results["weekly_active_users"] = f"❌ ERROR: {e}"
    
    # Test 6: Weekly Retention
    print(f"\n{'='*60}")
    print("TEST 6: WEEKLY RETENTION METRICS")
    print("="*60)
    print("Testing: heartbeat + phia_clicked retention for activated users")
    
    try:
        retention = service.get_weekly_retention_metrics(
            cohort_start, cohort_end, analysis_weeks=4
        )
        
        print(f"✅ Weekly retention data retrieved!")
        results["weekly_retention"] = "✅ PASS"
        
        print(f"  Cohort Period: {retention.cohort_start_date} to {retention.cohort_end_date}")
        print(f"  Initial Cohort Size: {retention.initial_cohort_size:,}")
        
        print(f"  Weekly Retention Rates:")
        for week, data in retention.weekly_retention_rates.items():
            print(f"    {week}: Heartbeat {data['heartbeat_retention_rate']:.1f}% | "
                  f"Phia Clicked {data['phia_clicked_retention_rate']:.1f}%")
            
        if retention.initial_cohort_size > 0:
            print("✅ Has retention cohort data")
        else:
            print("⚠️  No users in retention cohort")
            
    except Exception as e:
        print(f"❌ Weekly retention failed: {e}")
        results["weekly_retention"] = f"❌ ERROR: {e}"
    
    # Test 7: Comprehensive Analytics
    print(f"\n{'='*60}")
    print("TEST 7: COMPREHENSIVE ANALYTICS (ALL TOGETHER)")
    print("="*60)
    
    try:
        analytics = service.get_comprehensive_analytics(
            from_date=from_date,
            to_date=to_date,
            cohort_start_date=cohort_start,
            cohort_end_date=cohort_end,
            analysis_weeks=4
        )
        
        if analytics['success']:
            print(f"✅ Comprehensive analytics successful!")
            results["comprehensive"] = "✅ PASS"
            
            data = analytics['data']
            print(f"  Data sections: {len(data)}")
            for section in data.keys():
                print(f"    - {section}")
                
            # Save results to file
            output_file = f"comprehensive_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w') as f:
                json.dump(analytics, f, indent=2, default=str)
            print(f"  💾 Full results saved to: {output_file}")
            
        else:
            print(f"❌ Comprehensive analytics failed: {analytics.get('error', 'Unknown')}")
            results["comprehensive"] = f"❌ FAIL: {analytics.get('error')}"
            
    except Exception as e:
        print(f"❌ Comprehensive analytics error: {e}")
        results["comprehensive"] = f"❌ ERROR: {e}"
    
    # Final Summary
    print(f"\n{'='*60}")
    print("🎯 FINAL TEST RESULTS")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result.startswith("✅"))
    
    for test_name, result in results.items():
        status_icon = "✅" if result.startswith("✅") else "❌"
        print(f"{status_icon} {test_name.replace('_', ' ').title():<25}: {result}")
    
    print(f"\n📊 OVERALL SCORE: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Your MixpanelService is fully functional!")
        print("🚀 Ready for production Airflow DAG!")
    elif passed_tests >= total_tests * 0.7:  # 70% pass rate
        print("✅ Most tests passed! Service is mostly functional.")
        print("🔧 Fix remaining issues for full functionality.")
    else:
        print("⚠️  Many tests failed. Service needs more fixes.")
        print("🔍 Check error messages above for debugging.")
    
    print(f"\n📋 NEXT STEPS:")
    if passed_tests == total_tests:
        print("1. ✅ Deploy to Airflow DAG")
        print("2. ✅ Set up database tables")
        print("3. ✅ Schedule daily runs")
    else:
        failed_tests = [name for name, result in results.items() if not result.startswith("✅")]
        print(f"1. 🔧 Fix failed tests: {', '.join(failed_tests)}")
        print("2. 🧪 Re-run this test")
        print("3. 🚀 Deploy when all tests pass")

if __name__ == "__main__":
    main()