steps:
  # install dependencies
  # run
  # install dependencies
  - name: python
    entrypoint: pip3
    args: ["install", "-r", "utils/requirements.txt", "--user"]

  - name: python
    entrypoint: python3
    args:
      [
        "utils/add_dags_to_composer.py",
        "--dags_directory=${_DAGS_DIRECTORY}",
        "--dags_bucket=${_DAGS_BUCKET}",
        "--name_replacement=${_DAGS_DIRECTORY}",
        "--environment=${_ENV}",
      ]

options:
  logging: CLOUD_LOGGING_ONLY
