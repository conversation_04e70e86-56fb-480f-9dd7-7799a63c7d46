-- Migration: Add New Dashboard Components Tables
-- Description: Adds support for the 5 new dashboard components from Mixpanel screenshots
-- Date: 2025-07-01
-- Author: Data Pipeline

-- ========================================
-- SITE ACTIVATION RATES TABLE (Enhanced)
-- ========================================
-- Update existing site_activation table to support new site activation rate component
-- This stores (phia_clicked/phia_shown)*100 broken down by hostname

-- Add new columns to existing site_activation table if they don't exist
DO $$ 
BEGIN
    -- Add phia_shown_count column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'site_activation' AND column_name = 'phia_shown_count') THEN
        ALTER TABLE site_activation ADD COLUMN phia_shown_count INTEGER DEFAULT 0;
    END IF;
    
    -- Add phia_clicked_count column  
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'site_activation' AND column_name = 'phia_clicked_count') THEN
        ALTER TABLE site_activation ADD COLUMN phia_clicked_count INTEGER DEFAULT 0;
    END IF;
    
    -- Add hostname column (more specific than site_domain)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'site_activation' AND column_name = 'hostname') THEN
        ALTER TABLE site_activation ADD COLUMN hostname TEXT;
    END IF;
    
    -- Add platform filter column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'site_activation' AND column_name = 'platform') THEN
        ALTER TABLE site_activation ADD COLUMN platform TEXT DEFAULT 'IOS_SAFARI_EXTENSION';
    END IF;
END $$;

-- Update unique constraint to include hostname and platform
DROP INDEX IF EXISTS site_activation_date_site_domain_key;
CREATE UNIQUE INDEX IF NOT EXISTS site_activation_unique_idx 
ON site_activation(date, hostname, platform);

-- ========================================
-- ONBOARDING FUNNEL TABLE (Enhanced)
-- ========================================
-- Update existing onboarding_funnel table to support new funnel analysis

-- Add new columns for the specific onboarding funnel steps
DO $$ 
BEGIN
    -- Add step1_users (pathname=/mobile/almost-finished)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'onboarding_funnel' AND column_name = 'step1_users') THEN
        ALTER TABLE onboarding_funnel ADD COLUMN step1_users INTEGER DEFAULT 0;
    END IF;
    
    -- Add step2_users (domain=phia.com)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'onboarding_funnel' AND column_name = 'step2_users') THEN
        ALTER TABLE onboarding_funnel ADD COLUMN step2_users INTEGER DEFAULT 0;
    END IF;
    
    -- Add overall_conversion_rate
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'onboarding_funnel' AND column_name = 'overall_conversion_rate') THEN
        ALTER TABLE onboarding_funnel ADD COLUMN overall_conversion_rate DECIMAL(5,2);
    END IF;
END $$;

-- ========================================
-- WEEKLY RETENTION COHORTS TABLE (Enhanced)
-- ========================================
-- Update existing cohort_retention table to support Week 9 retention analysis

-- Add new columns for enhanced retention tracking
DO $$ 
BEGIN
    -- Add activation_event column to track what defines the cohort
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'cohort_retention' AND column_name = 'activation_event') THEN
        ALTER TABLE cohort_retention ADD COLUMN activation_event TEXT DEFAULT 'phia_clicked';
    END IF;
    
    -- Add retention_event column to track what defines retention
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'cohort_retention' AND column_name = 'retention_event') THEN
        ALTER TABLE cohort_retention ADD COLUMN retention_event TEXT DEFAULT 'heartbeat';
    END IF;
    
    -- Add cohort_end_date for better tracking
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'cohort_retention' AND column_name = 'cohort_end_date') THEN
        ALTER TABLE cohort_retention ADD COLUMN cohort_end_date DATE;
    END IF;
END $$;

-- ========================================
-- SAFARI EXTENSION METRICS TABLE (New)
-- ========================================
-- New table specifically for Safari extension metrics

CREATE TABLE IF NOT EXISTS safari_extension_metrics (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    metric_name TEXT NOT NULL, -- 'safari_users_since_launch', 'weekly_active_safari_users'
    event_name TEXT NOT NULL, -- 'heartbeat', 'phia_clicked', etc.
    total_events BIGINT DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    platform TEXT DEFAULT 'IOS_SAFARI_EXTENSION',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(date, metric_name, event_name)
);

-- ========================================
-- DASHBOARD COMPONENTS SUMMARY TABLE (New)
-- ========================================
-- New table to store aggregated data for all 5 dashboard components

CREATE TABLE IF NOT EXISTS dashboard_components (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    component_name TEXT NOT NULL, -- 'week_9_retention', 'safari_users_since_launch', etc.
    component_data JSONB NOT NULL, -- Flexible JSON storage for component-specific data
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(date, component_name)
);

-- ========================================
-- INDEXES FOR PERFORMANCE
-- ========================================

-- Indexes for site_activation table
CREATE INDEX IF NOT EXISTS idx_site_activation_hostname ON site_activation(hostname);
CREATE INDEX IF NOT EXISTS idx_site_activation_platform ON site_activation(platform);
CREATE INDEX IF NOT EXISTS idx_site_activation_date_desc ON site_activation(date DESC);

-- Indexes for onboarding_funnel table
CREATE INDEX IF NOT EXISTS idx_onboarding_funnel_date_desc ON onboarding_funnel(date DESC);
CREATE INDEX IF NOT EXISTS idx_onboarding_funnel_step ON onboarding_funnel(funnel_step);

-- Indexes for cohort_retention table
CREATE INDEX IF NOT EXISTS idx_cohort_retention_start_date ON cohort_retention(cohort_start_date);
CREATE INDEX IF NOT EXISTS idx_cohort_retention_week ON cohort_retention(week_number);
CREATE INDEX IF NOT EXISTS idx_cohort_retention_activation_event ON cohort_retention(activation_event);

-- Indexes for safari_extension_metrics table
CREATE INDEX IF NOT EXISTS idx_safari_metrics_date_desc ON safari_extension_metrics(date DESC);
CREATE INDEX IF NOT EXISTS idx_safari_metrics_metric_name ON safari_extension_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_safari_metrics_event_name ON safari_extension_metrics(event_name);

-- Indexes for dashboard_components table
CREATE INDEX IF NOT EXISTS idx_dashboard_components_date_desc ON dashboard_components(date DESC);
CREATE INDEX IF NOT EXISTS idx_dashboard_components_name ON dashboard_components(component_name);

-- ========================================
-- COMMENTS FOR DOCUMENTATION
-- ========================================

COMMENT ON TABLE safari_extension_metrics IS 'Safari extension specific metrics filtered by IOS_SAFARI_EXTENSION platform';
COMMENT ON TABLE dashboard_components IS 'Aggregated data for all 5 dashboard components with flexible JSON storage';

COMMENT ON COLUMN site_activation.phia_shown_count IS 'Number of phia_shown events for this hostname/date';
COMMENT ON COLUMN site_activation.phia_clicked_count IS 'Number of phia_clicked events for this hostname/date';
COMMENT ON COLUMN site_activation.hostname IS 'Specific hostname (more granular than site_domain)';

COMMENT ON COLUMN onboarding_funnel.step1_users IS 'Users who reached pathname=/mobile/almost-finished';
COMMENT ON COLUMN onboarding_funnel.step2_users IS 'Users who reached domain=phia.com';
COMMENT ON COLUMN onboarding_funnel.overall_conversion_rate IS 'Overall funnel conversion rate (step2/step1)*100';

COMMENT ON COLUMN cohort_retention.activation_event IS 'Event that defines cohort membership (e.g., phia_clicked)';
COMMENT ON COLUMN cohort_retention.retention_event IS 'Event that defines retention (e.g., heartbeat)';
COMMENT ON COLUMN cohort_retention.cohort_end_date IS 'End date of cohort period';

-- Migration complete
SELECT 'New dashboard components tables created/updated successfully' AS status;
