-- Migration: Optimize Mixpanel Service Integration
-- Description: Ensures all tables and indexes are optimized for the new MixpanelService data
-- Date: 2025-07-02
-- Author: Data Pipeline

-- ========================================
-- ENSURE ALL REQUIRED TABLES EXIST
-- ========================================

-- Verify site_activation table has correct structure
DO $$
BEGIN
    -- Ensure site_activation table exists with correct columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'site_activation') THEN
        CREATE TABLE site_activation (
            id SERIAL PRIMARY KEY,
            date DATE NOT NULL,
            site_domain TEXT NOT NULL,
            total_users INTEGER DEFAULT 0,
            activated_users INTEGER DEFAULT 0,
            activation_rate DECIMAL(5,2),
            created_at TIMESTAMPTZ DEFAULT NOW(),
            UNIQUE(date, site_domain)
        );
    END IF;
END $$;

-- Verify onboarding_funnel table has correct structure
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'onboarding_funnel') THEN
        CREATE TABLE onboarding_funnel (
            id SERIAL PRIMARY KEY,
            date DATE NOT NULL,
            funnel_step TEXT NOT NULL,
            step_order INTEGER,
            users_count INTEGER DEFAULT 0,
            conversion_rate DECIMAL(5,2),
            created_at TIMESTAMPTZ DEFAULT NOW(),
            UNIQUE(date, funnel_step)
        );
    END IF;
END $$;

-- Verify safari_extension_metrics table has correct structure
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'safari_extension_metrics') THEN
        CREATE TABLE safari_extension_metrics (
            id SERIAL PRIMARY KEY,
            date DATE NOT NULL,
            metric_name TEXT NOT NULL,
            event_name TEXT NOT NULL,
            total_events BIGINT DEFAULT 0,
            unique_users INTEGER DEFAULT 0,
            platform TEXT DEFAULT 'IOS_SAFARI_EXTENSION',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            UNIQUE(date, metric_name, event_name)
        );
    END IF;
END $$;

-- Verify daily_metrics table has correct structure
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'daily_metrics') THEN
        CREATE TABLE daily_metrics (
            id SERIAL PRIMARY KEY,
            date DATE NOT NULL,
            event_name TEXT NOT NULL,
            total_events BIGINT DEFAULT 0,
            unique_users INTEGER DEFAULT 0,
            metric_type TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            UNIQUE(date, event_name)
        );
    END IF;
END $$;

-- Verify cohort_retention table has correct structure
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'cohort_retention') THEN
        CREATE TABLE cohort_retention (
            id SERIAL PRIMARY KEY,
            cohort_start_date DATE NOT NULL,
            cohort_id INTEGER,
            cohort_name TEXT,
            cohort_size INTEGER,
            week_number INTEGER,
            active_users INTEGER DEFAULT 0,
            retention_rate DECIMAL(5,2),
            week_start_date DATE,
            week_end_date DATE,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            UNIQUE(cohort_start_date, cohort_id, week_number)
        );
    END IF;
END $$;

-- ========================================
-- FIX DATA TYPE PRECISION ISSUES
-- ========================================

-- Increase precision for activation_rate to handle edge cases
DO $$
BEGIN
    -- Update activation_rate column to handle larger values
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'site_activation' AND column_name = 'activation_rate') THEN
        ALTER TABLE site_activation ALTER COLUMN activation_rate TYPE DECIMAL(7,2);
    END IF;
END $$;

-- ========================================
-- PERFORMANCE INDEXES
-- ========================================

-- Indexes for efficient querying of MixpanelService data
CREATE INDEX IF NOT EXISTS idx_site_activation_date_domain ON site_activation(date DESC, site_domain);
CREATE INDEX IF NOT EXISTS idx_site_activation_rate ON site_activation(activation_rate DESC);

CREATE INDEX IF NOT EXISTS idx_onboarding_funnel_date_step ON onboarding_funnel(date DESC, funnel_step);
CREATE INDEX IF NOT EXISTS idx_onboarding_funnel_conversion ON onboarding_funnel(conversion_rate DESC);

CREATE INDEX IF NOT EXISTS idx_safari_metrics_date_metric ON safari_extension_metrics(date DESC, metric_name);
CREATE INDEX IF NOT EXISTS idx_safari_metrics_users ON safari_extension_metrics(unique_users DESC);

CREATE INDEX IF NOT EXISTS idx_daily_metrics_date_event ON daily_metrics(date DESC, event_name);
CREATE INDEX IF NOT EXISTS idx_daily_metrics_type ON daily_metrics(metric_type, date DESC);

CREATE INDEX IF NOT EXISTS idx_cohort_retention_date_week ON cohort_retention(cohort_start_date, week_number);
CREATE INDEX IF NOT EXISTS idx_cohort_retention_rate ON cohort_retention(retention_rate DESC);

-- ========================================
-- COMMENTS FOR DOCUMENTATION
-- ========================================

COMMENT ON TABLE site_activation IS 'Site activation metrics from MixpanelService - phia_shown/phia_clicked conversion rates by domain';
COMMENT ON TABLE onboarding_funnel IS 'Permission funnel metrics from MixpanelService - enable-phia to almost-finished conversion';
COMMENT ON TABLE safari_extension_metrics IS 'Safari extension specific metrics from MixpanelService - IOS_SAFARI_EXTENSION platform data';
COMMENT ON TABLE daily_metrics IS 'Weekly active users and other daily metrics from MixpanelService';
COMMENT ON TABLE cohort_retention IS 'Weekly retention analysis from MixpanelService - cohort-based user retention';

-- Migration complete
SELECT 'MixpanelService integration optimization complete' AS status;
