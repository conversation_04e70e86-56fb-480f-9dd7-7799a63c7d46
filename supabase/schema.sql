-- Supabase Schema for Normalized Transaction Data
-- This schema supports both Strackr and ShopMy transaction data

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create normalized_transactions table
CREATE TABLE IF NOT EXISTS normalized_transactions (
    -- Primary key and identifiers
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id VARCHAR(255) UNIQUE NOT NULL, -- Unique across platforms (platform_originalid)
    platform VARCHAR(50) NOT NULL CHECK (platform IN ('strackr', 'shopmy')),
    source_transaction_id VARCHAR(255) NOT NULL,
    
    -- Financial data
    currency CHAR(3) NOT NULL, -- ISO currency code
    order_amount DECIMAL(15,2) NOT NULL CHECK (order_amount >= 0),
    commission_amount DECIMAL(15,2) NOT NULL CHECK (commission_amount >= 0),
    final_order_amount DECIMAL(15,2) CHECK (final_order_amount >= 0),
    final_commission_amount DECIMAL(15,2) CHECK (final_commission_amount >= 0),
    
    -- Transaction details
    order_id VARCHAR(255) NOT NULL,
    customer_id VARCHAR(255),
    transaction_date TIMESTAMPTZ NOT NULL,
    created_date TIMESTAMPTZ NOT NULL,
    
    -- Merchant/Network information
    network_name VARCHAR(255) NOT NULL,
    merchant_name VARCHAR(255) NOT NULL,
    merchant_id VARCHAR(255),
    connection_name VARCHAR(255),
    
    -- Status and classification
    status VARCHAR(50) NOT NULL,
    transaction_type VARCHAR(100),
    decline_reason TEXT,
    
    -- Additional metadata
    channel_name VARCHAR(255),
    custom_fields JSONB, -- Platform-specific data
    comments TEXT,
    last_updated TIMESTAMPTZ NOT NULL,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_platform ON normalized_transactions(platform);
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_transaction_date ON normalized_transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_status ON normalized_transactions(status);
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_network ON normalized_transactions(network_name);
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_merchant ON normalized_transactions(merchant_name);
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_currency ON normalized_transactions(currency);
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_created_at ON normalized_transactions(created_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_platform_date ON normalized_transactions(platform, transaction_date);
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_platform_status ON normalized_transactions(platform, status);
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_date_status ON normalized_transactions(transaction_date, status);

-- JSONB index for custom fields
CREATE INDEX IF NOT EXISTS idx_normalized_transactions_custom_fields ON normalized_transactions USING GIN (custom_fields);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_normalized_transactions_updated_at 
    BEFORE UPDATE ON normalized_transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create view for transaction summaries
CREATE OR REPLACE VIEW transaction_summary AS
SELECT 
    platform,
    DATE_TRUNC('day', transaction_date) as transaction_day,
    status,
    currency,
    COUNT(*) as transaction_count,
    SUM(order_amount) as total_order_amount,
    SUM(commission_amount) as total_commission_amount,
    AVG(order_amount) as avg_order_amount,
    AVG(commission_amount) as avg_commission_amount
FROM normalized_transactions
GROUP BY platform, DATE_TRUNC('day', transaction_date), status, currency
ORDER BY transaction_day DESC, platform, status;

-- Create view for daily platform summaries
CREATE OR REPLACE VIEW daily_platform_summary AS
SELECT 
    platform,
    DATE_TRUNC('day', transaction_date) as date,
    COUNT(*) as total_transactions,
    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_transactions,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
    COUNT(CASE WHEN status = 'declined' THEN 1 END) as declined_transactions,
    SUM(order_amount) as total_order_amount,
    SUM(commission_amount) as total_commission_amount,
    SUM(CASE WHEN status = 'confirmed' THEN commission_amount ELSE 0 END) as confirmed_commission
FROM normalized_transactions
GROUP BY platform, DATE_TRUNC('day', transaction_date)
ORDER BY date DESC, platform;

-- Create view for merchant performance
CREATE OR REPLACE VIEW merchant_performance AS
SELECT 
    platform,
    merchant_name,
    network_name,
    COUNT(*) as total_transactions,
    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_transactions,
    ROUND(
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END)::DECIMAL / COUNT(*) * 100, 2
    ) as confirmation_rate,
    SUM(commission_amount) as total_commission,
    AVG(commission_amount) as avg_commission,
    MAX(transaction_date) as last_transaction_date
FROM normalized_transactions
GROUP BY platform, merchant_name, network_name
HAVING COUNT(*) >= 5 -- Only show merchants with at least 5 transactions
ORDER BY total_commission DESC;

-- Row Level Security (RLS) setup
ALTER TABLE normalized_transactions ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users (adjust based on your auth setup)
CREATE POLICY "Allow authenticated users to read transactions" ON normalized_transactions
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create policy for service role to insert/update (for DAG operations)
CREATE POLICY "Allow service role full access" ON normalized_transactions
    FOR ALL USING (auth.role() = 'service_role');

-- Grant permissions
GRANT SELECT ON normalized_transactions TO authenticated;
GRANT SELECT ON transaction_summary TO authenticated;
GRANT SELECT ON daily_platform_summary TO authenticated;
GRANT SELECT ON merchant_performance TO authenticated;

-- Grant full access to service role for DAG operations
GRANT ALL ON normalized_transactions TO service_role;
