#!/usr/bin/env python3
"""
Test heartbeat events for cohort retention
"""

import os
import sys

# Load environment variables (optional)
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ python-dotenv not installed, using system environment variables")

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dags.dependencies.mixpanel.mixpanel_service import MixpanelService

def test_heartbeat_cohort():
    """Test heartbeat events for cohort retention"""
    
    print("🔍 Testing Heartbeat Events for Cohort Retention")
    print("=" * 60)
    
    # Initialize service with environment variables
    project_id = os.getenv("MIXPANEL_PROJECT_ID")
    service_account_username = os.getenv("MIXPANEL_SERVICE_USERNAME")
    service_account_secret = os.getenv("MIXPANEL_SERVICE_SECRET")
    workspace_id = os.getenv("MIXPANEL_WORKSPACE_ID")
    
    if not all([project_id, service_account_username, service_account_secret]):
        print("❌ Missing required environment variables")
        return
    
    service = MixpanelService(
        project_id=project_id,
        service_account_username=service_account_username,
        service_account_secret=service_account_secret,
        workspace_id=workspace_id,
    )
    
    # Test 1: Basic heartbeat events
    print("\n1️⃣ Testing basic heartbeat events...")
    try:
        heartbeat_data = service.client.get_segmentation_data(
            event="heartbeat",
            from_date="2025-06-19",
            to_date="2025-06-25",
            segment_property='properties["permission_state"]',
            event_type="unique",
            unit="day"
        )
        
        if heartbeat_data and heartbeat_data.get("data"):
            print(f"   ✅ Found heartbeat data")
            
            # Show permission state breakdown
            permission_states = set()
            total_users = 0
            
            for date_key, date_data in heartbeat_data["data"].items():
                print(f"   📅 {date_key}:")
                if isinstance(date_data, dict):
                    for perm_state, count in date_data.items():
                        permission_states.add(perm_state)
                        total_users += count
                        print(f"      - {perm_state}: {count} users")
            
            print(f"   📊 Total unique permission states: {permission_states}")
            print(f"   👥 Total user events: {total_users}")
        else:
            print("   ❌ No heartbeat data found")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Heartbeat events with permission_state = "All"
    print("\n2️⃣ Testing heartbeat events with permission_state = 'All'...")
    try:
        all_permission_data = service.client.get_segmentation_data(
            event="heartbeat",
            from_date="2025-06-19",
            to_date="2025-06-25",
            segment_property='properties["phia_id"]',
            event_type="unique",
            unit="day",
            where_filter='properties["permission_state"] == "All"'
        )
        
        if all_permission_data and all_permission_data.get("data"):
            print(f"   ✅ Found heartbeat data with permission_state = 'All'")
            
            # Extract unique user IDs
            cohort_user_ids = set()
            for date_key, date_data in all_permission_data["data"].items():
                print(f"   📅 {date_key}:")
                if isinstance(date_data, dict):
                    for user_id, count in date_data.items():
                        if count > 0:
                            cohort_user_ids.add(user_id)
                    print(f"      - {len(date_data)} unique users with heartbeat events")
            
            print(f"   👥 Total unique users in cohort: {len(cohort_user_ids)}")
            print(f"   🔍 Sample user IDs: {list(cohort_user_ids)[:5]}")
        else:
            print("   ❌ No heartbeat data with permission_state = 'All' found")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Try the new retention method
    print("\n3️⃣ Testing new retention method...")
    try:
        retention_metrics = service.get_weekly_retention_metrics(
            "2025-06-19", "2025-06-25", analysis_weeks=4
        )
        
        print(f"   ✅ Retention method completed")
        print(f"   👥 Cohort size: {retention_metrics.initial_cohort_size}")
        print(f"   📊 Weekly retention rates: {len(retention_metrics.weekly_retention_rates)} weeks")
        
        for week_key, week_data in retention_metrics.weekly_retention_rates.items():
            print(f"      - {week_key}: {week_data.get('retention_rate', 0)}% ({week_data.get('active_users', 0)} users)")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_heartbeat_cohort()
