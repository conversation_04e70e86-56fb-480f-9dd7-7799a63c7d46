#!/usr/bin/env python3
"""
Debug script to test cohort retention API calls
"""

import os
import sys
import json
from datetime import datetime

# Load environment variables (optional)
try:
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    print("⚠️ python-dotenv not installed, using system environment variables")

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dags.dependencies.mixpanel.mixpanel_service import MixpanelService


def test_user_profiles_api():
    """Test different user profile queries to debug cohort retention"""

    print("🔍 Testing Mixpanel User Profiles API")
    print("=" * 60)

    # Initialize service with environment variables
    project_id = os.getenv("MIXPANEL_PROJECT_ID")
    service_account_username = os.getenv("MIXPANEL_SERVICE_USERNAME")
    service_account_secret = os.getenv("MIXPANEL_SERVICE_SECRET")
    workspace_id = os.getenv("MIXPANEL_WORKSPACE_ID")

    if not all([project_id, service_account_username, service_account_secret]):
        print("❌ Missing required environment variables:")
        print(f"   MIXPANEL_PROJECT_ID: {'✅' if project_id else '❌'}")
        print(
            f"   MIXPANEL_SERVICE_USERNAME: {'✅' if service_account_username else '❌'}"
        )
        print(f"   MIXPANEL_SERVICE_SECRET: {'✅' if service_account_secret else '❌'}")
        print(f"   MIXPANEL_WORKSPACE_ID: {'✅' if workspace_id else '❌'}")
        return

    service = MixpanelService(
        project_id=project_id,
        service_account_username=service_account_username,
        service_account_secret=service_account_secret,
        workspace_id=workspace_id,
    )

    # Test 1: Basic user profiles (no filter)
    print("\n1️⃣ Testing basic user profiles (first 5 users)...")
    try:
        basic_users = service.client.get_user_profiles(
            output_properties=["$first_seen", "$last_seen", "permission_state"], page=0
        )

        if basic_users and basic_users.get("results"):
            print(f"   ✅ Found {len(basic_users['results'])} users")

            # Show sample user data
            for i, user in enumerate(basic_users["results"][:3]):
                print(f"   👤 User {i+1}:")
                print(f"      - ID: {user.get('$distinct_id', 'N/A')}")
                print(
                    f"      - First Seen: {user.get('$properties', {}).get('$first_seen', 'N/A')}"
                )
                print(
                    f"      - Permission State: {user.get('$properties', {}).get('permission_state', 'N/A')}"
                )
        else:
            print("   ❌ No users found or API failed")
            print(f"   Response: {basic_users}")

    except Exception as e:
        print(f"   ❌ Error: {e}")

    # Test 2: Users with permission_state
    print("\n2️⃣ Testing users with permission_state property...")
    try:
        permission_users = service.client.get_user_profiles(
            where_filter='properties["permission_state"] != ""',
            output_properties=["$first_seen", "$last_seen", "permission_state"],
            page=0,
        )

        if permission_users and permission_users.get("results"):
            print(
                f"   ✅ Found {len(permission_users['results'])} users with permission_state"
            )

            # Show permission state values
            permission_values = {}
            for user in permission_users["results"][:10]:
                perm_state = user.get("$properties", {}).get("permission_state", "N/A")
                permission_values[perm_state] = permission_values.get(perm_state, 0) + 1

            print("   📊 Permission state breakdown:")
            for state, count in permission_values.items():
                print(f"      - {state}: {count} users")
        else:
            print("   ❌ No users with permission_state found")

    except Exception as e:
        print(f"   ❌ Error: {e}")

    # Test 3: Users with permission_state = "All"
    print("\n3️⃣ Testing users with permission_state = 'All'...")
    try:
        all_permission_users = service.client.get_user_profiles(
            where_filter='properties["permission_state"] == "All"',
            output_properties=["$first_seen", "$last_seen", "permission_state"],
            page=0,
        )

        if all_permission_users and all_permission_users.get("results"):
            print(
                f"   ✅ Found {len(all_permission_users['results'])} users with permission_state = 'All'"
            )

            # Show first seen dates
            first_seen_dates = []
            for user in all_permission_users["results"][:10]:
                first_seen = user.get("$properties", {}).get("$first_seen", "N/A")
                if first_seen != "N/A":
                    first_seen_dates.append(first_seen)

            print("   📅 Sample first seen dates:")
            for date in first_seen_dates[:5]:
                print(f"      - {date}")

        else:
            print("   ❌ No users with permission_state = 'All' found")

    except Exception as e:
        print(f"   ❌ Error: {e}")

    # Test 4: Users in June 2025 date range
    print("\n4️⃣ Testing users in June 2025 date range...")
    try:
        june_users = service.client.get_user_profiles(
            where_filter='properties["$first_seen"] >= "2025-06-19" and properties["$first_seen"] <= "2025-06-25"',
            output_properties=["$first_seen", "$last_seen", "permission_state"],
            page=0,
        )

        if june_users and june_users.get("results"):
            print(f"   ✅ Found {len(june_users['results'])} users in June 2025 range")

            # Show permission states for June users
            june_permissions = {}
            for user in june_users["results"]:
                perm_state = user.get("$properties", {}).get("permission_state", "N/A")
                june_permissions[perm_state] = june_permissions.get(perm_state, 0) + 1

            print("   📊 June 2025 users permission breakdown:")
            for state, count in june_permissions.items():
                print(f"      - {state}: {count} users")
        else:
            print("   ❌ No users found in June 2025 date range")

    except Exception as e:
        print(f"   ❌ Error: {e}")

    # Test 5: The exact cohort query
    print("\n5️⃣ Testing exact cohort query...")
    try:
        cohort_users = service.client.get_user_profiles(
            where_filter='properties["$first_seen"] >= "2025-06-19" and properties["$first_seen"] <= "2025-06-25" and properties["permission_state"] == "All"',
            output_properties=[
                "$first_seen",
                "$last_seen",
                "phia_id",
                "permission_state",
            ],
            page=0,
        )

        if cohort_users and cohort_users.get("results"):
            print(f"   ✅ Found {len(cohort_users['results'])} users in cohort!")

            for i, user in enumerate(cohort_users["results"][:3]):
                print(f"   👤 Cohort User {i+1}:")
                print(f"      - ID: {user.get('$distinct_id', 'N/A')}")
                print(
                    f"      - First Seen: {user.get('$properties', {}).get('$first_seen', 'N/A')}"
                )
                print(
                    f"      - Permission State: {user.get('$properties', {}).get('permission_state', 'N/A')}"
                )
        else:
            print("   ❌ No users found with exact cohort query")
            print(f"   Response: {cohort_users}")

    except Exception as e:
        print(f"   ❌ Error: {e}")


if __name__ == "__main__":
    test_user_profiles_api()
