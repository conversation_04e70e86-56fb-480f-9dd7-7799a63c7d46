"""create pipeline tables

Revision ID: c0ce2086ea3f
Revises: 
Create Date: 2024-06-19 15:01:08.581216

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlalchemy.dialects.postgresql as psql
import uuid

# revision identifiers, used by Alembic.
revision: str = "c0ce2086ea3f"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    # Create the PartnerProductSync table
    op.create_table(
        "PartnerProductSync",
        sa.Column(
            "id",
            psql.UUID(as_uuid=True),
            primary_key=True,
            default=uuid.uuid4,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("partner", sa.String(length=255), nullable=False),
        sa.Column(
            "created_at", sa.DateTime, nullable=False, server_default=sa.func.now()
        ),
        sa.Column(
            "updated_at",
            sa.DateTime,
            nullable=False,
            server_default=sa.func.now(),
            onupdate=sa.func.now(),
        ),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("metadata", sa.JSON, nullable=True),
    )

    # Create the PartnerProductFileSync table
    op.create_table(
        "PartnerProductFileSync",
        sa.Column(
            "id",
            psql.UUID(as_uuid=True),
            primary_key=True,
            default=uuid.uuid4,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column(
            "created_at", sa.DateTime, nullable=False, server_default=sa.func.now()
        ),
        sa.Column(
            "updated_at",
            sa.DateTime,
            nullable=False,
            server_default=sa.func.now(),
            onupdate=sa.func.now(),
        ),
        sa.Column("file_name", sa.String(length=255), nullable=False),
        sa.Column("partner", sa.String(length=255), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("metadata", sa.JSON, nullable=True),
        sa.Column(
            "partner_product_sync_id",
            psql.UUID(as_uuid=True),
            sa.ForeignKey("PartnerProductSync.id"),
            nullable=False,
        ),
    )


def downgrade() -> None:
    op.drop_table("PartnerProductSync")
    op.drop_table("PartnerProductFileSync")
