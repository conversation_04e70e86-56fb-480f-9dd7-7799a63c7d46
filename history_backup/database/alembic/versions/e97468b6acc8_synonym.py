"""synonym

Revision ID: e97468b6acc8
Revises: c0ce2086ea3f
Create Date: 2024-07-02 19:30:22.197740

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision: str = "e97468b6acc8"
down_revision: Union[str, None] = "c0ce2086ea3f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create SynonymSet table
    op.create_table(
        "SynonymSet",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("display_name", sa.String, nullable=True),
        sa.Column("last_modified_by", sa.String, nullable=True),
        sa.Column("created_at", sa.DateTime, default=sa.func.now()),
        sa.Column("updated_at", sa.DateTime, default=sa.func.now()),
    )

    # Create SynonymRule table
    op.create_table(
        "SynonymRule",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("display_name", sa.String, nullable=True),
        sa.Column("value", sa.String),
        sa.Column(
            "synoynm_set_id",
            UUID(as_uuid=True),
            sa.ForeignKey("SynonymSet.id"),
            nullable=True,
        ),
        sa.Column("last_modified_by", sa.String, nullable=True),
        sa.Column("created_at", sa.DateTime, default=sa.func.now()),
        sa.Column("updated_at", sa.DateTime, default=sa.func.now()),
    )


def downgrade() -> None:
    # Drop SynonymRule table
    op.drop_table("SynonymRule")

    # Drop SynonymSet table
    op.drop_table("SynonymSet")
