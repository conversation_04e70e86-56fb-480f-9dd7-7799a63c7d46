"""batch process table

Revision ID: 08d21970bf7b
Revises: e97468b6acc8
Create Date: 2024-08-28 18:02:02.406506

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision: str = "08d21970bf7b"
down_revision: Union[str, None] = "e97468b6acc8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create BatchProcessedProduct table
    op.create_table(
        "BatchProcessedProduct",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("product_id", sa.String, nullable=True),
        sa.Column("version", sa.String, nullable=True),
        sa.Column("values", sa.String, nullable=True),
        sa.Column("created_at", sa.DateTime, default=sa.func.now()),
        sa.Column("updated_at", sa.DateTime, default=sa.func.now()),
    )


def downgrade() -> None:
    # Drop BatchProcessedProduct table
    op.drop_table("BatchProcessedProduct")
