steps:
- name: 'python:3.8'
  entrypoint: 'sh'
  args:
  - '-c'
  - |
    pip install alembic psycopg2-binary
    cd database
    export DATABASE_URL=$${DATABASE_URL_MIGRATION_STRING}
    alembic upgrade head
  secretEnv: ["DATABASE_URL_MIGRATION_STRING"]
options:
  logging: CLOUD_LOGGING_ONLY
  substitutionOption: ALLOW_LOOSE
availableSecrets:
  secretManager:
    - versionName: projects/${PROJECT_ID}/secrets/data-pipeline-db-connection-migrations-string/versions/latest
      env: "DATABASE_URL_MIGRATION_STRING"