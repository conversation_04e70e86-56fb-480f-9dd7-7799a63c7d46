
### Local DB setup
To run the schema migrations locally, you should first set up a local db instance. The
easiest way to do that is run:

```shell
docker-compose -p data-pipeline-dev-db -f docker-compose.dev.yml up -d
```

Make sure your `DATABASE_URL` environment variable is set correctly:

```shell
export DATABASE_URL=postgresql://chandler:bing@localhost:20433/data_pipeline_dev
```

To create a new schema migration:

```shell
alembic revision -m "{revision description here}"
```

Then to apply your migration
```shell
alembic upgrade head
```
