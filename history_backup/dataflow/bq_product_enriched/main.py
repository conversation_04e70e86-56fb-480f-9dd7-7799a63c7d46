from datetime import datetime

import apache_beam as beam
from apache_beam.options.pipeline_options import PipelineOptions
from google.cloud import storage
import json


def read_json_from_gcs(bucket_name, file_path):
    # Initialize a client
    client = storage.Client()

    # Get the bucket
    bucket = client.get_bucket(bucket_name)

    # Get the blob (file) from the bucket
    blob = bucket.blob(file_path)

    # Download the contents of the blob as a string
    json_data = blob.download_as_string()

    # Parse the string as JSON
    data = json.loads(json_data)

    return data


def parse_categories():
    # we use the categories.json defined in https://github.com/Shopify/product-taxonomy
    # and store the file in out GCP
    data = read_json_from_gcs("phia-shopify-data", "categories.json")
    # category level starts from 0
    # assuming all the names are unique
    id_name_map = {}
    # all the direct children of an id
    id_children_map = {}
    # all the accestors till the root of an id
    id_ancestor_map = {}
    level_id_map = {}
    # only focus on level 2 and 3 categories on index
    categories = []
    # record their ids too
    categories_ids = []
    # the level 0 categories we will focus on right now
    focused_categories = {
        "Apparel & Accessories",
        "Luggage & Bags",
        "Furniture",
        "Home & Garden",
        "Electronics",
        "Toys & Games",
    }
    for vertical in data["verticals"]:
        if vertical["name"] not in focused_categories:
            continue
        for category in vertical["categories"]:
            id_name_map[category["id"]] = category["name"]
            if category["level"] not in level_id_map:
                level_id_map[category["level"]] = []
            level_id_map[category["level"]].append(category["id"])
            id_children_map[category["id"]] = [
                child["id"] for child in category["children"]
            ]
            id_ancestor_map[category["id"]] = [
                ancestor["id"] for ancestor in category["ancestors"]
            ]

    ignored_category_patterns = [
        "boy",
        "women",
        "girl",
        "costume",
        "clothing tops",
        "baby &",
        "outerwear",
        "pants",
        "uniforms",
        "swimwear",
    ]
    # these levels we will include their children instead of themselves
    # Clothing Tops, Outerwear, Pants, Swimwear
    included_level2 = [
        "gid://shopify/TaxonomyCategory/aa-1-13",
        "gid://shopify/TaxonomyCategory/aa-1-10",
        "gid://shopify/TaxonomyCategory/aa-1-12",
        "gid://shopify/TaxonomyCategory/aa-1-20",
    ]
    for cat in level_id_map[2]:
        cat_name = id_name_map[cat]
        exist_check = [
            ele for ele in ignored_category_patterns if ele in cat_name.lower()
        ]
        if len(exist_check) == 0:
            categories.append(cat_name)
            categories_ids.append(cat)
    # explicitly add some level 4 categories
    for cat_id in included_level2:
        categories.extend([id_name_map[child] for child in id_children_map[cat_id]])
        categories_ids.extend(id_children_map[cat_id])
    return id_name_map, id_ancestor_map, categories, categories_ids


class CategoryPrediction(beam.DoFn):
    def __init__(self):
        # Initialize the model in the setup method to ensure it's only loaded once per worker
        self.model = None
        self.device = "cuda"
        (
            self.category_id_name_map,
            self.category_id_ancestor_map,
            self.categories,
            self.categories_ids,
        ) = parse_categories()
        self.category_embeddings = None

    def setup(self):
        import torch

        from sentence_transformers import SentenceTransformer

        # This method is called once per worker, not per element
        self.model = SentenceTransformer(
            "Alibaba-NLP/gte-multilingual-base", trust_remote_code=True
        )
        self.model.to("cuda")

        # Calculate embeddings for the categories
        self.category_embeddings = self.model.encode(
            self.categories, convert_to_tensor=True, device=self.device
        )

    def get_category_lineage(self, category_id):
        """
        given a category id, return all it's ancestors together with the current category in string delimited by comma
        """
        cur_category_name = self.category_id_name_map[category_id]
        if category_id not in self.category_id_ancestor_map:
            return cur_category_name
        lineage = [
            self.category_id_name_map[ancestor]
            for ancestor in self.category_id_ancestor_map[category_id]
        ]
        lineage.reverse()
        lineage.append(cur_category_name)
        return ",".join(lineage)

    def process(self, elements):
        import torch
        from sentence_transformers import util

        # Extract attributes from the batch of elements
        attributes = [element["name"] for element in elements]

        # Calculate embeddings for the batch
        embeddings = self.model.encode(
            attributes, convert_to_tensor=True, device=self.device
        )

        # Compute similarity scores for all embeddings at once using matrix multiplication
        similarity_scores = util.pytorch_cos_sim(embeddings, self.category_embeddings)

        # Find the index of the highest similarity score for each embedding
        max_indices = torch.argmax(similarity_scores, dim=1)

        # Yield each element with the predicted category individually
        for i, element in enumerate(elements):
            # Get the corresponding category
            element["category_names"] = self.get_category_lineage(
                self.categories_ids[max_indices[i].item()]
            )
            # Yield each element separately
            yield element


class PrintElement(beam.DoFn):
    def process(self, element):
        print(element)
        yield element


def pipeline():
    options = PipelineOptions(
        project="phia-prod-416420",
        runner="DataflowRunner",
        region="us-central1",
        sdk_container_image="us-central1-docker.pkg.dev/phia-prod-416420/dataflow/bq-product-enriched:latest",
        temp_location="gs://bq-product-enriched-dataflow/temp",
        staging_location="gs://bq-product-enriched-dataflow/staging",
        job_name=f"bq-product-enriched-v2-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
        autoscaling_algorithm="THROUGHPUT_BASED",
        worker_harness_container_image="us-central1-docker.pkg.dev/phia-prod-416420/dataflow/bq-product-enriched:latest",
        # requirements_file="requirements.txt",
        # GPU configuration
        max_num_workers=6,
        machine_type="n1-standard-4",
        experiments=["use_runner_v2", "enable_dynamic_split"],
        dataflow_service_options=[
            "worker_accelerator=type:nvidia-tesla-v100;count:1;install-nvidia-driver"
        ],
        disk_size_gb=100,
    )

    query = """
        SELECT p.id,
            CASE
            WHEN p.secondhandRetailerId = 'the_real_real' THEN p.description
            ELSE p.name
        END AS name FROM `phia-prod-416420.datastream.public_Product` p;
        """

    schema = {
        "fields": [
            {"name": "id", "type": "STRING", "mode": "REQUIRED"},
            {"name": "name", "type": "STRING", "mode": "NULLABLE"},
            {"name": "category_names", "type": "STRING", "mode": "NULLABLE"},
        ]
    }

    p = beam.Pipeline(options=options)
    (
        p
        | "ReadFromBigQuery"
        >> beam.io.ReadFromBigQuery(
            query=query, use_standard_sql=True, method="DIRECT_READ"
        )
        | "BatchElements"
        >> beam.BatchElements(min_batch_size=1000, max_batch_size=5000)
        | "CategoryPrediction" >> beam.ParDo(CategoryPrediction())
        | "WriteToBigQuery"
        >> beam.io.WriteToBigQuery(
            table="phia-prod-416420:datastream.public_Product_Enriched",
            schema=schema,
            create_disposition=beam.io.BigQueryDisposition.CREATE_IF_NEEDED,
            write_disposition=beam.io.BigQueryDisposition.WRITE_APPEND,
        )
    )

    return p


if __name__ == "__main__":
    pipeline().run()
