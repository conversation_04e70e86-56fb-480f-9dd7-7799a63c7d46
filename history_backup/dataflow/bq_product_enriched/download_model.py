#
# model_name = "Alibaba-NLP/gte-multilingual-base"
#
# model = AutoModel.from_pretrained(model_name)
# tokenizer = AutoTokenizer.from_pretrained(model_name)
#
# model.save_pretrained("./model")
# tokenizer.save_pretrained("./tokenizer")

#
# from sentence_transformers import SentenceTransformer
#
# model = SentenceTransformer("Alibaba-NLP/gte-multilingual-base", trust_remote_code=True)
#
# model.save("./model")
