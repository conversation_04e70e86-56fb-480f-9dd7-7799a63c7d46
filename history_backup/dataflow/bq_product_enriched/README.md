https://cloud.google.com/dataflow/docs/guides/templates/using-flex-templates

To run a Dataflow job we first need to build a Flex Template. A Flex Template consists of the following components:
- A Docker container image that packages our pipeline code.
- A template specification file. This file is a JSON document that contains the location of the container image plus metadata about the template, such as pipeline parameters.

In order to push to our artifact repository you may first need to run: 
```shell
gcloud auth configure-docker us-central1-docker.pkg.dev
```

We've been experimenting with two ways to create the docker image for use with a flex template.
- Create a custom docker image and then build a flex template using that custom Docker image:
- Build the flex template using the standard docker image provided by the flex template




### Create a custom docker image 

Python 3.10.14

To build a custom docker image we run:

```shell
gcloud builds submit .  --tag us-central1-docker.pkg.dev/phia-prod-416420/dataflow/bq-product-enriched --project phia-prod-416420
```

### Run the dataflow job

The docker image we build uses python 3.10 so you should make sure that this version of python is installed. 
We can run the dataflow job directly from the command line using:

```shell
python main.py
```
 
### Build a flex template using a custom Docker image (outdated):

Using a flex-template seemed to be the recommend approach on GCP documentation but this setup never worked.
Then to build the flex template using the custom image, you need to run:

```shell
gcloud dataflow flex-template build gs://bq-product-enriched-dataflow/bq-product-enriched-template.json \
  --image us-central1-docker.pkg.dev/phia-prod-416420/dataflow/bq-product-enriched:latest \
  --sdk-language "PYTHON" \
  --staging-location gs://bq-product-enriched-dataflow/staging \
  --temp-location gs://bq-product-enriched-dataflow/temp \
  --project phia-prod-416420 \
  --worker-region us-central1 \
  --worker-machine-type n1-standard-4
```

### Build the flex template using the standard docker image provided by the flex template
Alternatively, if you would prefer to use the standard flex-template docker image, you can just run:

```shell
gcloud dataflow flex-template build gs://bq-product-enriched-dataflow/bq-product-enriched-template.json \
 --image-gcr-path "us-central1-docker.pkg.dev/phia-prod-416420/dataflow/bq-product-enriched:latest" \
 --sdk-language "PYTHON" \
 --flex-template-base-image "PYTHON3" \
 --py-path "." \
 --env "FLEX_TEMPLATE_PYTHON_PY_FILE=main.py" \
 --env "FLEX_TEMPLATE_PYTHON_REQUIREMENTS_FILE=requirements.txt"
```

Regardless of which option was used to create the docker image and flex template, we can start the dataflow job using:

```shell
gcloud dataflow flex-template run "bq-product-enriched-$(date +%Y%m%d-%H%M%S)" \
 --template-file-gcs-location "gs://bq-product-enriched-dataflow/bq-product-enriched-template.json" \
 --parameters output="gs://bq-product-enriched-dataflow/output-" \
 --parameters sdk_container_image="us-central1-docker.pkg.dev/phia-prod-416420/dataflow/bq-product-enriched:latest" \
 --region "us-central1"
```

You can see the status of the dataflow job [here](https://console.cloud.google.com/dataflow/jobs?project=phia-prod-416420). 