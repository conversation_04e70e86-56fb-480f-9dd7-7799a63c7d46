FROM pytorch/pytorch:2.0.1-cuda11.7-cudnn8-runtime

WORKDIR /pipeline

COPY *.py ./

# Install the pipeline requirements and check that there are no conflicts.
# Since the image already has all the dependencies installed,
# there's no need to run with the --requirements_file option.
RUN pip install --no-cache-dir apache-beam[gcp]==2.58.0 google-cloud-bigquery google-cloud-storage sentence-transformers torch

# Set the entrypoint to Apache Beam SDK worker launcher.
COPY --from=apache/beam_python3.10_sdk:2.58.0 /opt/apache/beam /opt/apache/beam
ENTRYPOINT [ "/opt/apache/beam/boot" ]