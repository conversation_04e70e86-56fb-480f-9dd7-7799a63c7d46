import logging
import multiprocessing
import os
import sys
import time

import google.cloud.logging
from psycopg2.pool import ThreadedConnectionPool
from concurrent.futures import ThreadPoolExecutor, as_completed

# Database connection parameters
DATABASE_CONFIG = {
    "host": "***********",
    "port": "5432",
    "dbname": "postgres",
    "user": "ingest_data_user",
    "password": "",
}

RETAILERS = ["ebay", "poshmark"]

NUM_WORKERS = multiprocessing.cpu_count() * 2
BATCH_SIZE = 100000
min_conn = 5
max_conn = NUM_WORKERS * 2
conn_pool = ThreadedConnectionPool(minconn=5, maxconn=max_conn, **DATABASE_CONFIG)


def delete_records_in_batches(partner):
    while True:
        product_ids = fetch_product_ids(partner)
        embedding_ids = fetch_embedding_ids(product_ids)

        if not product_ids:
            logging.info(f"No more products to delete for {partner}")
            break
        delete_related_records(product_ids, partner)
        delete_product_embeddings(embedding_ids)
        delete_products(product_ids, partner)
        delete_embeddings(product_ids, partner)

    logging.info(f"Deletion process complete for {partner}")


def fetch_product_ids(partner):
    conn = conn_pool.getconn()
    try:
        with conn.cursor() as cur:
            start_time = time.time()
            cur.execute(
                """
                SELECT id FROM public."Product"
                WHERE "secondhandRetailerId" = %s
                AND "lastSeenAt" < %s
                LIMIT %s
            """,
                (partner, "2024-08-05 00:00:00", BATCH_SIZE),
            )

            product_ids = cur.fetchall()
            logging.info(
                f"Fetched {len(product_ids)} products in {time.time() - start_time:.2f} seconds for {partner}"
            )
            return [id_tuple[0] for id_tuple in product_ids]
    except Exception as e:
        print(f"An error occurred while fetching product IDs: {e}")
        return []
    finally:
        conn_pool.putconn(conn)


def fetch_embedding_ids(product_ids):
    conn = conn_pool.getconn()
    try:
        with conn.cursor() as cur:
            start_time = time.time()
            cur.execute(
                """
                SELECT id FROM public."Embedding"
                WHERE "productId" IN %s
            """,
                (tuple(product_ids),),
            )

            embedding_ids = cur.fetchall()
            logging.info(
                f"Fetched {len(embedding_ids)} embeddings for {len(product_ids)} in {time.time() - start_time:.2f} seconds"
            )
            return embedding_ids
    except Exception as e:
        logging.error(f"An error occurred while fetching embeddings: {e}")
        conn.rollback()
    finally:
        conn_pool.putconn(conn)


def delete_products(product_ids, partner):
    conn = conn_pool.getconn()
    try:
        # Connect to the database
        with conn.cursor() as cur:
            if product_ids:
                start_time = time.time()
                res = cur.execute(
                    """
                    DELETE FROM public."Product"
                    WHERE id IN %s
                """,
                    (tuple(product_ids),),
                )

                conn.commit()
                logging.info(
                    f"Deleted {len(product_ids)} products in {time.time() - start_time} seconds for {partner}"
                )

    except Exception as e:
        logging.error(f"An error occurred while deleting products: {e}")
        conn.rollback()
    finally:
        conn_pool.putconn(conn)


def delete_product_embeddings(embedding_ids):
    conn = conn_pool.getconn()
    try:
        # Connect to the database
        with conn.cursor() as cur:
            if embedding_ids:
                start_time = time.time()
                res = cur.execute(
                    """
                    DELETE FROM public."product_embedding_table"
                    WHERE "embeddingId" IN %s
                """,
                    (tuple(embedding_ids),),
                )

                conn.commit()
                logging.info(
                    f"Deleted {len(embedding_ids)} embeddings from product_embedding_table in {time.time() - start_time} seconds"
                )

    except Exception as e:
        logging.error(
            f"An error occurred while deleting product_embedding_table entries: {e}"
        )
        conn.rollback()
    finally:
        conn_pool.putconn(conn)


def delete_embeddings(product_ids, partner):
    conn = conn_pool.getconn()
    try:
        # Connect to the database
        with conn.cursor() as cur:
            if product_ids:
                start_time = time.time()
                cur.execute(
                    """
                    DELETE FROM public."Embedding"
                    WHERE "productId" IN %s
                """,
                    (tuple(product_ids),),
                )

                conn.commit()
                logging.info(
                    f"Deleted embeddings in {time.time() - start_time} seconds for {partner}"
                )

    except Exception as e:
        logging.error(f"An error occurred while deleting embeddings: {e}")
        conn.rollback()
    finally:
        conn_pool.putconn(conn)


def delete_related_records(product_ids, partner):
    conn = conn_pool.getconn()
    try:
        with conn.cursor() as cur:
            if product_ids:
                # Deleting from AffiliateUrl
                cur.execute(
                    """
                    DELETE FROM public."AffiliateUrl"
                    WHERE "productId" IN %s
                    """,
                    (tuple(product_ids),),
                )

                # Deleting from UserSave
                cur.execute(
                    """
                    DELETE FROM public."UserSave"
                    WHERE "productId" IN %s
                    """,
                    (tuple(product_ids),),
                )

                conn.commit()
                logging.info(
                    f"Deleted related records for {len(product_ids)} products for {partner}"
                )

    except Exception as e:
        logging.error(f"An error occurred while deleting related records: {e}")
        conn.rollback()
    finally:
        conn_pool.putconn(conn)


def setup_logging():
    if os.getenv("ENVIRONMENT") != "local":
        logging.getLogger().setLevel(logging.INFO)
        client = google.cloud.logging.Client()
        client.setup_logging()
    else:
        logging.basicConfig(stream=sys.stdout, level=logging.INFO)


def main():
    with ThreadPoolExecutor(max_workers=len(RETAILERS)) as executor:
        futures = [
            executor.submit(delete_records_in_batches, retailer)
            for retailer in RETAILERS
        ]
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                logging.error(f"Error in thread: {e}")


if __name__ == "__main__":
    logging.basicConfig(stream=sys.stdout, level=logging.INFO)
    setup_logging()
    logging.info(f"Starting hard deletion process")
    main()
