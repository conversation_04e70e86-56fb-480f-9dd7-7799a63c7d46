import json

from google.cloud import secretmanager
from python_graphql_client import GraphqlClient


def create_attribute_from_name_query():
    return """
            mutation CreateAttribute($attribute: AttributeInput!) {
              createAttribute(attribute: $attribute) {
                id
                displayName
                source
              }
            }
    """


def create_attributes():
    client = secretmanager.SecretManagerServiceClient()
    x_phia_token_name = f"projects/phia-prod-416420/secrets/internal-api-key/versions/1"
    x_phia_token_resp = client.access_secret_version(
        request={"name": x_phia_token_name}
    )
    x_phia_token = x_phia_token_resp.payload.data.decode("UTF-8")

    client = GraphqlClient(
        endpoint="https://phia-external-gateway-7rubhb98.uc.gateway.dev/internal/graphql",
        headers={"x-phia-token": x_phia_token},
    )

    with open("../../transform_chunk/local/unique_attributes.json", "r") as f:
        unique_attributes = json.load(f)
        for unique_attribute in unique_attributes:
            name = unique_attribute.lower()
            print(name)
            create_attribute_variables = {
                "attribute": {"displayName": name, "source": "PIPELINE"}
            }
            try:
                create_attribute_resp = client.execute(
                    query=create_attribute_from_name_query(),
                    variables=create_attribute_variables,
                )
                print(f"Created attribute response: {create_attribute_resp}")
            except Exception as e:
                print(f"Failed to create attribute: {e}")


create_attributes()
