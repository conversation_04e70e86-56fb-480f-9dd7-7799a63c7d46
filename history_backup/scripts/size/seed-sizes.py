"""
model Size {
  id           String            @id @default(cuid())
  displayName  String            @db.<PERSON>ar<PERSON>har(256)
  category     SizeCategory
  value        String
  numericValue Decimal?
  gender       Gender
  users        UserPreferences[] @relation("UserPreferencesSizes")
  products     Product[]         @relation("ProductSize")

  @@unique([category, value])
}


class CategoryBroad(Enum):
    PANTS = "pants"
    CLOTHING = "clothing"
    SHOE = "shoe"
    RINGS = "ring"
    ACCESSORIES = "accessories"
"""

from google.cloud import secretmanager

from python_graphql_client import GraphqlClient

mens_shoes = {
    "4",
    "4",
    "4.5",
    "5",
    "5.5",
    "6",
    "6.5",
    "7",
    "7.5",
    "8",
    "8.5",
    "9",
    "9.5",
    "10",
    "10.5",
    "11",
    "11.5",
    "12",
    "12.5",
    "13",
    "13.5",
    "14",
    "14.5",
    "15",
}

womens_shoes = {
    "3",
    "3.5",
    "4",
    "4.5",
    "5",
    "5.5",
    "6",
    "6.5",
    "7",
    "7.5",
    "8",
    "8.5",
    "9",
    "9.5",
    "10",
    "10.5",
    "11",
    "11.5",
    "12",
    "12",
}

womens_pants_sizes = {
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "19",
    "20",
    "21",
}

us_pants_sizes = {
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31",
    "32",
    "33",
    "34",
    "35",
    "36",
    "37",
    "38",
}

us_womens_clothing = {
    "00",
    "0",
    "1",
    "2",
    "4",
    "6",
    "8",
    "10",
    "12",
    "14",
    "14",
    "18",
    "5xl",
    "6xl",
    "7xl",
    "8xl",
    "9xl",
}

us_mens_clothing = {
    "32",
    "34",
    "36",
    "38",
    "40",
    "42",
    "44",
    "46",
    "46",
    "48",
}

ring_us = {
    3,
    3.5,
    4,
    4.5,
    5,
    5.5,
    6,
    6.5,
    7,
    7.5,
    8,
    8.5,
    9,
    9.5,
    10,
    10.5,
    11,
    11.5,
    12,
    12.5,
    13,
}


def create_size_json():
    shoes_men = []
    for size in mens_shoes:
        try:
            numeric_size = float(size)
        except ValueError:
            numeric_size = None
        shoes_men.append(
            {
                "displayName": "Men Shoe Size " + str(size),
                "category": "SHOE",
                "value": str(size),
                "numericValue": numeric_size,
                "gender": "MEN",
            }
        )

    shoes_women = []
    for size in womens_shoes:
        try:
            numeric_size = float(size)
        except ValueError:
            numeric_size = None
        shoes_women.append(
            {
                "displayName": "Women Shoe Size " + str(size),
                "category": "SHOE",
                "value": str(size),
                "numericValue": numeric_size,
                "gender": "WOMEN",
            }
        )

    pants_men = []
    for size in us_pants_sizes:
        try:
            numeric_size = float(size)
        except ValueError:
            numeric_size = None
        pants_men.append(
            {
                "displayName": "Men Pants Size " + str(size),
                "category": "PANTS",
                "value": str(size),
                "numericValue": numeric_size,
                "gender": "MEN",
            }
        )

    pants_women = []
    # for size in us_pants_sizes:
    #     try:
    #         numeric_size = float(size)
    #     except ValueError:
    #         numeric_size = None
    #     pants_women.append(
    #         {
    #             "displayName": "Women Pants Size " + str(size),
    #             "category": "PANTS",
    #             "value": str(size),
    #             "numericValue": numeric_size,
    #             "gender": "WOMEN",
    #         }
    #     )

    for size in womens_pants_sizes:
        try:
            numeric_size = float(size)
        except ValueError:
            numeric_size = None
        pants_women.append(
            {
                "displayName": "Size " + str(size),
                "category": "PANTS",
                "value": str(size),
                "numericValue": numeric_size,
                "gender": "WOMEN",
            }
        )

    clothing_men = []
    for size in us_mens_clothing:
        try:
            numeric_size = float(size)
        except ValueError:
            numeric_size = None
        clothing_men.append(
            {
                "displayName": "Men Clothing Size " + str(size),
                "category": "CLOTHING",
                "value": str(size),
                "numericValue": numeric_size,
                "gender": "MEN",
            }
        )

    clothing_women = []
    for size in us_womens_clothing:
        try:
            numeric_size = float(size)
        except ValueError:
            numeric_size = None
        clothing_women.append(
            {
                "displayName": "Women Clothing Size " + str(size),
                "category": "CLOTHING",
                "value": str(size),
                "numericValue": numeric_size,
                "gender": "WOMEN",
            }
        )

    ring_sizes = []
    for size in ring_us:
        try:
            numeric_size = float(size)
        except ValueError:
            numeric_size = None
        ring_sizes.append(
            {
                "displayName": "Ring Size " + str(size),
                "category": "RING",
                "value": str(size),
                "numericValue": numeric_size,
                "gender": "UNISEX",
            }
        )

    sizes = {
        "shoes_men": shoes_men,
        "shoes_women": shoes_women,
        "pants_men": pants_men,
        "pants_women": pants_women,
        "clothing_men": clothing_men,
        "clothing_women": clothing_women,
        "ring_sizes": ring_sizes,
    }

    # Haven't run this yet. Need to think about adding accesseries and one size to the size and size category table for unification
    sizes_one_size = {
        "one_size": [
            {
                "displayName": "One Size",
                "category": "RING",
                "value": "One Size",
                "numericValue": None,
                "gender": "UNISEX",
            },
            {
                "displayName": "One Size",
                "category": "RING",
                "value": "One Size",
                "numericValue": None,
                "gender": "MEN",
            },
            {
                "displayName": "One Size",
                "category": "RING",
                "value": "One Size",
                "numericValue": None,
                "gender": "WOMEN",
            },
            {
                "displayName": "One Size",
                "category": "CLOTHING",
                "value": "One Size",
                "numericValue": None,
                "gender": "MEN",
            },
            {
                "displayName": "One Size",
                "category": "CLOTHING",
                "value": "One Size",
                "numericValue": None,
                "gender": "WOMEN",
            },
            {
                "displayName": "One Size",
                "category": "CLOTHING",
                "value": "One Size",
                "numericValue": None,
                "gender": "UNISEX",
            },
            {
                "displayName": "One Size",
                "category": "PANTS",
                "value": "One Size",
                "numericValue": None,
                "gender": "MEN",
            },
            {
                "displayName": "One Size",
                "category": "PANTS",
                "value": "One Size",
                "numericValue": None,
                "gender": "WOMEN",
            },
            {
                "displayName": "One Size",
                "category": "PANTS",
                "value": "One Size",
                "numericValue": None,
                "gender": "UNISEX",
            },
            {
                "displayName": "One Size",
                "category": "ACCESSORIES",
                "value": "One Size",
                "numericValue": None,
                "gender": "WOMEN",
            },
            {
                "displayName": "One Size",
                "category": "ACCESSORIES",
                "value": "One Size",
                "numericValue": None,
                "gender": "MEN",
            },
            {
                "displayName": "One Size",
                "category": "ACCESSORIES",
                "value": "One Size",
                "numericValue": None,
                "gender": "UNISEX",
            },
        ]
    }

    client = secretmanager.SecretManagerServiceClient()
    x_phia_token_name = f"projects/phia-prod-416420/secrets/internal-api-key/versions/1"
    x_phia_token_resp = client.access_secret_version(
        request={"name": x_phia_token_name}
    )
    x_phia_token = x_phia_token_resp.payload.data.decode("UTF-8")

    client = GraphqlClient(
        endpoint="https://phia-external-gateway-7rubhb98.uc.gateway.dev/internal/graphql",
        headers={"x-phia-token": x_phia_token},
    )

    query = """
        mutation CreateSize($size: SizeInput!) {
          createSize(size: $size) {
            displayName
            category
            value
            numericValue
            gender
          }
        }
    """

    for size in pants_women:
        # for size in size_values:
        try:
            print(
                f"Creating size for {size['gender']} - {size['category']} - {size['displayName']}"
            )
            data = client.execute(query=query, variables={"size": size})
            print(data)
        except Exception as e:
            print(e)


create_size_json()
