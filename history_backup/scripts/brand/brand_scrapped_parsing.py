import json
import os
import re

from scripts.brand.brand_mappings import BRAND_MAPPINGS


def parse_scrapped_brand_lists():
    brands = []
    for filename in os.listdir("./brands_scrapped/"):
        print(filename)
        with open(
            os.path.join(os.getcwd(), f"brands_scrapped/{filename}"),
            "r",
        ) as file:
            data = list(json.load(file))
            brands.extend(data)

    remove_collabs = []
    COLLAB_EXCEPTIONS = ["Gabriel for sach"]
    for b in brands:
        if b in COLLAB_EXCEPTIONS:
            remove_collabs.append(b)
            continue
        if " x " in b.lower():
            remove_collabs.extend(re.split(" x ", b, flags=re.IGNORECASE))
        elif " by " in b.lower():
            remove_collabs.extend(re.split(" by ", b, flags=re.IGNORECASE))
        elif " for " in b.lower():
            remove_collabs.extend(re.split(" for ", b, flags=re.IGNORECASE))
        else:
            remove_collabs.append(b)

    remove_similar_brands = []
    for b in remove_collabs:
        mapped = False
        for brand_name, brand_alternatives in BRAND_MAPPINGS.items():
            if b.lower().strip() in [ba.lower().strip() for ba in brand_alternatives]:
                remove_similar_brands.append(brand_name)
                mapped = True
        if not mapped:
            remove_similar_brands.append(b)

    case_mapping = {}
    for b in remove_similar_brands:
        case_mapping[b.lower().strip()] = b.strip()

    dedupe_brands = sorted([case_mapping[b] for b in list(set(case_mapping.keys()))])
    with open("./brand.json", "w", encoding="utf8") as file:
        json.dump(dedupe_brands, file, indent=2)


parse_scrapped_brand_lists()
