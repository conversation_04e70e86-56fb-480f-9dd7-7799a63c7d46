### Overview

#### Designer/Brand Scrapping

The scripts in this directory demonstrate a naive/brute-force way to collect a list of brands/designers from various
ecommerce sites. Implementing traditional web-crawler methods using headless browser libraries such as puppeteer 
or playwright has proven challenging as many ecommerce sites block these approaches. As an alternative, 
we have simply used the JS below to download a list of brands by directly querying the DOM on TheRealReal's designer
list pages.

In order to add a download method to the browser console, first run the IIFE from `console.save.js` in the browser. 
Then, executing the below JS in the browser console at https://www.therealreal.com/designers

```js
const brandElements = document.querySelectorAll('.designer-directory__designer');
const brandList = Array.from(brandElements).map(el => el.textContent.trim());

console.save(brandList)
```
will download a list of designers as json.


#### Designer/Brand Parsing

A simple approach to combine various designer/brand lists from scrapped files is available in `brand_scrapped_parsing.py`.
Running this python script will output a single `brand.json` file with a minimum amount of cleaning, dedup\ing, etc performed
across the various brand files in the `./brand_scrapped` directory.  