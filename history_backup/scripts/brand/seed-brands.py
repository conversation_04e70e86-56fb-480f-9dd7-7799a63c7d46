import json

from google.cloud import secretmanager
from python_graphql_client import GraphqlClient


def seed_brands():
    client = secretmanager.SecretManagerServiceClient()
    x_phia_token_name = f"projects/phia-prod-416420/secrets/internal-api-key/versions/1"
    x_phia_token_resp = client.access_secret_version(
        request={"name": x_phia_token_name}
    )
    x_phia_token = x_phia_token_resp.payload.data.decode("UTF-8")

    client = GraphqlClient(
        endpoint="https://phia-external-gateway-7rubhb98.uc.gateway.dev/internal/graphql",
        headers={"x-phia-token": x_phia_token},
    )

    query = """
        mutation Mutation($brand: BrandInput!) {
          createBrand(brand: $brand) {
            displayName
          }
        }
    """

    # with open("./brands.json", "r") as f:
    #     brands = json.load(f)
    #     for brand in brands:
    #         data = {
    #             "brand": {"displayName": brand},
    #         }
    #         try:
    #             client.execute(query=query, variables=data)
    #         except Exception as e:
    #             print(e)

    additions = ["Boden"]
    for brand in additions:
        data = {
            "brand": {"displayName": brand},
        }
        try:
            client.execute(query=query, variables=data)
        except Exception as e:
            print(e)


seed_brands()
