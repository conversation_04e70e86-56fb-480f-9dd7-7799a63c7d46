import os
from slack_sdk import WebClient


class SlackService:

    def __init__(self):
        token = os.getenv("SLACK_BOT_TOKEN")
        self.client = WebClient(token=token)

    def send_slack_message(self, contentful_page_id: str, page_slug: str):
        blocks = [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"We've replenished sold out products for *{page_slug}*",
                },
            },
            {"type": "divider"},
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"You can preview the page in <https://phia.retool.com/apps/2bcbabcc-2e3f-11ef-a79f-eb3b01cb5069/Marketplace%20Content/Preview%20Content?id={contentful_page_id}|Retool>",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"You can publish the page from <https://app.contentful.com/spaces/jh5y1lmiedby/entries/{contentful_page_id}?focusedField=slug|Contentful>",
                },
            },
        ]
        # Send a message
        self.client.chat_postMessage(
            channel="marketplace-replenish-alerts",
            blocks=blocks,
            username="Replenishment Update",
        )
