import os

import requests

from replenishment_publisher.api_service import ApiService
from replenishment_publisher.main import run_replenishment

sample_req_body = {
    "sys": {"id": "28STZP3TDIlgxUtX9wNIBh", "publishedVersion": 31},
    "__typename": "CategoryPage",
    "slug": "/shop/shoes",
    "heading": "Women's Shoes",
    "subheading": "Stylish Women's Shoes for Every Occasion",
    "products": {
        "products": [
            # {"productId": "farfetch-20289164-28", "fallbackProductIds": []},
            # {"productId": "farfetch-22079744-20", "fallbackProductIds": []},
            # {"productId": "farfetch-22782658-26", "fallbackProductIds": []},
            # {"productId": "farfetch-23054272-32", "fallbackProductIds": []},
            # {"productId": "farfetch-23054277-30", "fallbackProductIds": []},
            # {"productId": "farfetch-23414416-24", "fallbackProductIds": []},
            {"productId": "rebag-7521622982833", "fallbackProductIds": []},
            # {"productId": "rebag-7563976048817", "fallbackProductIds": []},
            # {"productId": "rebag-7686012141745", "fallbackProductIds": []},
            # {"productId": "rebag-7686012207281", "fallbackProductIds": []},
            # {"productId": "rebag-7707824816305", "fallbackProductIds": []},
            # {"productId": "rebag-7914449698993", "fallbackProductIds": []},
            # {
            #     "productId": "stockx-2528030a-63c4-4ab3-9e0a-6b08360cb872",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-3055fc5d-919c-42d6-ae0a-ffec6eecef0d",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-30a50a88-b0df-435c-aa55-f1f1f65c1433",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-8f597885-3714-4b82-b9c9-ecba0f63b7ed",
            #     "fallbackProductIds": [],
            # },
            # {"productId": "the_real_real-AMINM24680", "fallbackProductIds": []},
            # {"productId": "the_real_real-BOT210965", "fallbackProductIds": []},
            # {"productId": "the_real_real-BOT210975", "fallbackProductIds": []},
            # {"productId": "the_real_real-BOT239913", "fallbackProductIds": []},
            # {"productId": "the_real_real-CHR417494", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-821281", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-848304", "fallbackProductIds": []},
            # {
            #     "productId": "stockx-006cf5ba-763a-4802-b118-c4719e4e6fa0",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-00e1deb4-6238-490a-bac2-15345183989e",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-07c8296f-7640-44e6-b6b6-dc9d4c3691c6",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-082e64e3-284e-43c8-b5a5-623bd7739408",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-091922cf-6dc5-4d4b-8172-6a1f7bfcd051",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-09c8aed1-1669-4fc9-a4b4-a20b9f9e5c27",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0b4cf3c3-d349-435b-8725-82fdde471479",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0ebfaab9-58d8-4467-aae6-14a7f6bbf203",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0d121bca-40c1-4114-8962-2fff759973fe",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0cf3f922-5f82-4f52-b516-8daf54d967cc",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0f70bc22-5edc-464a-a3dd-d313aed6c3b4",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-108e45a2-64ea-4b62-9d99-ab14595f4c7d",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-11e0d303-045d-42ec-930a-5b4ec87a64eb",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-11ea0a33-a2bb-492d-b0cb-2677a0dcbb4b",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-12f82d07-574e-42aa-a685-f3aea155aeb1",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-158cc787-3f41-496c-bfab-42dc32059bd7",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-173df29b-5709-4dc0-8214-6c8dc8d4f006",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-2bb045b7-2074-4b53-a17e-fdc64d854992",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-31b534da-c438-4ef8-8799-6b7e02079f6d",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-35f26483-d1c4-4bda-bbe2-94c9d10bcd7d",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-3687f652-5816-4ecc-93cd-f757fe0c2a6b",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-39177947-1be8-45be-a006-e294675a7f1f",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-3f06096a-2425-44e5-880b-4f486cf16757",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-631f6baf-f220-4530-b043-fcac1c515add",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-632ecaf3-6c02-4d81-bfa8-5914ba6ffa88",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-723db257-f73b-469d-915c-51546adb70b2",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-14a02024-e5f6-49f9-abd2-3f0b224ac3bd",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-2c4ff74b-e574-4789-b469-936d43646e6e",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-36fd57d0-5940-493e-8afd-1fbe29f428c7",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-8348a4c0-118f-4eef-86e0-45988ea9c511",
            #     "fallbackProductIds": [],
            # },
            # {"productId": "the_luxury_closet-1002054", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1001992", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1002738", "fallbackProductIds": []},
            # {"productId": "hewi-191679", "fallbackProductIds": []},
            # {"productId": "rebag-7674558841009", "fallbackProductIds": []},
            # {
            #     "productId": "stockx-025ee3f9-eefb-41fa-bef9-f4385b7888b5",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-5cc711bc-5d40-4a8b-add0-e836509666c1",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-97d8cecc-aef5-420e-a700-66b8c99c3676",
            #     "fallbackProductIds": [],
            # },
            # {"productId": "the_luxury_closet-1000248", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1003204", "fallbackProductIds": []},
            # {
            #     "productId": "stockx-0186c274-d03f-40f0-993a-6d53e94e7a6b",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-018473b7-16b8-4b45-ab0f-918a7559a669",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-08fa4ce0-f67e-4e34-84f7-e7b2c03f74c1",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0bc8a8f0-a85a-4484-ba74-e4e4c2ce3fcb",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0f85bc49-2b02-421d-b65f-56a773ac87ad",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-16301ca8-8928-4c1c-9ab9-bbe83586c368",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-223c0804-78ca-48ee-81da-27d115297d2a",
            #     "fallbackProductIds": [],
            # },
            # {"productId": "hewi-133968", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1000432", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1002982", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-826175", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-900249", "fallbackProductIds": []},
            # {
            #     "productId": "stockx-00317a66-e443-4a95-92e3-4ff29e4df41f",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0379b0a3-39d5-4008-bee0-0d4ac5fc5113",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-03b82c8d-47d2-44de-a493-f555a17fd4a2",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-0804b08b-ec8c-42e0-a722-6a30d0fb448f",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-20a0dd1c-a4f2-4324-a632-c10e2168ed3d",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-2c9a502f-c7b6-405a-8cf9-9eec32b78d77",
            #     "fallbackProductIds": [],
            # },
            # {"productId": "the_luxury_closet-1000438", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1001615", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1003002", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1006269", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-1003050", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-578162", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-638326", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-921182", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-936643", "fallbackProductIds": []},
            # {"productId": "the_luxury_closet-939949", "fallbackProductIds": []},
            # {
            #     "productId": "stockx-17f276e7-ef0e-4e58-a11f-2ff43caab441",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-29047005-b0fb-4361-a862-3d27cbd25a0c",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-2b78ed61-7f4c-4a46-ab80-45db4014be4a",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-3f9d368d-16fd-4b39-99f5-9e24a0d976dd",
            #     "fallbackProductIds": [],
            # },
            # {
            #     "productId": "stockx-44e9fdec-51b5-4867-b917-5f2c3a2cc3f4",
            #     "fallbackProductIds": [],
            # },
        ]
    },
    "metaDescription": "",
}


ivan_sample_req_body = {
    "sys": {"id": "443x2TmJH1nA8DPvyOyIaP", "publishedVersion": None},
    "__typename": "EditorialPage",
    "slug": "/ivans-edit",
    "heroStyle": "Image Block",
    "image": None,
    "bgImage": None,
    "eyebrow": "Phia Edit",
    "heading": "Ivan's Edit",
    "author": "Ivan",
    "date": None,
    "description": "",
    "backgroundColor": None,
    "products": {
        "products": [
            {
                "productId": "hewi-186531",
                "fallbackProductIds": [],
            }
        ]
    },
    "textAlignment": "bottom-left",
    "textColor": None,
    "metaDescription": "",
}

request_body_id = {"contentful_page_id": "22hkfN6GiFrYsVf7y7MZ8q"}


tags = [
    "miu miu",
    "cult gaia",
    "short",
    "long",
    "mini",
    "leather",
    "pleated",
    "sparkly",
    "chanel",
    "black",
    "red",
    "micro",
    "belted",
    "fun",
    "fancy",
    "classy",
    "chic",
    "sporty",
    "silk",
    "missoni",
    "sheer",
    "prada",
    "pencil",
    "work",
    "short skirt",
    "stylish skirt",
    "Women's skirts",
    "skater skirt",
    "long skirt",
    "Trendy skirts",
    "fancy skirt",
    "casual skirt",
    "Stylish skirts",
    "ballet skirt",
    "work skirt",
    "summer skirt",
    "Skirt",
    "skater skirt",
    "spring shirt",
]


def run():
    os.environ["ENVIRONMENT"] = "local"
    # run_replenishment(sample_req_body)
    # run_replenishment(request_body_id)
    print(f"Getting tags for {len(tags)} tags")
    attributes = ApiService().get_attribute_ids(tags)
    print(attributes)


run()


def get_page():
    token = os.getenv("CONTENTFUL_BEARER_TOKEN")
    headers = {
        "Content-Type": "application/vnd.contentful.management.v1+json",
        "Authorization": f"Bearer {token}",
    }
    resp = requests.get(
        f"https://api.contentful.com/spaces/jh5y1lmiedby/environments/master/entries/443x2TmJH1nA8DPvyOyIaP",
        headers=headers,
    )

    print(resp.json())


# get_page()


def send_slack_message():
    import os
    from slack_sdk import WebClient

    message = "*Replenishment Update made*"

    # Set up a WebClient with the Slack OAuth token
    client = WebClient(token="")

    # response = client.users_list()
    # for user in response["members"]:
    #     print(user)
    #     if user["name"] == "username":  # Replace 'username' with the actual username
    #         print(f"User ID for {user['name']} is {user['id']}")

    blocks = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "We've replenished sold out products for *Ivan's Edit*",
            },
        },
        {"type": "divider"},
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "You can preview the page in <https://phia.retool.com/apps/2bcbabcc-2e3f-11ef-a79f-eb3b01cb5069/Marketplace%20Content/Preview%20Content?id=443x2TmJH1nA8DPvyOyIaP|Retool>",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "You can publish the page from <https://app.contentful.com/spaces/jh5y1lmiedby/entries/443x2TmJH1nA8DPvyOyIaP?focusedField=slug|Contentful>",
            },
        },
    ]
    # Send a message
    client.chat_postMessage(
        channel="U071UKDFFB2", blocks=blocks, username="Replenishment Update"
    )


# send_slack_message()
