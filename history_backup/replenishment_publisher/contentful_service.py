import logging
import os

import requests


class ContentfulService:

    def __init__(self):
        self.token = os.getenv("CONTENTFUL_CMA_TOKEN")
        self.contentful_space_id = "jh5y1lmiedby"

    def update_contentful(
        self, request_json: dict, x_contentful_version: str, contentful_page_id: str
    ):
        try:
            headers = {
                "Content-Type": "application/vnd.contentful.management.v1+json",
                "X-Contentful-Version": str(x_contentful_version),
                "Authorization": f"Bearer {self.token}",
            }
            resp = requests.put(
                f"https://api.contentful.com/spaces/{self.contentful_space_id}/environments/master/entries/{contentful_page_id}",
                headers=headers,
                json={"fields": request_json},
            )
            resp.raise_for_status()
        except Exception as e:
            logging.error(f"Failed to get update contentful page {contentful_page_id}")
            logging.exception(e)
            raise e

    def get_page(self, contentful_page_id: str) -> dict:
        try:
            headers = {
                "Content-Type": "application/vnd.contentful.management.v1+json",
                "Authorization": f"Bearer {self.token}",
            }
            resp = requests.get(
                f"https://api.contentful.com/spaces/{self.contentful_space_id}/environments/"
                f"master/entries/{contentful_page_id}",
                headers=headers,
            )
            resp.raise_for_status()
            return resp.json()
        except Exception as e:
            logging.error(f"Failed to get contentful page {contentful_page_id}")
            logging.exception(e)
            raise e
