import logging
import os

from python_graphql_client import GraphqlClient
import google.auth.transport.requests
import google.oauth2.id_token


def get_search_query():
    return """
            query Products($query: ProductSearchInput!, $filters: ProductSearchFilters!, $options: ProductSearchOptions!) {
          products(query: $query, filters: $filters, options: $options) {
            numProducts
            results {
            id
            similarity
            priceUsd
            isAuthenticated
            soldAt
            secondhandRetailer {
              displayName
              id
            }
            primaryColor
            }
            
          }
        }
    """


def get_product_query():
    return """
        query Product($productId: String!) {
          productById(id: $productId) {
            id
            name
            description
            priceUsd
            productUrl
            imgUrl
            additionalImgUrls
            colors
            condition
            gender
            isAuthenticated
            isFreeShipping
            isReturnable
            isVintage
            createdAt
            updatedAt
            lastSeenAt
            soldAt
            brands {
              displayName
            }
            attributes {
              id
            }
          }
        }
            """


def get_attribute_id_from_name_query():
    return """
        query Attributes($text: String!) {
          attributes(text: $text) {
            id
          }
        }
    """


def create_attribute_from_name_query():
    return """
            mutation CreateAttribute($attribute: AttributeInput!) {
              createAttribute(attribute: $attribute) {
                id
                displayName
                source
              }
            }
    """


def link_attribute_to_product():
    return """
        mutation LinkAttributeToProduct($productId: String!, $attributeId: String!) {
          linkAttributeToProduct(productId: $productId, attributeId: $attributeId) {
            id
            attributes {
                id
                displayName
            }
          }
        }
    """


class ApiService:

    def __init__(self):
        token = os.getenv("X_PHIA_TOKEN")
        """
            Note, the below auth will not work locally unless you manually
            generate an identity token using 'gcloud auth print-identity-token'

            TODO - Fix this to work locally
        """
        auth_req = google.auth.transport.requests.Request()
        id_token = google.oauth2.id_token.fetch_id_token(
            auth_req, "https://phia-api-data-pipeline-prod-4wdelowz4a-uc.a.run.app"
        )
        client = GraphqlClient(
            endpoint="https://phia-api-data-pipeline-prod-4wdelowz4a-uc.a.run.app/internal/graphql",
            headers={"x-phia-token": token, "Authorization": f"Bearer {id_token}"},
        )
        self.client = client

    def get_product_by_id(self, product_id: str, page_id: str):
        try:
            resp = self.client.execute(
                query=get_product_query(), variables={"productId": product_id}
            )
            if not resp["data"]:
                logging.error(f"Product {product_id} not found from {page_id}")
                return None
            return resp["data"]["productById"]
        except Exception as e:
            logging.error(f"Failed to get product for product ID {product_id}: {e}")
            logging.exception(e)
            raise e

    def get_similar_products(
        self,
        product_id: dict,
        product_image_url: str,
        filters: dict,
        full_product_set: set,
    ):
        """
        {
          "query": {
            "image": {
                "urls": ["https://product-images.therealreal.com/BOT232401_1_enlarged.jpg"]
            }
          },
          "filters": {},
          "options": {
            "searchType": "IMAGE_EMBEDDING_V2"
            }
        }
        """
        try:
            variables = {
                "options": {
                    "searchType": "IMAGE_EMBEDDING_V2",
                    "sortType": "BEST_MATCH",
                    "similarityThresholds": {"overallSimilar": 0.7},
                    "limit": 10,
                },
                "filters": filters,
                "query": {"image": {"urls": [product_image_url]}},
            }

            search = get_search_query()
            resp = self.client.execute(query=search, variables=variables)
            if resp["data"] is None or resp["data"]["products"] is None:
                logging.warning(
                    f"No similar products found for {product_id} and {product_image_url}"
                )
                return []
            products = resp["data"]["products"]
            logging.info(f"Similar products: {products}")
            if products["numProducts"] == 0:
                logging.warning(f"No products found for {product_id}")
                return []

            ids = [
                p["id"] for p in products["results"] if p["id"] not in full_product_set
            ]
            return ids[:3]
        except Exception as e:
            logging.error(
                f"Failed to get similar products for {product_id} and {product_image_url}"
            )
            logging.exception(e)
            raise e

    def add_tags(self, product_id: str, tags: list[str]):
        try:
            for tag in tags:
                variables = {
                    "attributeId": tag,
                    "productId": product_id,
                }
                self.client.execute(
                    query=link_attribute_to_product(), variables=variables
                )
        except Exception as e:
            logging.error(f"Failed to add tags {tags} to product {product_id}")
            logging.exception(e)
            raise e

    def get_attribute_ids(self, attribute_names: list[str]):
        ids = []
        logging.info(f"Getting attributes: {attribute_names}")
        for name in attribute_names:
            variables = {"text": name.replace("'", "''")}
            try:
                get_attribute_resp = self.client.execute(
                    query=get_attribute_id_from_name_query(), variables=variables
                )
            except Exception as e:
                logging.error(f"Failed to get attribute {name}")
                logging.exception(e)
                raise e
            if get_attribute_resp["data"] and get_attribute_resp["data"]["attributes"]:
                logging.info(
                    f"Found existing attribute {get_attribute_resp.get("data")} for attribute name {name}"
                )
                found_ids = [
                    item["id"] for item in get_attribute_resp["data"]["attributes"]
                ]
                logging.info(f"Found existing ids: {found_ids}")
                ids.extend(found_ids)
                logging.info(f"ids after extend: {ids}")
            else:
                logging.info(f"Creating attribute for attribute name {name}")
                create_attribute_variables = {
                    "attribute": {"displayName": name, "source": "USER_DEFINED"}
                }
                try:
                    create_attribute_resp = self.client.execute(
                        query=create_attribute_from_name_query(),
                        variables=create_attribute_variables,
                    )
                    logging.info(f"Create attribute response {create_attribute_resp}")
                    ids.append(create_attribute_resp["data"]["createAttribute"]["id"])
                except Exception as e:
                    logging.error(f"Failed to create attribute {name}")
                    logging.exception(e)
                    raise e

        return ids
