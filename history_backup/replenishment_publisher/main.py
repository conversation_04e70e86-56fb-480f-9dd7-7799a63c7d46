import logging
import os
import sys

import functions_framework
import google.cloud.logging

from contentful_service import ContentfulService
from api_service import ApiService
from slack_service import SlackService


@functions_framework.http
def handle_replenishment(request):
    return run_replenishment(request)


def run_replenishment(request):
    page_id = ""
    try:
        setup_logging()
        if os.getenv("ENVIRONMENT") != "local":
            request_json = request.get_json(silent=True)
        else:
            request_json = request
        page_id = request_json["contentful_page_id"]
        contentful_service = ContentfulService()
        page = contentful_service.get_page(page_id)

        logging.info(f"Running replenishment publisher for page {page_id}")
        x_contentful_version = page.get("sys").get("version")
        published_version = page.get("sys").get("publishedVersion")
        published_counter = page.get("sys").get("publishedCounter")
        page_slug = page.get("fields").get("slug").get("en-US")
        if published_counter == 0:
            logging.info(f"Skipping replenishment. Page {page} is still a draft")
            return f"Replenishment publisher successful", 200

        if (
            published_version is not None
            and x_contentful_version > published_version + 1
        ):
            logging.info(f"Page {page_slug} with ID {page_id} is in need of review")

        fields = page.get("fields")
        products = fields.get("products").get("en-US").get("products")
        api_service = ApiService()

        page_type = page.get("sys").get("contentType").get("sys").get("id")
        # page_type can be 'brandPage' 'partnerPage'
        filters = {}
        if "mens" in page_slug:
            filters["genders"] = ["MEN"]

        if "womens" in page_slug:
            filters["genders"] = ["WOMEN"]

        if page_type == "brandPage":
            brand_id = fields.get("brandId", {}).get("en-US")
            if brand_id is None:
                logging.warning(
                    f"Brand ID is missing for brand page {page_slug} - {page_id}. Skipping replenishment."
                )
                return (
                    f"Skipping replenishment for brand page {page_slug} - {page_id}",
                    200,
                )
            filters["brandIds"] = [brand_id]

        if page_type == "partnerPage":
            secondhand_retailer_id = fields.get("partnerId", {}).get("en-US")
            if secondhand_retailer_id is None:
                logging.warning(
                    f"Secondhand Partner ID is missing for partner page {page_slug} - {page_id}. Skipping replenishment."
                )
                return (
                    f"Skipping replenishment for partner page {page_slug} - {page_id}",
                    200,
                )
            filters["secondhandRetailerIds"] = [secondhand_retailer_id]

        full_product_set = set()

        for product in products:
            full_product_set.add(product["productId"])

        has_sold_out_product = False
        sold_out_product_count = 0
        new_product_list = []
        for product in products:

            logging.debug(f"Product before update {product}")
            product_id = product["productId"]
            fallback_ids = product["fallbackProductIds"]

            full_product = api_service.get_product_by_id(
                product_id=product_id, page_id=page_id
            )

            if full_product is None:
                logging.warning(
                    f"Product {product_id} was not found. Removing from product list for page_id {page_id}."
                )
                continue

            if full_product.get("soldAt") is not None:
                sold_out_product_count += 1
                has_sold_out_product = True
                logging.info(
                    f"Finding alternatives for sold out product {product_id} for {page_slug} - {page_id}"
                )
                found_valid_fallback = False
                for fallback_id in fallback_ids:
                    if fallback_id in full_product_set:
                        logging.info(
                            f"Fallback id {fallback_id} already in list of products. Not using as replacement."
                        )
                        continue
                    full_fallback_product = api_service.get_product_by_id(
                        product_id=fallback_id, page_id=page_id
                    )
                    if full_fallback_product is None:
                        continue
                    if full_fallback_product.get("soldAt") is None:
                        product["productId"] = fallback_id
                        product["fallbackProductIds"] = fallback_ids[
                            fallback_ids.index(fallback_id) + 1 :
                        ]
                        full_product_set.add(fallback_id)
                        found_valid_fallback = True
                        logging.info(
                            f"Using fallback product ID {fallback_id} to replace {product_id} for {page_slug} - {page_id}"
                        )
                        break

                if not found_valid_fallback:
                    logging.info(
                        f"Using similarity search to find alternatives for {product_id}"
                    )
                    similar_products = api_service.get_similar_products(
                        full_product["id"],
                        full_product["imgUrl"],
                        filters,
                        full_product_set,
                    )
                    logging.info(f"Found similar products: {similar_products}")
                    if len(similar_products) > 0:
                        first = similar_products.pop(0)
                        product["productId"] = first
                        full_product_set.add(first)
                        product["fallbackProductIds"] = similar_products

            logging.info(f"Product after update {product}")

            new_product_list.append(product)
        if has_sold_out_product:
            fields.get("products").get("en-US")["products"] = new_product_list
            logging.info(
                f"Updating page {page_slug} - {page_id} with {sold_out_product_count} sold out products replenished"
            )
            contentful_service.update_contentful(fields, x_contentful_version, page_id)
            SlackService().send_slack_message(page_id, page_slug)
        else:
            logging.info(f"No sold out products for page {page_slug} with ID {page_id}")
        logging.info(f"Replenishment publisher successful for {page}")
        return f"Replenishment publisher successful", 200
    except Exception as e:
        logging.exception(f"Replenishment publisher failed for {page_id}: {e}")
        return f"Replenishment publisher failed {e}", 500


def setup_logging():
    if os.getenv("ENVIRONMENT") != "local":
        logging.getLogger().setLevel(logging.INFO)
        client = google.cloud.logging.Client()
        client.setup_logging()
    else:
        logging.basicConfig(stream=sys.stdout, level=logging.INFO)
