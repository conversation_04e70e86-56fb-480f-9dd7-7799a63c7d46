import logging
import os
import google.cloud.logging
from sqlalchemy.orm import sessionmaker

from utils.pipeline_db.connector import connect_to_pipeline_db_with_connector
from utils.pipeline_db.service import create_partner_product_sync
from utils.tsv_splitter import TSVSplitter
from ebay_data_downloader import EbayDataDownloader

EBAY_PARTNER_NAME = "ebay"


def run_ebay_full_feed_download():
    logging.info("Bootstrapping eBay Item Feed")
    engine = connect_to_pipeline_db_with_connector()
    Session = sessionmaker(bind=engine)
    with Session() as session:
        partner_product_sync_id = create_partner_product_sync(
            session, EBAY_PARTNER_NAME
        )
    logging.info(f"Created partner product sync {partner_product_sync_id}")
    file_name = EbayDataDownloader().fetch_data()
    TSVSplitter(
        partner_name=EBAY_PARTNER_NAME,
        batch_size=10000,
        partner_product_sync_id=partner_product_sync_id,
    ).split_data_into_batches(input_filepath=file_name)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    if int(os.environ.get("PRODUCTION", 0)) == 1:
        logging_client = google.cloud.logging.Client()
        logging_client.setup_logging()
        try:
            run_ebay_full_feed_download()
        except Exception as e:
            logging.error(e)
            raise e
