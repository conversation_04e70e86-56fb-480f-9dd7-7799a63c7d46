import os
import io
import time
import logging
import gzip
import csv
import tracemalloc
from datetime import datetime

from sqlalchemy.orm import sessionmaker

from utils.gcs_storage_manager import GCSStorageManager
from utils.helper import get_tracemem_use
from utils.pipeline_db.connector import connect_to_pipeline_db_with_connector
from utils.pipeline_db.service import create_partner_product_file_sync


class TSVSplitter:

    def __init__(self, partner_name, batch_size, partner_product_sync_id):
        self.partner_name = partner_name
        self.batch_size = batch_size
        self.partner_product_sync_id = partner_product_sync_id

    def split_data_into_batches(self, input_filepath: str):
        """
        Split a compressed TSV file (gzip) into smaller batches of records.

        Args:
            input_filepath: The path to the input gzip file containing the TSV file.
        """
        tracemalloc.start()
        with gzip.open(filename=input_filepath, mode="rt") as zf:
            logging.info("Splitting file into batches...")
            """
                We want to use a last_modified_timestamp
                set by the Downloader implementation or fallback to the file
                path's last modified time
            """
            file_modified_time = os.path.getmtime(input_filepath)

            file_timestamp = int(datetime.fromtimestamp(file_modified_time).timestamp())
            self._process_tsv_file(
                file=zf,
                batch_size=self.batch_size,
                file_timestamp=file_timestamp,
            )

    def _process_tsv_file(self, file, batch_size: int, file_timestamp: int):
        """
        Process the TSV file, splitting it into batches.

        Args:
            file: The file object representing the TSV file.
            batch_size: The number of records in each batch.
            file_timestamp: The timestamp of the partner inventory file.
        """
        start = time.time()
        logging.debug(f"Memory before batching data: {get_tracemem_use()}")

        reader = csv.reader(file, delimiter="\u0009", escapechar="\\")
        header = next(reader)

        batch_count, record_count = 0, 0
        output_string_io = io.StringIO()
        writer = csv.writer(output_string_io, delimiter="\u0009", escapechar="\\")

        writer.writerow(header)

        for row in reader:
            writer.writerow(row)
            record_count += 1

            if record_count >= batch_size:
                output_string_io.seek(0)
                tsv_str = output_string_io.getvalue()

                self._upload_to_cloud_storage(
                    tsv_str, record_count, batch_count, file_timestamp
                )
                logging.debug(
                    f"Memory after batch {batch_count} clean: {get_tracemem_use()}"
                )
                batch_count += 1
                record_count = 0
                logging.info(
                    f"Time splitting {batch_size} records: {time.time() - start}"
                )
                start = time.time()
                output_string_io = io.StringIO()
                writer = csv.writer(output_string_io, delimiter="\u0009")
                writer.writerow(header)

        # Write any remaining records to a final batch file
        if record_count > 0:
            output_string_io.seek(0)
            tsv_str = output_string_io.getvalue()
            self._upload_to_cloud_storage(
                tsv_str, record_count, batch_count, file_timestamp
            )
            output_string_io = io.StringIO()

    def _upload_to_cloud_storage(
        self,
        batch_data: str,
        record_count: int,
        batch_count: int,
        file_timestamp: int,
    ):
        """
        Write the current batch to an TSV file to GCS.

        Args:
            batch_data: The string text stream of serialized elements of the current batch.
            record_count: The number of records in the current batch.
            batch_count: The current batch number.
            file_timestamp: The timestamp of the partner inventory file.
        """
        batch_filename = f"{self.partner_name}_{file_timestamp}_{batch_count}.tsv"
        partner_product_file_sync_id = self.create_partner_product_file_sync(
            batch_filename
        )
        GCSStorageManager().upload_to_gcs(
            content=batch_data,
            file_name=batch_filename,
            partner_name=self.partner_name,
            content_type="text/tsv",
            metadata={
                "partner_product_file_sync_id": partner_product_file_sync_id,
                "is_ebay_bootstrap": True,
            },
        )
        logging.info(f"Batch {batch_count} written with {record_count} records.")
        logging.debug(f"Memory after batch {batch_count} write: {get_tracemem_use()}")

    def create_partner_product_file_sync(self, file_name: str) -> str:
        engine = connect_to_pipeline_db_with_connector()
        Session = sessionmaker(bind=engine)
        with Session() as session:
            partner_product_file_sync_id = create_partner_product_file_sync(
                session, self.partner_product_sync_id, file_name, self.partner_name
            )
            return partner_product_file_sync_id
