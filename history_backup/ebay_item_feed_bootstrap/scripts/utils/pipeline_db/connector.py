import os

import sqlalchemy
import pg8000
from google.cloud import secretmanager
from google.cloud.sql.connector import Connector, IPTypes


def connect_to_pipeline_db_with_connector() -> sqlalchemy.engine.base.Engine:
    env = os.getenv("ENVIRONMENT")
    if env == "local":
        pool = sqlalchemy.create_engine(
            "postgresql+pg8000://chandler:bing@localhost:20433/data_pipeline_dev",
        )
    else:
        pool = sqlalchemy.create_engine(
            "postgresql+pg8000://", creator=get_google_cloud_creator()
        )
    return pool


def get_google_cloud_creator():
    instance_connection_name = os.getenv(
        "DATA_PIPELINE_INSTANCE_CONNECTION_NAME",
        "phia-prod-416420:us-central1:data-pipeline-db",
    )
    db_user = os.getenv("DATA_PIPELINE_DB_USER", "data_pipeline_user")
    db_pass = os.getenv("DATA_PIPELINE_DB_USER_PASS", get_pipeline_db_pass())
    db_name = os.getenv("DATA_PIPELINE_DB_NAME", "postgres")

    connector = Connector()
    ip_type = IPTypes.PUBLIC

    def getconn() -> pg8000.dbapi.Connection:
        conn = connector.connect(
            instance_connection_name,
            "pg8000",
            user=db_user,
            password=db_pass,
            db=db_name,
            ip_type=ip_type,
        )
        return conn

    return getconn


def get_pipeline_db_pass():
    client = secretmanager.SecretManagerServiceClient()
    app_id_name = (
        f"projects/phia-prod-416420/secrets/data-pipeline-db-user-password/versions/1"
    )
    data_pipeline_db_user_pass_resp = client.access_secret_version(
        request={"name": app_id_name}
    )
    data_pipeline_db_user_pass = data_pipeline_db_user_pass_resp.payload.data.decode(
        "UTF-8"
    )
    return data_pipeline_db_user_pass
