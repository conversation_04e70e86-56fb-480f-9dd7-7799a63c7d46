# -*- coding: utf-8 -*-
"""
Copyright 2019 eBay Inc.

Licensed under the Apache License, Version 2.0 (the "License");
You may not use this file except in compliance with the License.
You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and
limitations under the License.
"""

import json
import requests
import logging
from .model.util import (
    _generate_request_headers,
    _generate_application_request_body,
)
from datetime import datetime, timedelta
from .model.model import oAuth_token


class oauth2api(object):

    def __init__(self, client_id, client_secret):
        self.client_id = client_id
        self.client_secret = client_secret

    def get_application_token(self, env_type, scopes):
        """
        makes call for application token and stores result in credential object
        returns credential object
        """

        logging.info("Trying to get a new application access token ... ")
        headers = _generate_request_headers(
            client_id=self.client_id, client_secret=self.client_secret
        )
        body = _generate_application_request_body(" ".join(scopes))

        resp = requests.post(env_type.api_endpoint, data=body, headers=headers)
        content = json.loads(resp.content)
        token = oAuth_token()

        if resp.status_code == requests.codes.ok:
            token.access_token = content["access_token"]
            # set token expiration time 5 minutes before actual expire time
            token.token_expiry = (
                datetime.utcnow()
                + timedelta(seconds=int(content["expires_in"]))
                - timedelta(minutes=5)
            )

        else:
            token.error = str(resp.status_code) + ": " + content["error_description"]
            logging.error(
                "Unable to retrieve token.  Status code: %s - %s",
                resp.status_code,
                requests.status_codes._codes[resp.status_code],
            )
            logging.error(
                "Error: %s - %s", content["error"], content["error_description"]
            )
        return token
