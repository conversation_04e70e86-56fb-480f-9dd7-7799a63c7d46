# -*- coding: utf-8 -*-
"""
Copyright 2019 eBay Inc.

Licensed under the Apache License, Version 2.0 (the "License");
You may not use this file except in compliance with the License.
You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and
limitations under the License.

"""
import base64


def _generate_request_headers(client_id, client_secret):
    s = client_id + ":" + client_secret
    encoded = s.encode("ascii")
    b64_encoded_credential = base64.b64encode(encoded)
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": "Basic " + b64_encoded_credential.decode("ascii"),
    }
    return headers


def _generate_application_request_body(scopes):
    body = {"grant_type": "client_credentials", "scope": scopes}
    return body
