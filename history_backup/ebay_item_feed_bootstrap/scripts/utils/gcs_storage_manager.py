from google.cloud import storage
import os
import logging

from utils.gcs_task_manager import GCSTaskManager


class GCSStorageManager:
    def __init__(self):
        self.bucket_name = os.getenv("BUCKET_NAME", "raw-chunked-files-prod")
        self.storage_client = storage.Client()
        self.bucket = self.storage_client.bucket(self.bucket_name)

    def upload_to_gcs(
        self,
        content: str,
        file_name: str,
        partner_name: str,
        content_type: str,
        metadata: dict,
    ):
        file_path = f"{partner_name}/{file_name}"
        blob = self.bucket.blob(file_path)
        if metadata is not None:
            blob.metadata = metadata
        blob.upload_from_string(content, content_type=content_type)
        logging.info(f"Uploaded {file_name} to GCS")
        GCSTaskManager().create_task(file_path)

    def upload_tsv_to_gcs(self, tsv_str: str, file_name: str, partner_name: str):
        self.upload_to_gcs(tsv_str, file_name, partner_name, "text/tsv")
