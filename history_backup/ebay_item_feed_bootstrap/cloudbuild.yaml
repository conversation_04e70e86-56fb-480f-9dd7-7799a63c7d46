steps:
  # Step 1: Build Docker image
  - name: "gcr.io/cloud-builders/docker"
    args:
      ["build", "-t", "${_DOCKER_IMAGE}", "-f", "./ebay_item_feed_bootstrap/Dockerfile", "."]
    id: Docker Build
  # Step 2: Push the Docker image to Google Artifacts Registry
  - name: "gcr.io/cloud-builders/docker"
    args: ["push", "${_DOCKER_IMAGE}"]
    id: Docker Push
options:
  logging: CLOUD_LOGGING_ONLY
substitutions:
  _DOCKER_IMAGE: ${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE}