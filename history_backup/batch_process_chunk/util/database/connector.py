import os

import sqlalchemy
import pg8000
from google.cloud.sql.connector import Connector, IPTypes


def connect_with_connector() -> sqlalchemy.engine.base.Engine:
    if os.getenv("ENVIRONMENT") == "local":
        pool = sqlalchemy.create_engine(
            "postgresql+pg8000://chandler:bing@localhost:20433/data_pipeline_dev"
        )
    else:
        pool = sqlalchemy.create_engine(
            "postgresql+pg8000://", creator=get_google_cloud_creator()
        )
    return pool


def get_google_cloud_creator():
    instance_connection_name = os.getenv("DATA_PIPELINE_INSTANCE_CONNECTION_NAME")
    db_user = os.getenv("DB_USER", "data_pipeline_user")
    db_pass = os.getenv("DATA_PIPELINE_DB_USER_PASS")
    db_name = os.getenv("DB_NAME", "postgres")

    connector = Connector()
    ip_type = IPTypes.PUBLIC

    def getconn() -> pg8000.dbapi.Connection:
        conn = connector.connect(
            instance_connection_name,
            "pg8000",
            user=db_user,
            password=db_pass,
            db=db_name,
            ip_type=ip_type,
        )
        return conn

    return getconn
