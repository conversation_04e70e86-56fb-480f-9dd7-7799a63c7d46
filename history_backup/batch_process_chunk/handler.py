import logging


class FileHandler:
    def __init__(
        self,
        partner_name: str,
        file_name: str,
    ):
        self.partner_name = partner_name
        self.file_name = file_name

    def __entry_is_empty(self, entry):
        """
        Check if the given entry (lxml.etree._Element) is None or equivalent.
        Returns:
            True if the entry is empty, False otherwise.
        """
        if entry is None:
            return True
        if (
            len(entry) == 0
            and not (entry.text and entry.text.strip())
            and not entry.attrib
        ):
            return True
        return False

    def _process_entry(self, entry):
        if self.__entry_is_empty(entry):
            return None

        logging.info(f"Processing entry... ${str(entry)[:100]}")

        # TODO: Implement
        return entry

    def parse_and_process(self, input_filepath: str, output_filepath: str):
        logging.info(
            f"Parsing and processing, Input ${input_filepath} Output ${output_filepath}"
        )

        with open(input_filepath, "r") as input_file:
            with open(output_filepath, "w") as output_file:
                input_jsonls = list(input_file)
                logging.info(f"input_jsonls ${input_jsonls}")
                # TODO: Process JSONL's

                output_file.write("test output" + "\n".join(input_jsonls))
                logging.info(f"output_file written successfully")
