import logging
import os
import sys

import functions_framework
import google.cloud.logging
from google.cloud import storage
from sqlalchemy.orm import sessionmaker
from handler import FileHandler
from storage.gcs_storage_manager import GCSStorageManager
from util.database.connector import connect_with_connector
from util.database.models import Status
from util.database.service import (
    update_partner_product_file_syncs_status,
    get_partner_product_file_syncs_status,
)


@functions_framework.http
def batch_process_partner_chunk(request):
    """HTTP Cloud Function to fetch data for a given partner."""
    setup_logging()
    request_json = request.get_json()
    logging.info("Received request to batch process partner chunk")
    if (
        request_json
        and "message" in request_json
        and "attributes" in request_json["message"]
    ):
        message = request_json["message"]["attributes"]
        logging.info(f"Received message: {message}")
    else:
        logging.error(f"Missing message or attributes in the request {request_json}")
        logging.error(f"Raw request {request}")
        return "Bad Request", 400

    if message and "objectId" in message and "bucketId" in message:
        file_name = message["objectId"]
        bucket_name = message["bucketId"]
        logging.info(f"Received file: {file_name} from bucket: {bucket_name}")
    else:
        logging.error("Missing file name or bucket name in the request")
        return "Bad Request", 400

    print(f"Processing file: {file_name} from bucket: {bucket_name}")

    # Extract the folder path from the file name
    folder_path = "/".join(file_name.split("/")[:-1])
    if folder_path:
        logging.info(f"Folder path: {folder_path}")

    # Placeholder for additional processing logic
    return process_file(file_name, bucket_name, folder_path)


def setup_logging():
    if os.getenv("ENVIRONMENT") != "local":
        logging.getLogger().setLevel(logging.INFO)
        client = google.cloud.logging.Client()
        client.setup_logging()
    else:
        logging.basicConfig(stream=sys.stdout, level=logging.INFO)


def process_file(file_path, bucket_name, partner_name):
    partner_product_file_sync_id = None
    engine = connect_with_connector()
    Session = sessionmaker(bind=engine)
    try:
        # TODO: StatLogger
        file_name = os.path.basename(file_path)
        logging.info(
            f"Processing file: {file_name} from bucket: {bucket_name} for partner: {partner_name}"
        )
        # Initialize a storage client
        storage_client = storage.Client()

        # Get the bucket and blob (file) objects
        bucket = storage_client.bucket(bucket_name)
        logging.info(f"Bucket: {bucket.name}")
        blob = bucket.blob(file_path)
        blob.reload()
        logging.info(f"Got blob at file path: {file_path}")

        partner_product_file_sync_id = blob.metadata.get("partner_product_file_sync_id")
        is_ebay_bootstrap = bool(blob.metadata.get("is_ebay_bootstrap", False))
        with Session() as session:
            status = get_partner_product_file_syncs_status(
                session, partner_product_file_sync_id
            )
            if status in [
                Status.BATCH_PROCESSING.value,
                Status.BATCH_PROCESSED.value,
                # TODO: Add this state back in after all backfill is done
                # Status.INGESTING.value,
                # Status.INGESTED.value,
            ]:
                logging.warning(
                    f"{file_path} with id {partner_product_file_sync_id} is already in {status} "
                    f"state. Will skip processing."
                )
                return (
                    f"Batch process chunk finished successfully for {partner_name}!",
                    200,
                )
            update_partner_product_file_syncs_status(
                session, partner_product_file_sync_id, Status.BATCH_PROCESSING.value
            )

        # TODO: Update

        # Download the file to a temporary location
        temp_file_path = f"/tmp/{file_path}"
        os.makedirs(os.path.dirname(temp_file_path), exist_ok=True)
        blob.download_to_filename(temp_file_path)
        logging.info(f"Downloaded file to: {temp_file_path}")

        # Define the output file path
        output_file_path = f"/tmp/output/{file_path}"
        os.makedirs(os.path.dirname(output_file_path), exist_ok=True)

        # Create an instance of the handler based on the partner name and process it
        handler_instance = FileHandler(
            partner_name=partner_name,
            file_name=file_path,
        )
        handler_instance.parse_and_process(temp_file_path, output_file_path)
        GCSStorageManager().upload_jsonl_to_gcs(
            output_file_path,
            file_name,
            partner_name,
            {
                "partner_product_file_sync_id": partner_product_file_sync_id,
                "is_ebay_bootstrap": is_ebay_bootstrap,
            },
        )
        os.remove(temp_file_path)
        os.remove(output_file_path)
        logging.info(f"Batch process completed successfully for {partner_name}!")
        with Session() as session:
            update_partner_product_file_syncs_status(
                session,
                partner_product_file_sync_id,
                Status.BATCH_PROCESSED.value,
            )
        return (
            f"Batch process completed successfully for {partner_name}!",
            200,
        )
    except Exception as e:
        logging.exception(e)
        if partner_product_file_sync_id is not None:
            with Session() as session:
                update_partner_product_file_syncs_status(
                    session,
                    partner_product_file_sync_id,
                    Status.FAILED_BATCH_PROCESS.value,
                )
        return f"Error processing data for {partner_name}: {e}", 500
