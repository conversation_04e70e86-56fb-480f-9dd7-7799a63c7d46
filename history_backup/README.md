# Data Pipeline

This is the directory containing Phia's data pipeline

The main pipeline:

- `download_and_chunk` contains the cloud function for downloading and chunking our data from our various partners.
- `transform_chunk` applies cleaning, curation steps, and transformations to each given chunk.
- `batch_process_chunk` applied batch processing steps such as ML (e.g. OpenAI gpt-4o-mini category prediction).
- `ingest_data` inserts the standardized chunk into our database.
- `ebay_item_feed_bootstrap` contains a script for downloading and chunking the Ebay full item feed.

Each directory above represents a different [cloud function](https://console.cloud.google.com/functions/list?referrer=search&project=phia-prod-416420).
These functions are invoked on GCP via an http request trigger. You can also run them locally. For example,
in the `transform_chunk` directory, you could basically invoke the lambda on a single file using the following script:

```python
from enums import Partners
from main import process_file


def run():
    luxury_closet_path = "the_luxury_closet/the_luxury_closet_1715174134_0.xml" # Example file path in Cloud Storage
    bucket_name = "raw-chunked-files-prod"
    process_file(
        luxury_closet_path,
        bucket_name,
        Partners.THE_LUXURY_CLOSET.value,
    )


run()
```

Other services:

- `modal` batch creates image embeddings using modal

## Adding Another Cloud Function Step

There is a chicken-vs-egg problem with the way Google Cloud Functions vs. Terraform interact. They are mutually linked, so the way we solve for this is by deploying in the following sequence:

- Use gcloud cli to create the function and the zip function asset in cloud storage
- Download the zip function asset from cloud storage gcf-v2-sources bucket
- Delete the cloud function (which will remove the cloud storage asset from the gcf-v2-sources bucket)
- Add back the asset to the exact same cloud storage directory that terraform assumes has the assest
- Apply the terraform
