import re

from cleaners.base_cleaner import BaseCleaner, extract_condition
from enum import Enum

from models.product import Condition
from util.processors import extract_numeric_price
from typing import override

THE_LUXURY_CLOSET_NAMESPACE = {"g": "http://base.google.com/ns/1.0"}

THE_LUX_CLOSET_CONDITION_MAP = {
    "pristine": Condition.PRISTINE.value,
    "excellent": Condition.PRISTINE.value,
    "new": Condition.PRISTINE.value,
    "good": Condition.VERY_GOOD.value,
    "fair": Condition.GOOD.value,
    "used": Condition.VERY_GOOD.value,
}


class Availability(Enum):
    """Enum class for TheLuxuryCloset availability status"""

    BACKORDER = "backorder"
    IN_STOCK = "in stock"
    OUT_OF_STOCK = "out of stock"
    PREORDER = "preorder"


class TheLuxuryClosetCleaner(BaseCleaner):
    @override
    def get_value(self, key):
        value = self.item.find(key, THE_LUXURY_CLOSET_NAMESPACE)
        return None if value is None else value.text

    def is_available(self) -> bool:
        """Check if the product is available for TheLuxuryCloset"""
        availability = self.get_value("g:availability")
        return availability == Availability.IN_STOCK.value

    def is_authenticated(self) -> bool:
        """ "TheLuxuryCloset authenticates every single product"""
        return True

    def get_name(self) -> str:
        """Extract name from item for TheLuxuryCloset"""
        return self.get_value("title")

    def get_price(self) -> float:
        """Extract price from item for TheLuxuryCloset"""
        price = self.get_value("g:price")
        return extract_numeric_price(price)

    def get_product_url(self) -> str | None:
        """Extract product URL from item for TheLuxuryCloset"""
        product_url = self.get_value("link")
        if product_url is not None:
            return f"{product_url}?utm_source=phia&utm_medium=affiliate&utm_campaign=extension"
        return product_url

    def get_img_url(self) -> str:
        """Extract image URL from item for TheLuxuryCloset"""
        return self.get_value("g:image_link")

    def get_additional_img_urls(self) -> list[str]:
        """Extract additional image URLs from item for TheLuxuryCloset"""
        all_images = self.item.findall(
            "g:additional_image_link", THE_LUXURY_CLOSET_NAMESPACE
        )
        return [img.text for img in all_images]

    def get_description(self):
        """Extract description from item for TheLuxuryCloset"""
        return self.get_value("description")

    def get_material(self) -> str:
        """Extract material from item for TheLuxuryCloset"""
        return self.get_value("material")

    def get_raw_id(self) -> str:
        """Extract raw ID from item for TheLuxuryCloset"""
        return self.get_value("g:id")

    def get_gender(self) -> str:
        """Extract gender from item for TheLuxuryCloset"""
        return self.get_value("g:gender")

    def get_color(self) -> str:
        """Extract color from item"""
        return self.get_value("g:color")

    def get_category_raw(self) -> str:
        """Extract category from item"""
        return self.get_value("g:google_product_category")

    def get_raw_size(self) -> str | None:
        size = self.get_value("g:size")
        if size is None:
            size_system, size = extract_country_code_and_size(self.get_name())
            return size
        return size

    def get_raw_size_system(self) -> str:
        size_system = self.get_value("g:size_system")
        if size_system is None:
            size_system, size = extract_country_code_and_size(self.get_name())
            return size
        return size_system

    def get_condition(self) -> str | None:
        return extract_condition(
            self.get_value("condition_detail"), THE_LUX_CLOSET_CONDITION_MAP
        ) or THE_LUX_CLOSET_CONDITION_MAP.get(self.get_value("g:condition"))

    def get_brand(self) -> str:
        return self.get_value("g:brand")

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return True


def extract_country_code_and_size(string):
    pattern = r"(\b[A-Z]{2}\b) (\d+)"
    match = re.search(pattern, string)
    if match:
        country_code = match.group(1)
        size = match.group(2)
        return country_code, size
    else:
        return None, None
