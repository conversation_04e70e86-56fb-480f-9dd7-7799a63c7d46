from typing import override

from cleaners.base_cleaner import BaseCleaner
from enum import Enum

from util.processors import extract_numeric_price


class Availability(Enum):
    """Enum class for CJ availability status"""

    IN_STOCK = "in stock"
    OUT_OF_STOCK = "out of stock"
    PREORDER = "preorder"
    BACKORDER = "backorder"


class CJCleaner(BaseCleaner):
    @override
    def get_value(self, key):
        value = self.item.find(key)
        return None if value is None else value.text

    def is_available(self) -> bool:
        """Check if the product is available for CJ"""
        return self.get_value("availability") == Availability.IN_STOCK.value

    def get_name(self) -> str:
        """Extract name from item for CJ"""
        return self.get_value("title")

    def get_price(self) -> float:
        """Extract price from item for CJ"""
        price = self.get_value("sale_price") or self.get_value("price")
        return extract_numeric_price(price)

    def get_product_url(self) -> str:
        """Extract product URL from item for CJ"""
        return self.get_value("link")

    def get_img_url(self) -> str:
        """Extract image URL from item for CJ"""
        return self.get_value("image_link")

    def get_additional_img_urls(self) -> list[str]:
        """Extract additional image URLs from item for CJ"""
        additional_img_urls = self.get_value("additional_image_link")
        if isinstance(additional_img_urls, list):
            return additional_img_urls
        elif isinstance(additional_img_urls, str):
            return [additional_img_urls]

    def get_description(self):
        """Extract description from item for CJ"""
        return self.get_value("description")

    def get_material(self) -> str:
        """Extract material from item for CJ"""
        return self.get_value("material")

    def get_raw_id(self) -> str:
        """Extract raw ID from item for CJ"""
        return self.get_value("id")

    def get_gender(self) -> str:
        return self.get_value("gender")

    def get_category_raw(self) -> str:
        return self.get_value("product_type")

    def get_raw_size(self) -> str:
        return self.get_value("size")

    def get_condition(self) -> str:
        """Extract condition from item"""
        return self.get_value("condition")

    @override
    def get_brand(self) -> str:
        return self.get_value("brand")
