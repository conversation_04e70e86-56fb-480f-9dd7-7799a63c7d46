from enum import Enum
from typing import override

from cleaners.base_cleaner import Base<PERSON>leaner, get_category_broad_from_category_raw
from models.product import Condition
from util.processors import extract_numeric_price

FARFETCH_CONDITION_MAP = {
    "used": Condition.GOOD.value,
    "new": Condition.PRISTINE.value,
}


class Availability(Enum):
    """Enum class for CJ availability status"""

    IN_STOCK = "in stock"
    OUT_OF_STOCK = "out of stock"
    PREORDER = "preorder"
    BACKORDER = "backorder"


class FarfetchCleaner(BaseCleaner):

    @override
    def get_value(self, key):
        value = self.item.find(key)
        return None if value is None else value.text

    def is_available(self) -> bool:
        return self.get_value("availability") == Availability.IN_STOCK.value

    def get_name(self) -> str:
        return self.get_value("title")

    def get_price(self) -> float:
        price = self.get_value("sale_price") or self.get_value("price")
        return extract_numeric_price(price)

    def get_product_url(self) -> str:
        return self.get_value("link")

    def get_img_url(self) -> str:
        return self.get_value("image_link")

    def get_additional_img_urls(self) -> list[str]:
        """Extract additional image URLs from item for Impact"""
        additional_image_links = []
        i = 1
        while True:
            link = self.get_value(f"additional_image_link_{i}")
            if link is None or link.strip() == "":
                break
            additional_image_links.append(link)
            i += 1
        return additional_image_links

    def get_description(self):
        return self.get_value("description")

    def get_material(self) -> str:
        return self.get_value("material")

    def get_raw_id(self) -> str:
        return self.get_value("id")

    def get_gender(self) -> str:
        return self.get_value("gender")

    def get_category_raw(self) -> str:
        return self.get_value("product_type")

    def get_category_broad(self) -> str:
        """Extract category broad from item"""
        return get_category_broad_from_category_raw(self.get_category_raw())

    def get_raw_size(self) -> str:
        return self.get_value("size")

    def get_raw_size_system(self) -> str | None:
        return self.get_value("size_standard")

    def get_condition(self) -> str | None:
        """Extract condition from item"""
        return FARFETCH_CONDITION_MAP.get(self.get_value("condition"))

    def get_brand(self) -> str:
        return self.get_value("brand")

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return True
