from cleaners.base_cleaner import BaseCleaner
from cleaners.impact_cleaner import Impact<PERSON>leaner
from typing import override

from models.product import Condition

TRR_CONDITION_MAP = {
    "new": Condition.PRISTINE.value,
    "used": Condition.VERY_GOOD.value,
}


class TheRealRealCleaner(BaseCleaner):
    def get_price(self) -> float:
        sale_price = self.get_value("saleprice")
        if sale_price is None:
            return self.get_value("price")
        return sale_price

    def get_product_url(self) -> str:
        return self.get_value("producturl")

    def get_img_url(self) -> str:
        return self.get_value("imageurl")

    def get_additional_img_urls(self) -> list[str] | None:
        alt_image_url = self.get_value("alternateimageurl")
        return [alt_image_url] if alt_image_url else None

    def get_material(self) -> str:
        return self.get_value("material")

    def get_gender(self) -> str:
        return self.get_value("gender")

    def get_category_raw(self) -> str:
        return self.get_value("subcategory")

    def get_raw_id(self) -> str:
        return self.get_value("sku")

    def get_name(self) -> str:
        return self.get_value("name")

    def get_description(self):
        """Extract description from item for Impact"""
        return self.get_value("description")

    def is_authenticated(self):
        """The RealReal authenticates every single product"""
        return True

    @override
    def is_available(self) -> bool:
        # RealReal products are updated every 2 hours and are always in stock
        return True

    def get_raw_size(self) -> str:
        return self.get_value("size")

    @override
    def get_condition(self) -> str:
        # Condition is only Used. How do we get more granular condition?
        return TRR_CONDITION_MAP.get(self.get_value("condition").lower())

    @override
    def get_brand(self) -> str:
        return self.get_value("brand")

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return True
