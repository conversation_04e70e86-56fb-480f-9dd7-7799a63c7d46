import re

from cleaners.cj_cleaner import <PERSON><PERSON><PERSON>leaner
from models.product import Condition

REBAG_CONDITION_REGEX = r"Condition: ((\w| )*)(\.)"

REBAG_CONDITION_MAP = {
    "pristine": Condition.PRISTINE.value,
    "excellent": Condition.PRISTINE.value,
    "great": Condition.VERY_GOOD.value,
    "very good": Condition.VERY_GOOD.value,
    "good": Condition.GOOD.value,
    "fair": Condition.GOOD.value,
}


class RebagCleaner(CJCleaner):
    def is_authenticated(self) -> bool:
        """<PERSON><PERSON> authenticates every single product"""
        return True

    def get_category_raw(self) -> str:
        """Extract category from item"""
        google_product_category_name = self.get_value("google_product_category_name")
        if google_product_category_name is not None:
            return google_product_category_name
        else:
            return self.get_value("product_type")

    def get_raw_size(self) -> str:
        """
        Extract size from item
        size system is also available as a field
        """
        return self.get_value("size")

    def get_raw_size_system(self) -> str:
        return self.get_value("size_system")

    def get_color(self) -> str:
        return self.get_value("color")

    def get_condition(self) -> str | None:
        return REBAG_CONDITION_MAP.get(
            get_condition_from_description_str(self.get_description()).lower()
        )

    def is_returnable(self) -> bool:
        return True


def get_condition_from_description_str(description_str: str) -> str | None:
    condition_match = re.search(
        REBAG_CONDITION_REGEX,
        description_str,
    )
    return condition_match.group(1).strip() if condition_match else None
