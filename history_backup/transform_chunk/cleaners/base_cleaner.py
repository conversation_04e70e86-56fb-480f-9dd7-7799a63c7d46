import re
from abc import ABC, abstractmethod
from models.product import CleanProductSchema, CategoryBroad, Condition
from datetime import datetime
from util.processors import drop_null_values_from_dict

BASE_CLEANER_CATEGORY_BROAD_REGEX = {
    CategoryBroad.PANTS.value: re.compile(
        r"\b(?:pant|pants|jeans|trousers)\b", re.IGNORECASE
    ),
    CategoryBroad.SHOES.value: re.compile(
        r"\b(?:shoes|sneakers|pumps|espadrilles|footwear)\b", re.IGNORECASE
    ),
    CategoryBroad.RINGS.value: re.compile(r"\b(?:ring|rings)\b", re.IGNORECASE),
    CategoryBroad.ACCESSORIES.value: re.compile(
        r"\b(?:accessories|wallets|jewellery"
        r"|jewelry|watch|belts|bags|hats|"
        r"kitchen|sunglasses|eyeglasses|watches|handbags|goggles|eye masks|luggage)\b",
        re.IGNORECASE,
    ),
    CategoryBroad.CLOTHING.value: re.compile(
        r"\b(?:clothing|formal|tops|costumes|skirts|jackets|coats)\b", re.IGNORECASE
    ),
}


class BaseCleaner(ABC):
    def __init__(self, partner_name: str, item: dict, file_name: str):
        self.partner_name = partner_name
        self.item = item
        self.file_name = file_name

    def get_value(self, key: str):
        """Method for extracting key from item"""
        return self.item.get(key)

    @abstractmethod
    def is_available(self) -> bool:
        """Check if the product is available"""
        pass

    def is_authenticated(self) -> bool:
        """Check if the product is authenticated. Default to False."""
        return False

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return False

    @abstractmethod
    def get_name(self) -> str:
        """Extract name from item"""
        pass

    @abstractmethod
    def get_price(self) -> float:
        """Extract price from item"""
        pass

    @abstractmethod
    def get_product_url(self) -> str:
        """Extract product URL from item"""
        pass

    @abstractmethod
    def get_img_url(self) -> str:
        """Extract image URL from item"""
        pass

    @abstractmethod
    def get_additional_img_urls(self) -> list[str] | None:
        """Extract additional image URLs from item"""
        pass

    @abstractmethod
    def get_description(self) -> str:
        """Extract description from item"""
        pass

    @abstractmethod
    def get_material(self) -> str:
        """Extract material from item"""
        pass

    @abstractmethod
    def get_raw_id(self) -> str:
        """Extract raw ID from item"""
        pass

    @abstractmethod
    def get_gender(self) -> str:
        """Extract raw gender from item"""
        pass

    def get_color(self) -> str:
        """Extract color from item"""
        return self.get_value("color")

    def get_last_seen_at(self) -> str:
        """
        Represents the time at which the item was last seen.
        TODO: We want to update other cleaners to set this value based on
            the original file timestamp or when the original product processed
        """
        return str(datetime.now())

    @abstractmethod
    def get_condition(self) -> str:
        """Extract condition from item"""
        pass

    @abstractmethod
    def get_category_raw(self) -> str:
        """Extract category raw from item"""
        pass

    @abstractmethod
    def get_raw_size(self) -> str | None:
        """Extract raw size from item"""
        pass

    def get_raw_size_system(self) -> str | None:
        return None

    def get_category_broad(self) -> str:
        """Extract category broad from item"""
        return get_category_broad_from_category_raw(self.get_category_raw())

    @abstractmethod
    def get_brand(self) -> str | None:
        pass

    def is_approved_seller(self) -> bool:
        return True

    def create_item(self) -> dict:
        """Create a formatted item from raw data"""

        try:
            raw_id = self.get_raw_id()
            id = self.partner_name + "-" + raw_id

            # TODO: Add in color, size, condition, gender,brand, category
            # TODO: Add in raw fields
            # TODO: Get timestamp from file or table
            # TODO: transform to usd (or enforce currency conversion in the transform step)
            schema = CleanProductSchema()
            result = {
                "id": id,
                "secondhand_retailer": self.partner_name,
                "name": self.get_name(),
                "price_usd": self.get_price(),
                "product_url": self.get_product_url(),
                "img_url": self.get_img_url(),
                "additional_img_urls": (
                    self.get_additional_img_urls()
                    if self.get_additional_img_urls()
                    else []
                ),
                "description": self.get_description(),
                "raw_id": raw_id,
                "material": self.get_material(),
                "gender": self.get_gender(),
                "last_seen_at": self.get_last_seen_at(),
                "color": self.get_color(),
                "is_available": self.is_available(),
                "is_authenticated": self.is_authenticated(),
                "is_returnable": self.is_returnable(),
                "category": self.get_category_raw(),
                "category_broad": self.get_category_broad(),
                "raw_size": self.get_raw_size(),
                "raw_size_system": self.get_raw_size_system(),
                "condition": self.get_condition(),
                "brand": self.get_brand(),
                "is_approved_seller": self.is_approved_seller(),
            }
            return schema.load(drop_null_values_from_dict(result))
        except IndexError as e:
            print("Index Error: ", e)
            return None
        except Exception as e:
            print("Other Error: ", e)
            raise e


def get_category_broad_from_category_raw(category_raw: str) -> str | None:
    if category_raw is None:
        return None
    if category_raw is not None:
        category = category_raw.lower()
        category = (
            category.split("apparel & accessories", 1)[1]
            if "apparel & accessories" in category
            else category
        )
        for (
            standard_category_broad,
            pattern,
        ) in BASE_CLEANER_CATEGORY_BROAD_REGEX.items():
            if re.search(pattern, category):
                return standard_category_broad
    return CategoryBroad.CLOTHING.value


def extract_condition(text: str, condition_map: dict):
    condition_pattern = r"\b(?:" + "|".join(list(condition_map.keys())) + r")\b"
    matches = re.findall(condition_pattern, text, flags=re.IGNORECASE)
    if matches:
        mapped_matches = [condition_map.get(m.lower()) for m in matches]
        # Return the best type of condition in the list of matches
        if Condition.NEW_WITH_TAG.value in mapped_matches:
            return Condition.NEW_WITH_TAG.value
        if Condition.PRISTINE.value in mapped_matches:
            return Condition.PRISTINE.value
        if Condition.VERY_GOOD.value in mapped_matches:
            return Condition.VERY_GOOD.value
        if Condition.GOOD.value in mapped_matches:
            return Condition.GOOD.value
    return None
