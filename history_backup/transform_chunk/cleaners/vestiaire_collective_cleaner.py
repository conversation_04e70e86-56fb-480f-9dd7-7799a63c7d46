import re
from typing import override

from cleaners.base_cleaner import extract_condition
from cleaners.cj_cleaner import C<PERSON><PERSON>leaner
from models.product import Condition

VESTIAIRE_CONDITION_MAP = {
    "brand new": Condition.PRISTINE.value,
    "excellent": Condition.PRISTINE.value,
    "very good": Condition.VERY_GOOD.value,
    "good": Condition.GOOD.value,
    "fair": Condition.GOOD.value,
    "used": Condition.VERY_GOOD.value,
}


class VestiaireCollectiveCleaner(CJCleaner):

    def is_authenticated(self) -> bool:
        """Check if the product is authenticated. Default to False."""
        price = self.get_price()
        return price > 1000

    @override
    def get_gender(self) -> str:
        return self.get_value("product_type")

    def get_raw_size(self) -> str | None:
        size = self.get_value("size")
        if size is not None and size.startswith("Taille Unique"):
            return "Custom"
        return size

    @override
    def get_condition(self) -> str:
        return extract_condition(
            self.get_description(), VESTIAIRE_CONDITION_MAP
        ) or VESTIAIRE_CONDITION_MAP.get(self.get_value("condition"))

    def get_color(self) -> str:
        return self.get_value("color")

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return True
