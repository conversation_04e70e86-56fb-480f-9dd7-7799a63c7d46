import re

from cleaners.base_cleaner import extract_condition
from cleaners.impact_cleaner import ImpactCleaner
from models.product import Condition

BOPF_CONDITION_MAP = {
    "excellent": Condition.PRISTINE.value,
    "as good as new": Condition.PRISTINE.value,
    "like new": Condition.PRISTINE.value,
    "brand new": Condition.PRISTINE.value,
    "new with tag": Condition.NEW_WITH_TAG.value,
    "new w tag": Condition.NEW_WITH_TAG.value,
    "new w/ tag": Condition.NEW_WITH_TAG.value,
    "brand new with tag": Condition.NEW_WITH_TAG.value,
    "good": Condition.GOOD.value,
    "very good": Condition.VERY_GOOD.value,
}


class BopfCleaner(ImpactCleaner):

    def get_raw_size(self) -> str | None:
        size_column = self.get_value("Size")
        if size_column:
            return size_column
        description = self.get_value("Product Description")
        size_match = re.search(
            r"(<(strong|b|br)>)?Size ?:?(<\/(strong|b|br)>)?\s*:?\s*([^<]+)",
            description,
        )
        return size_match.group(5).strip() if size_match else None

    def get_condition(self) -> str | None:
        description = self.get_value("Product Description")
        return extract_condition(description, BOPF_CONDITION_MAP)

    def get_color(self):
        return self.get_value("Color")

    def get_brand(self) -> str:
        return self.get_value("Manufacturer")

    def is_authenticated(self) -> bool:
        """BOPF authenticates every single product"""
        return True

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return True

    def get_gender(self) -> str:
        gender = self.get_value("Gender")
        if not gender:
            labels = self.get_value("Labels")
            if labels:
                labels_lower = labels.lower()
                if (
                    re.search(r"\bmen\b", labels_lower)
                    or re.search(r"\bmens\b", labels_lower)
                    or re.search(r"\bmenswear\b", labels_lower)
                ):
                    return "men"
                else:
                    return "women"
        return gender
