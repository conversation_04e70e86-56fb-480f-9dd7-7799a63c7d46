import logging
from enum import Enum

from cleaners.base_cleaner import BaseCleaner, get_category_broad_from_category_raw
from models.product import Condition
from util.processors import extract_numeric_price


class Availability(Enum):
    """Enum class for Rakuten availability status"""

    IN_STOCK = "in-stock"
    OUT_OF_STOCK = "out-of-stock"
    PREORDER = "preorder"
    BACKORDER = "backorder"


class ShopPremiumOutletsCleaner(BaseCleaner):

    def is_available(self) -> bool:
        availability = self.get_value("availability")
        if availability not in Availability:
            logging.warning(
                f"Unknown availability status: {availability} for Shop Premium Outlets"
            )
            return False

        return availability == Availability.IN_STOCK.value

    def is_authenticated(self) -> bool:
        return True

    def get_name(self) -> str:
        return self.get_value("product_name")

    def get_price(self) -> float:
        sale_price = self.get_value("sales_price")
        if not sale_price:
            return extract_numeric_price(self.get_value("retail_price"))
        return extract_numeric_price(sale_price)

    def get_product_url(self) -> str:
        return self.get_value("product_url")

    def get_img_url(self) -> str:
        return self.get_value("product_image_url")

    def get_additional_img_urls(self) -> str | None:
        return None

    def get_description(self) -> str:
        return self.get_value("short_product_description")

    def get_material(self) -> str:
        return self.get_value("material")

    def get_raw_id(self) -> str:
        return self.get_value("product_id")

    def get_category(self) -> str:
        return self.get_value("secondary_category")

    """
        Rakuten doesn't provide a gender field by default on their product feeds.
        For SPO, attribute_6 corresponds to gender
    """

    def get_gender(self) -> str:
        return self.get_value("attribute_6")

    def get_category_raw(self) -> str:
        """
        Extract category from item
        Ebay always provides category as
        Clothing, Shoes & Accessories|Women|Women's Clothing|Shorts
        where Clothing, Shoes & Accessories is the first item since this is
        the categoryId we have requested in our getItemSnapshotFeed
        """
        return self.get_value("attribute_2")

    def get_category_broad(self) -> str:
        return get_category_broad_from_category_raw(self.get_category_raw())

    def get_condition(self) -> str:
        return Condition.PRISTINE.value

    def get_raw_size(self) -> str | None:
        return self.get_value("attribute_3")

    def get_color(self) -> str:
        return self.get_value("attribute_5")

    def get_brand(self) -> str | None:
        return self.get_value("brand")
