import re
from typing import override

from cleaners.base_cleaner import (
    extract_condition,
    get_category_broad_from_category_raw,
)
from cleaners.impact_cleaner import ImpactCleaner
from models.product import Condition

HEWI_CONDITION_MAP = {
    "with tags": Condition.NEW_WITH_TAG.value,
    "never worn": Condition.PRISTINE.value,
    "hardly ever worn": Condition.PRISTINE.value,
    "very good": Condition.VERY_GOOD.value,
}


class HewiCleaner(ImpactCleaner):
    def is_authenticated(self) -> bool:
        """HEWI authenticates every single product"""
        return True

    @override
    def get_condition(self) -> str:
        condition = self.get_value("Condition")
        if condition == "New":
            return Condition.PRISTINE.value
        return extract_condition(self.get_description(), HEWI_CONDITION_MAP)

    def get_category_broad(self) -> str:
        """Extract category broad from item"""
        return get_category_broad_from_category_raw(self.get_name())
