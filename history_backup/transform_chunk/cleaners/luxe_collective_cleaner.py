import re

from cleaners.impact_cleaner import ImpactCleaner
from models.product import Condition

LUXE_COLLECTIVE_CONDITION_REGEX = r"<strong> Condition:<\/strong> ([^<]+)<br>"
LUXE_COLLECTIVE_CONDITION_MAP = {
    "brand new": Condition.PRISTINE.value,
    "as new": Condition.PRISTINE.value,
    "excellent": Condition.PRISTINE.value,
    "very good": Condition.VERY_GOOD.value,
}


class LuxeCollectiveCleaner(ImpactCleaner):
    def is_authenticated(self) -> bool:
        """Luxe Collective authenticates every single product"""
        return True

    def get_category_raw(self) -> str:
        """Extract category from item"""
        return self.get_value("Labels")

    def get_raw_size(self) -> str | None:
        """
        Extract size from item
        """
        size = self.get_value("Size")
        if not size:
            description = self.get_description()
            size_match = re.search(
                r"<strong> ?Size: ?<\/strong>(?:<span>)?\s*([^<]+)(?:<\/span>)?.*?<br>",
                description,
                re.IGNORECASE,
            )
            return size_match.group(1).strip() if size_match else None
        return size

    def get_condition(self) -> str:
        description = self.get_description().lower()
        return get_condition_from_description_str(description)

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return True

    def get_gender(self) -> str:
        gender = self.get_value("Gender")
        if not gender:
            labels = self.get_value("Labels")
            if labels:
                labels_lower = labels.lower()
                if (
                    re.search(r"\bmen\b", labels_lower)
                    or re.search(r"\bmens\b", labels_lower)
                    or re.search(r"\bmenswear\b", labels_lower)
                ):
                    return "men"
                else:
                    return "women"
        return gender


def get_condition_from_description_str(description_str: str) -> str | None:
    condition_match = re.search(
        LUXE_COLLECTIVE_CONDITION_REGEX, description_str, re.IGNORECASE
    )
    return (
        LUXE_COLLECTIVE_CONDITION_MAP.get(condition_match.group(1).strip())
        if condition_match
        else None
    )
