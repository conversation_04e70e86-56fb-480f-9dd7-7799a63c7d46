from cleaners.cj_cleaner import <PERSON><PERSON><PERSON>leaner, Availability
from models.product import Condition

THE_LUX_CLOSET_CONDITION_MAP = {
    "pristine": Condition.PRISTINE.value,
    "excellent": Condition.PRISTINE.value,
    "new": Condition.PRISTINE.value,
    "good": Condition.VERY_GOOD.value,
    "fair": Condition.GOOD.value,
    "used": Condition.VERY_GOOD.value,
}


class TheLuxuryClosetCleanerCJ(CJCleaner):

    def is_authenticated(self) -> bool:
        """ "TheLuxuryCloset authenticates every single product"""
        return True

    def get_condition(self) -> str | None:
        return THE_LUX_CLOSET_CONDITION_MAP.get(self.get_value("condition"))
