import logging

from cleaners.base_cleaner import BaseCleaner
from util.processors import extract_numeric_price


class EtsyCleaner(BaseCleaner):
    def is_available(self) -> bool:
        """Check if the product is available for Etsy"""
        try:
            in_stock = int(self.get_value("in_stock"))
            stock_quantity = int(self.get_value("stock_quantity"))
        except Exception as e:
            stock_quantity = 0
            in_stock = 0
        return stock_quantity > 0 and in_stock != 0

    def is_authenticated(self) -> bool:
        """Etsy does not authenticate items"""
        return False

    def get_name(self) -> str:
        """Extract name from item for Etsy"""
        return self.get_value("product_name")

    def get_price(self) -> float:
        """Extract price from item for Etsy"""
        price = self.get_value("search_price") or self.get_value("product_price_old")
        return extract_numeric_price(price)

    def get_product_url(self) -> str:
        """Extract product URL from item for Etsy"""
        return self.get_value("aw_deep_link")

    def get_img_url(self) -> str:
        """Extract image URL from item for Etsy"""
        return self.get_value("aw_image_url")

    def get_additional_img_urls(self) -> list[str]:
        """Extract additional image URLs from item for Etsy"""
        additional_image_fields = [
            "aw_thumb_url",
            "alternate_image",
        ]
        return [
            self.get_value(field)
            for field in additional_image_fields
            if self.get_value(field)
        ]

    def get_description(self):
        """Extract description from item for Etsy"""
        return self.get_value("description")

    def get_material(self) -> str:
        """Extract material from item for Etsy"""
        return self.get_value("Fashion:material")

    def get_raw_id(self) -> str:
        """Extract raw ID from item for Etsy"""
        return self.get_value("aw_product_id")

    def get_gender(self) -> str:
        return self.get_value("custom_2")

    def get_category_raw(self) -> str:
        """Extract category from item"""
        return self.get_value("product_type")

    def get_raw_size(self) -> str | None:
        return self.get_value("Fashion:size")

    def get_condition(self) -> str | None:
        """
        No condition tags. Can leave blank
        """
        return None

    def get_color(self) -> str:
        """Extract color from item"""
        return self.get_value("colour")

    def get_brand(self) -> str | None:
        return None
