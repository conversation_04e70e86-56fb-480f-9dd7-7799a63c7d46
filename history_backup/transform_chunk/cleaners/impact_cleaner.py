import re

from cleaners.base_cleaner import (
    <PERSON><PERSON><PERSON><PERSON>,
    BASE_CLEANER_CATEGORY_BROAD_REGEX,
    get_category_broad_from_category_raw,
)
from enum import Enum

from models.product import CategoryBroad, Condition
from util.processors import extract_numeric_price
import logging

IMPACT_CONDITION_MAP = {
    "used": Condition.GOOD.value,
    "new": Condition.PRISTINE.value,
}


class Availability(Enum):
    """Enum class for Impact availability status"""

    IN_STOCK = "Y"
    OUT_OF_STOCK = "N"
    # BACKORDER = "BackOrder"
    # LIMITED_AVAILABILITY = "LimitedAvailability"
    # PREORDER = "PreOrder"


AVAILABLE_STATUSES = {member.value for member in Availability}


class ImpactCleaner(BaseCleaner):
    def is_available(self) -> bool:
        """Check if the product is available for Impact"""
        availability = self.get_value("Stock Availability")
        if availability is None or availability.strip() == "":
            return True

        if availability is not None and availability not in AVAILABLE_STATUSES:
            return False

        return availability == Availability.IN_STOCK.value

    def get_name(self) -> str:
        """Extract name from item for Impact"""
        return self.get_value("Product Name")

    def get_price(self) -> float:
        """Extract price from item for Impact"""
        price = self.get_value("Current Price") or self.get_value("Original Price")
        return extract_numeric_price(price)

    def get_product_url(self) -> str:
        """Extract product URL from item for Impact"""
        return self.get_value("Product URL")

    def get_img_url(self) -> str:
        """Extract image URL from item for Impact"""
        return self.get_value("Image URL")

    def get_additional_img_urls(self) -> list[str]:
        """Extract additional image URLs from item for Impact"""
        additional_image_links = []
        i = 1
        while True:
            link = self.get_value(f"Alternative Image URL {i}")
            if link is None or link.strip() == "":
                break
            additional_image_links.append(link)
            i += 1
        return additional_image_links

    def get_description(self):
        """Extract description from item for Impact"""
        return self.get_value("Product Description")

    def get_material(self) -> str:
        """Extract material from item for Impact"""
        return self.get_value("Material")

    def get_raw_id(self) -> str:
        """Extract raw ID from item for Impact"""
        return self.get_value("Unique Merchant SKU")

    def get_color(self) -> str:
        """Extract color from item for Impact"""
        return self.get_value("Color")

    def get_gender(self) -> str:
        return self.get_value("Gender")

    def get_category_raw(self) -> str:
        """Extract category from item"""
        return self.get_value("Category")

    def get_raw_size(self) -> str:
        return self.get_value("Size")

    def get_condition(self) -> str | None:
        """Extract condition from item"""
        return IMPACT_CONDITION_MAP.get(self.get_value("Condition"))

    def get_brand(self) -> str:
        return self.get_value("Manufacturer")
