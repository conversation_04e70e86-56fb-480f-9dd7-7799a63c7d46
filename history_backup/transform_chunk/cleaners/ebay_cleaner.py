import base64
from datetime import datetime
import re
from enum import Enum

from cleaners.base_cleaner import (
    <PERSON><PERSON><PERSON><PERSON>,
    BASE_CLEANER_CATEGORY_BROAD_REGEX,
)
from models.product import Condition
from util.processors import extract_numeric_price
import logging


class EbayAvailability(Enum):
    """Enum class for Ebay availability status"""

    AVAILABLE = "AVAILABLE"
    UNAVAILABLE = "UNAVAILABLE"
    TEMPORARILY_UNAVAILABLE = "TEMPORARILY_UNAVAILABLE"


EBAY_AVAILABLE_STATUSES = {member.value for member in EbayAvailability}

EBAY_CONDITION_MAP = {
    "NEW": Condition.NEW_WITH_TAG.value,
    "NEW_OTHERS": Condition.PRISTINE.value,
    "NEW_WITH_DEFECTS": Condition.VERY_GOOD.value,
    "USED": Condition.GOOD.value,
}


class EbayCleaner(BaseCleaner):

    def is_available(self) -> bool:
        availability = self.get_value("Availability")
        if availability not in EBAY_AVAILABLE_STATUSES:
            logging.warning(f"Unknown availability status: {availability}")
            return False

        return availability == EbayAvailability.AVAILABLE.value

    def is_authenticated(self) -> bool:
        """Ebay authenticates all items greater than or equal to $500"""
        if self.get_price() is not None:
            return self.get_price() >= 500
        return False

    def get_name(self) -> str:
        return self.get_value("Title").replace("\x00", "")

    def get_price(self) -> float:
        price = self.get_value("PriceValue")
        return extract_numeric_price(price)

    def get_product_url(self) -> str:
        return ebay_product_url_affiliate_like_substitution(
            self.get_value("ItemAffiliateWebUrl")
        )

    def get_img_url(self) -> str:
        return self.get_value("ImageUrl")

    def get_additional_img_urls(self) -> list[str]:
        additional_img_urls = self.get_value("AdditionalImageUrls")
        return additional_img_urls.split("|") if additional_img_urls else []

    def get_description(self) -> str:
        if self.get_value("Description"):
            return self.get_value("Description").replace("\x00", "")
        return ""

    def get_material(self) -> str:
        return self.get_value("Material")

    def get_raw_id(self) -> str:
        return self.get_value("ItemId")

    def get_category(self) -> str:
        return self.get_value("Category")

    def get_gender(self) -> str:
        return self.get_value("Gender") or self.get_category()

    def get_last_seen_at(self) -> str:
        timestamp = self.file_name.split("_")[1]
        return str(datetime.fromtimestamp(int(timestamp)))

    def get_category_raw(self) -> str:
        """
        Extract category from item
        Ebay always provides category as
        Clothing, Shoes & Accessories|Women|Women's Clothing|Shorts
        where Clothing, Shoes & Accessories is the first item since this is
        the categoryId we have requested in our getItemSnapshotFeed
        """
        return self.get_value("Category")

    def get_category_broad(self) -> str:
        return get_ebay_category_broad_from_category_raw(self.get_category_raw())

    def get_condition(self) -> str | None:
        return EBAY_CONDITION_MAP.get(self.get_value("Condition"))

    def get_raw_size(self) -> str | None:
        size = self.get_value("Size")
        if not size:
            localized_aspects = self.get_value("LocalizedAspects")
            if localized_aspects:
                return extract_size_from_localized_aspect(localized_aspects)
        return size

    def get_color(self) -> str:
        return self.get_value("Color")

    def get_brand(self) -> str | None:
        return self.get_value("Brand")

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return self.get_value("ReturnsAccepted") in ["TRUE", "true", "True"]

    def is_approved_seller(self) -> bool:
        return self.get_value("SellerTrustLevel") in ["TOP_RATED"]


def get_ebay_category_broad_from_category_raw(category_raw: str) -> str | None:
    try:
        category_raw = category_raw.split("|", 1)[1]
    except IndexError:
        return None
    if category_raw is not None:
        category = category_raw.lower()
        for (
            standard_category_broad,
            pattern,
        ) in BASE_CLEANER_CATEGORY_BROAD_REGEX.items():
            if re.search(pattern, category):
                return standard_category_broad


def ebay_product_url_affiliate_like_substitution(link: object) -> object:
    return link.replace("CAMPAIGNID", "5339031575").replace("&customid=CUSTOMID", "")


def extract_size_from_localized_aspect(localized_aspects: str):
    if (
        not isinstance(localized_aspects, str) or localized_aspects.strip() == ""
    ):  # Check if it's not a string or if it's empty
        return None  # Return None for non-strings or empty strings

    decoded_aspects = []
    parts = localized_aspects.split("|")

    for part in parts:
        if ":" in part:  # If the part contains a name:value pair
            aspects = part.split(";")
            decoded_part = []
            for aspect in aspects:
                if ":" in aspect:
                    name, value = aspect.split(":")
                    decoded_name = decode_base64(name)
                    decoded_value = decode_base64(value)
                    decoded_part.append(f"{decoded_name}:{decoded_value}")
            decoded_aspects.append(";".join(decoded_part))
        else:
            if part:  # For the label part
                decoded_label = decode_base64(part)
                decoded_aspects.append(decoded_label)

    decoded = "|".join(decoded_aspects)
    return extract_values(
        decoded,
        keys_of_interest=[
            "US Shoe Size (Youth)",
            "US Shoe Size (Women's)",
            "US Shoe Size (Men's)",
            "Size (Men's)",
            "Size (Women's)",
            "Size",
        ],
    )


def extract_values(aspect_string, keys_of_interest):
    """
    Extracts specific value from key-value pairs from a localized aspect string.
        input: Decade:1980s;Size (Men's):S;Size Type:Regular;Color:White;Style:Graphic Tee;Material:Cotton Blend;
        output: S
    """
    for key in keys_of_interest:
        # Regular expression pattern to match the key and capture the value
        pattern = re.compile(rf"{re.escape(key)}:\s*([^;]+)")
        match = pattern.search(aspect_string)

        if match:
            return match.group(1)


def decode_base64(encoded_str):
    """Decodes a Base64 encoded string."""
    return base64.b64decode(encoded_str).decode("utf-8")
