import re

from cleaners.base_cleaner import BaseCleaner, extract_condition
from models.product import Condition
from util.processors import extract_numeric_price

POSHMARK_CONDITION_MAP = {
    "used": Condition.VERY_GOOD.value,
    "new": Condition.PRISTINE.value,
    "new with tag": Condition.NEW_WITH_TAG.value,
    "excellent": Condition.PRISTINE.value,
    "very good": Condition.VERY_GOOD.value,
    "good": Condition.GOOD.value,
}


class PoshmarkCleaner(BaseCleaner):
    def is_available(self) -> bool:
        """Check if the product is available for Poshmark"""
        # NOTE: Poshmark does not have an "availability" field
        return True

    def is_authenticated(self) -> bool:
        """Poshmark authenticates all items greater than or equal to $500"""
        return self.get_price() >= 500

    def get_name(self) -> str:
        """Extract name from item for Poshmark"""
        return self.get_value("title")

    def get_price(self) -> float:
        """Extract price from item for Poshmark"""
        price = self.get_value("price")
        return extract_numeric_price(price)

    def get_product_url(self) -> str:
        """Extract product URL from item for Poshmark"""
        return self.get_value("url")

    def get_img_url(self) -> str:
        """Extract image URL from item for Poshmark"""
        return self.get_value("image_url")

    def get_additional_img_urls(self) -> list[str]:
        """Extract additional image URLs from item for Poshmark"""
        # NOTE: Poshmark does not have additional image URLs
        return []

    def get_description(self):
        """Extract description from item for Poshmark"""
        return self.get_value("description")

    def get_material(self) -> str:
        """Extract material from item for Poshmark"""
        # NOTE: Poshmark does not have a "material" field currently
        return None

    def get_raw_id(self) -> str:
        """Extract raw ID from item for Poshmark"""
        return self.get_value("id")

    def get_gender(self) -> str:
        return self.get_value("gender")

    def get_category_raw(self) -> str:
        """Extract category from item"""
        return self.get_value("category")

    def get_raw_size(self) -> str | None:
        return self.get_value("size")

    def get_condition(self) -> str | None:
        return extract_condition(
            self.get_description(), POSHMARK_CONDITION_MAP
        ) or POSHMARK_CONDITION_MAP.get(self.get_value("condition"))

    def get_color(self) -> str:
        """Extract color from item"""
        return self.get_value("colors")

    def get_brand(self) -> str:
        return self.get_value("brand")

    def is_returnable(self) -> bool:
        """Check if the product is returnable. Default to False."""
        return True

    def is_approved_seller(self) -> bool:
        return self.get_value("posh_pro_status") in ["posh_pro"]
