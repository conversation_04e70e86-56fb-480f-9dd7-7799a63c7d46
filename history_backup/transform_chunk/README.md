# Transform Chunk

This directory contains the code backing our Transform Chunk function, details [here](https://docs.google.com/document/d/14lckij3reEJ9c6UBEai8-DBtyPAszP4jQSbfzdLV33Q/). For implementing new methods, you can also refer to the [legacy data processing package](https://github.com/phiadev/phia-data-processing-library).

## Development

From the transform-chunk directory, run
`pip install -r requirements-dev.txt`
To run tests, from the tests directory, run
`python -m pytest`

### Constants Module

The `constants` module within the `data-pipeline` directory provides shared enumerations and constants to be used across different cloud functions in the datapipeline.
