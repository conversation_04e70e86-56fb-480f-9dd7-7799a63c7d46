import re
from models.product import CleanProductSchema, Gender
from util.stat_logger import Stat<PERSON>og<PERSON>, NON_UNIFIED_GENDER_COUNT_KEY

GENDER_REGEXES = {
    Gender.MEN.value: r"\b(?:men|mens|male|man)\b",
    Gender.WOMEN.value: r"\b(?:women|womens|female|woman)\b",
    Gender.UNISEX.value: r"\bunisex\b",
}


class GenderCurator:

    def __init__(self, item: CleanProductSchema):
        self.item = item

    def get_gender(self) -> str | None:
        gender = self.item.get("gender")
        if gender is not None:
            gender = gender.lower()
            for standardized_gender, pattern in GENDER_REGEXES.items():
                if re.search(pattern, gender):
                    return standardized_gender
        StatLogger.add_non_unified_count(NON_UNIFIED_GENDER_COUNT_KEY)
        return None
