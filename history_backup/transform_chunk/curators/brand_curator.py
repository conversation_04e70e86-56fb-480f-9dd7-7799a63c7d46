from models.product import CleanProductSchema, Brand
from rapidfuzz import process

from util.stat_logger import StatLogger, NON_UNIFIED_BRAND_COUNT_KEY

COLLAB_TOKENS = [" x ", " by ", " for "]
BRAND_LOWER_CASE_MAP = {}
BRAND_NAME_ALTERNATIVE_MAPPINGS = {
    "chlo\u00e9": "<PERSON>",
    "aime leon dore": "Aimé Leon Dore",
    "polo by ralph lauren": "Ralph Lauren",
    "alaia": "Ala\u00efa",
}


class BrandCurator:
    def __init__(
        self, item: CleanProductSchema, brand_id_mapper: dict, brand_list: list
    ):
        self.item = item
        self.retailer = self.item.get("secondhand_retailer")
        self.brand_id_mapper = brand_id_mapper
        self.brand_list = brand_list

    def load_brands(self, primary_brand, raw_brand, brands):
        return Brand().load(
            {
                "primary_brand": primary_brand,
                "raw_brand": raw_brand,
                "brands": brands,
                "id": self.brand_id_mapper.get(primary_brand.lower()),
            }
        )

    def get_brand(self) -> Brand:
        if not self.item or "brand" not in self.item or self.item["brand"] is None:
            return self.load_brands(
                primary_brand="Unavailable", raw_brand=None, brands=[]
            )
        raw_brand = self.item["brand"]
        raw_brand_lower = raw_brand.lower()
        if raw_brand in [
            "unbranded",
            "unknown",
            "unavailable",
            "none",
            "n/a",
            "",
            None,
        ]:
            return self.load_brands(
                primary_brand="Unavailable", raw_brand=raw_brand, brands=[]
            )

        for b in self.brand_list:
            BRAND_LOWER_CASE_MAP[b.lower().strip()] = b.strip()

        for b_alt, brand_name in BRAND_NAME_ALTERNATIVE_MAPPINGS.items():
            BRAND_LOWER_CASE_MAP[b_alt] = brand_name

        if raw_brand_lower in BRAND_LOWER_CASE_MAP.keys():
            return self.load_brands(
                primary_brand=BRAND_LOWER_CASE_MAP[raw_brand_lower],
                raw_brand=raw_brand,
                brands=[BRAND_LOWER_CASE_MAP[raw_brand_lower]],
            )
        for token in COLLAB_TOKENS:
            if token in raw_brand_lower:
                brands = raw_brand_lower.split(token)
                brand_list = []
                for b in brands:
                    if b in BRAND_LOWER_CASE_MAP.keys():
                        brand_list.append(BRAND_LOWER_CASE_MAP[b])
                    else:
                        brand_matches = find_match(b)
                        if len(brand_matches) > 0:
                            brand_list.extend(brand_matches)
                if len(brand_list) > 0:
                    return self.load_brands(
                        primary_brand=brand_list[0],
                        raw_brand=raw_brand,
                        brands=brand_list,
                    )
                return self.load_brands(
                    primary_brand="Unavailable",
                    raw_brand=None,
                    brands=[],
                )

        matches = find_match(raw_brand_lower)
        if len(matches) > 0:
            return self.load_brands(
                primary_brand=matches[0], raw_brand=raw_brand, brands=matches
            )

        StatLogger.add_non_unified_count(NON_UNIFIED_BRAND_COUNT_KEY)
        return self.load_brands(
            primary_brand="Unavailable",
            raw_brand=raw_brand,
            brands=[],
        )


def find_match(brand) -> list:
    processed = process.extract(
        brand, BRAND_LOWER_CASE_MAP.keys(), score_cutoff=90, limit=3
    )
    if len(processed) >= 1:
        return [BRAND_LOWER_CASE_MAP[b[0]] for b in processed]
    return []
