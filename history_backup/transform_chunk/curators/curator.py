import logging

from assets.blacklisted_raw_categories import BLACKLISTED_RAW_CATEGORIES
from assets.profanity_tokens import PROFANITY_TOKENS
from curators.attribute_id_curator import AttributeIdCurator
from curators.gender_curator import GenderCurator
from curators.product_url_curator import ProductUrlCurator
from curators.size_curator import Size<PERSON><PERSON>tor
from field_id_mappers.field_id_mapper import FieldIdMapper
from models.product import CleanProductSchema, ProductSchema
from marshmallow import ValidationError
from curators.brand_curator import BrandCurator
from curators.color_curator import ColorCurator
from curators.image_quality_curator import ImageQualityCurator


class Curator:
    def __init__(
        self, item: CleanProductSchema, field_id_mapper: FieldIdMapper, filename=""
    ):
        self.item = item
        self.partner = item.get("secondhand_retailer")
        self.field_id_mapper = field_id_mapper
        self.brand_curator = BrandCurator(
            self.item, field_id_mapper.brand_id_mapper, field_id_mapper.brand_list
        )
        self.color_curator = ColorCurator(self.item)
        self.image_quality_curator = ImageQualityCurator(self.item)
        self.gender_curator = GenderCurator(self.item)
        self.product_url_curator = ProductUrlCurator(self.item)
        self.size_curator = SizeCurator(self.item, field_id_mapper.size_id_mapper)
        self.clean_category_curator = AttributeIdCurator(
            self.item, field_id_mapper.attribute_id_mapper
        )
        self.filename = filename

    def __drop_null_values(self, dct: dict) -> dict:
        output = {}
        for key, value in dct.items():
            if value is not None:
                output[key] = value
        return output

    def curate(self) -> ProductSchema | None:
        data = {}
        try:
            # TODO: Change to size category
            if self.should_filter_product():
                return None
            category_broad = self.item.get("category_broad")
            gender = self.gender_curator.get_gender()
            primary_color, colors = self.color_curator.get_colors()
            size = self.size_curator.get_size(category_broad=category_broad, gen=gender)
            brand = self.brand_curator.get_brand()
            image_quality = self.image_quality_curator.get_image_quality()
            raw_category = self.item.get("category")
            attribute_list = (
                self.clean_category_curator.get_attribute_display_name_and_id(
                    raw_category
                )
            )
            transformed_data = {
                "id": self.item["id"],
                "name": self.item["name"],
                "description": self.item.get("description"),
                "price_usd": self.item.get("price_usd"),
                "product_url": self.product_url_curator.get_product_url(),
                "img_url": self.product_url_curator.get_img_url(),
                "additional_img_urls": self.product_url_curator.get_additional_img_urls(),
                "firsthand_retailer_id": self.item.get("firsthand_retailer"),
                "secondhand_retailer_id": self.item.get("secondhand_retailer"),
                "category_raw": raw_category,
                "category_broad": category_broad,
                "attribute_list": attribute_list,
                "size": size,
                "condition": self.item.get("condition"),
                "gender": gender,
                "is_authenticated": self.item.get("is_authenticated"),
                "is_free_shipping": self.item.get("is_free_shipping"),
                "is_returnable": self.item.get("is_returnable"),
                "is_vintage": self.item.get("is_vintage"),
                "is_available": self.item.get("is_available"),
                "brand": brand,
                "primary_color": primary_color,
                "image_quality": image_quality,
                "colors": colors,
                "raw_color": self.item.get("color"),
                "created_at": str(self.item["created_at"]),
                "updated_at": str(self.item["updated_at"]),
                "last_seen_at": str(self.item["last_seen_at"]),
                "sold_at": self.item.get("sold_at"),
                "meta": {
                    "filepath": self.filename,
                },
            }
            data = self.__drop_null_values(transformed_data)
            return ProductSchema().load(data)
        except ValidationError as err:
            print(f"Validation errors occurred: {err.messages} for data: {data}")
            raise

    def should_filter_product(self):
        return (
            self.has_profanity()
            or self.is_price_outside_valid_range()
            or self.is_blacklisted_raw_category()
            or self.is_not_approved_seller()
        )

    def has_profanity(self):
        # If product's name or description contains a profanity token. We filter it out.
        name_description = (
            self.item.get("name", "").lower() + self.item.get("description", "").lower()
        )
        for token in PROFANITY_TOKENS:
            if token.lower() in name_description:
                return True
        return False

    def is_price_outside_valid_range(self):
        product_id = self.item["id"]
        price = self.item.get("price_usd")
        try:
            float_price = float(price)
        except (ValueError, TypeError):
            logging.warning(
                f"Price is not a valid number. {price} for product {product_id}"
            )
            return False

        if price is None:
            logging.warning(f"Price is not defined in the item for {product_id}")
            return False

        return not (10.00 <= float_price <= 1000000.00)

    def is_blacklisted_raw_category(self):
        return self.item.get("category") in BLACKLISTED_RAW_CATEGORIES.get(
            self.partner, {}
        )

    def is_not_approved_seller(self):
        return not self.item.get("is_approved_seller")
