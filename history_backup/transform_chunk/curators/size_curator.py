import re
import logging

from field_id_mappers.size_id_mapper import SizeIdMapper
from models.product import CleanProductSchema, CategoryBroad, Si<PERSON>, Gender
from util.stat_logger import StatLogger, NON_UNIFIED_SIZE_COUNT_KEY

VALID_SIZE_SYSTEMS = [
    "GB",
    "UK",
    "EUR",
    "FR",
    "IT",
    "EU",
    "US",
    "KR",
    "INTERNATIONAL",
    "0-5",
    "0-6",
    "EU(TOURDECOU/COLLAR)",
    "MM",
]

SIZE_STANDARD_MAPPINGS = {
    "Italy/France": "IT",
    "Italy": "IT",
    "IT": "IT",
    "France": "FR",
    "FR": "FR",
    "KR": "KR",
    "UK": "UK",
    "United Kingdon": "UK",
    "United States": "US",
    "GB": "GB",
    "EUR": "EUR",
    "EU": "EU",
    "US": "US",
    "INTERNATIONAL": "INTERNATIONAL",
    "0-5": "0-5",
    "0-6": "0-6",
    "EU(TOURDECOU/COLLAR)": "EU(TOURDECOU/COLLAR)",
    "MM": "MM",
}

VALID_GENERIC_SIZES = {
    "xxs": "xxs",
    "xs": "xs",
    "xsmall": "xs",
    "s": "s",
    "small": "s",
    "m": "m",
    "medium": "m",
    "l": "l",
    "large": "l",
    "xl": "xl",
    "xlarge": "xl",
    "2xl": "2xl",
    "xxl": "xxl",
    "xxxl": "xxxl",
    "3xl": "3xl",
    "4xl": "4xl",
    "5xl": "5xl",
    "6xl": "6xl",
    "custom": "custom",
    "one size": "one size",
}


class SizeCurator:

    def __init__(self, item: CleanProductSchema, size_id_mapper: dict):
        self.item = item
        self.retailer = self.item.get("secondhand_retailer")
        self.size_id_mapper = size_id_mapper

    def get_size(self, category_broad, gen) -> Size:
        gender = gen.lower() if gen else ""
        raw_size = self.item.get("raw_size")
        raw_size_system = self.item.get("raw_size_system")
        mapped_size_system = SIZE_STANDARD_MAPPINGS.get(raw_size_system, "")
        us_size_value = self.get_us_size_value(
            raw_size, mapped_size_system, category_broad, gender
        )
        size_id = (
            self.size_id_mapper.get(
                f"{gender.lower()}_{category_broad}_{us_size_value.lower()}"
            )
            if gender and us_size_value
            else None
        )
        return Size().load(
            {
                "raw_size_value": raw_size,
                "raw_size_system": raw_size_system,
                "mapped_size_system": mapped_size_system,
                "us_size_value": us_size_value,
                "id": size_id,
            }
        )

    def get_us_size_value(
        self, raw_size, raw_size_system, category_broad, gender
    ) -> str | None:

        if category_broad == CategoryBroad.ACCESSORIES.value:
            return raw_size

        if raw_size is None:
            return None

        size_system, size_value = extract_size_and_system(
            raw_size, raw_size_system, gender, category_broad
        )

        """
        If size_system is None then we did not extract it from the raw size value
        and it was not provided as raw_size_system an additional field. 
        So we first check if the category is Shoes and if it is outside the US range
        for show sizes we default to IT. Otherwise we default to US
        """
        if not size_system and category_broad == CategoryBroad.SHOES.value:
            if is_float(size_value) and float(size_value) > 30:
                size_system = "IT"

        if size_system is None:
            size_system = "US"

        size_value = size_value.lower()
        size_system = size_system.upper()

        size_parts = size_value.split("-")
        if len(size_parts) == 2:
            size_value = size_parts[1]

        if category_broad == CategoryBroad.RINGS.value:
            # How do we want to handle rings?
            if is_float(size_value):
                size_float_value = float(size_value)
                if 14.8 < size_float_value < 22:
                    return convert_to_us_ring_size(
                        size_float_value, size_chart_conversion_diameter_to_us
                    )
                if 46 < size_float_value <= 68.5:
                    return convert_to_us_ring_size(
                        size_float_value, size_chart_conversion_circumference_to_us
                    )
            return None

        if size_system not in VALID_SIZE_SYSTEMS:
            StatLogger.add_non_unified_count(NON_UNIFIED_SIZE_COUNT_KEY)
            return size_value

        # If size system is US, we don't need to convert based
        # on the product category, we can just return the value
        if size_system == "US":
            # TODO: We want to check that size_value is in
            #  accepted list of US size values for the provided category
            return size_value

        if category_broad == CategoryBroad.PANTS.value:
            # Prioritize these two country specific pants conversions before using regular clothing conversions
            if size_system == "FR":
                us_size_value = fr_to_us_pants_size.get(size_value)
                if us_size_value is not None:
                    return us_size_value

            if size_system == "KR":
                return size_value

            if gender == Gender.WOMEN.value.lower():
                if size_system == "FR":
                    us_size_value = fr_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "IT":
                    us_size_value = it_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system in ["UK"]:
                    us_size_value = uk_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system in ["EUR", "EU", "INTERNATIONAL"]:
                    us_size_value = international_to_us_womens_pants.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "0-5" or size_system == "0-6":
                    us_size_value = zero_to_five_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value

            if gender == Gender.MEN.value.lower():
                if size_system in ["FR", "IT"]:
                    us_size_value = eu_to_us_mens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system in ["UK", "GB"]:
                    return size_value
                if size_system in ["EUR", "EU", "INTERNATIONAL"]:
                    us_size_value = international_to_us_mens_pants.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "0-5" or size_system == "0-6":
                    us_size_value = zero_to_five_to_us_mens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value

        if category_broad == CategoryBroad.SHOES.value:
            size_value = size_value.replace(",", ".")
            if gender == Gender.MEN.value.lower():
                if size_system in ["EUR", "EU", "FR", "IT"]:
                    us_size_value = eu_to_us_mens_shoes.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system in ["UK", "GB"]:
                    us_size_value = uk_to_us_mens_shoes.get(size_value)
                    if us_size_value is not None:
                        return us_size_value

            if gender == Gender.WOMEN.value.lower():
                if size_system in ["EUR", "EU", "FR"]:
                    us_size_value = eu_to_us_womens_shoes.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "IT":
                    us_size_value = it_to_us_womens_shoes.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system in ["UK", "GB"]:
                    us_size_value = uk_to_us_womens_shoes.get(size_value)
                    if us_size_value is not None:
                        return us_size_value

        if category_broad == CategoryBroad.CLOTHING.value:
            if gender == Gender.MEN.value.lower():
                if size_system in ["FR", "IT"]:
                    us_size_value = eu_to_us_mens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system in ["UK", "GB"]:
                    return size_value
                if size_system in ["EUR", "EU", "INTERNATIONAL"]:
                    us_size_value = international_to_us_mens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "0-5" or size_system == "0-6":
                    us_size_value = zero_to_five_to_us_mens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "EU(TOURDECOU/COLLAR)":
                    us_size_value = eu_to_us_mens_clothing_collar.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "KR":
                    us_size_value = kr_to_us_mens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value

            if gender == Gender.WOMEN.value.lower():
                if size_system == "FR":
                    us_size_value = fr_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "IT":
                    us_size_value = it_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system in ["UK"]:
                    us_size_value = uk_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system in ["EUR", "EU", "INTERNATIONAL"]:
                    us_size_value = international_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "0-5" or size_system == "0-6":
                    us_size_value = zero_to_five_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value
                if size_system == "KR":
                    us_size_value = kr_to_us_womens_clothing.get(size_value)
                    if us_size_value is not None:
                        return us_size_value

        StatLogger.add_non_unified_count(NON_UNIFIED_SIZE_COUNT_KEY)
        return None


def extract_size_and_system(raw_size, size_system, gender, category) -> tuple[str, str]:

    system, size = simple_extract(raw_size)
    if system is not None and size is not None:
        return system, size

    # If raw_size is a float, just return that
    if is_float(raw_size):
        return size_system, raw_size

    # If raw size is a clean size xxs - 6xl, just return that, but converted to a us clothing size
    cleaned_raw_size = raw_size.lower().strip()
    if cleaned_raw_size in VALID_GENERIC_SIZES.keys():
        # make sure sizes like "medium" are mapped to "m"
        cleaned_raw_size = VALID_GENERIC_SIZES.get(cleaned_raw_size)
        if gender == Gender.WOMEN.value.lower():
            if category == CategoryBroad.CLOTHING.value:
                return "US", international_to_us_womens_clothing.get(
                    cleaned_raw_size, cleaned_raw_size
                )
            if category == CategoryBroad.PANTS.value:
                return "US", international_to_us_womens_pants.get(
                    cleaned_raw_size, cleaned_raw_size
                )
        if gender == Gender.MEN.value.lower():
            if category == CategoryBroad.CLOTHING.value:
                return "US", international_to_us_mens_clothing.get(
                    cleaned_raw_size, cleaned_raw_size
                )
            if category == CategoryBroad.PANTS.value:
                return "US", international_to_us_mens_pants.get(
                    cleaned_raw_size, cleaned_raw_size
                )

    # Attempt to parse size_system from more complicated size values like EU 38.5 UK 5.5,, etc
    sizes = re.findall(
        r"([A-Z]+|\d+(?:\.\d+)?|\b(?:XXS|XS|S|M|L|XL|XXL|XXXL)\b)\s?([A-Z]+|\d+(?:\.\d+)?)",
        raw_size,
        re.IGNORECASE,
    )

    # If we don't match these formats, just return the size_system and value
    if not sizes:
        return size_system, raw_size

    # If there are multiple matches, e.g. EU 38.5 UK 5.5, find the US match
    # if it exists. Otherwise take the first value in the list
    us_sizes = [size for size in sizes if size[0] == "US"]
    if us_sizes:
        ret = us_sizes[0]
        # The regex matches differently depending on whether we get "'UK 34' or '34 UK',
        # we want to return the size_system first, then the size value second
        if is_float(ret[0]) or ret[1].lower().strip() == "international":
            return ret[1], ret[0]
        return ret
    else:
        ret = sizes[0]
        if is_float(ret[0]) or ret[1].lower().strip() == "international":
            return ret[1], ret[0]
        return ret


def simple_extract(size_value) -> tuple[str, str]:
    # Attempt to parse simple raw_sizes like
    # "M US", "XL INTERNATIONAL", "42 IT", etc
    size_parts = size_value.split(" ", 1)
    if (
        size_parts
        and len(size_parts) == 2
        and size_parts[1].upper().replace(" ", "") in VALID_SIZE_SYSTEMS
    ):
        return size_parts[1].upper().replace(" ", ""), size_parts[0]
    else:
        return None, None


def is_float(s: str) -> bool:
    if s is None:
        return False
    try:
        float(s)
        return True
    except ValueError:
        return False


# Mapping tables for shoe size conversion
eu_to_us_mens_shoes = {
    "36.5": "4",
    "37": "4",
    "37.5": "4.5",
    "38": "5",
    "38.5": "5.5",
    "39": "6",
    "39.5": "6.5",
    "40": "7",
    "40.5": "7.5",
    "41": "8",
    "41.5": "8.5",
    "42": "9",
    "42.5": "9.5",
    "43": "10",
    "43.5": "10.5",
    "44": "11",
    "44.5": "11.5",
    "45": "12",
    "45.5": "12.5",
    "46": "13",
    "46.5": "13.5",
    "47": "14",
    "47.5": "14.5",
    "48": "15",
}

uk_to_us_mens_shoes = {
    "3": "4",
    "3.5": "4.5",
    "4": "5",
    "4.5": "5.5",
    "5": "6",
    "5.5": "6.5",
    "6": "7",
    "6.5": "7.5",
    "7": "8",
    "7.5": "8.5",
    "8": "9",
    "8.5": "9.5",
    "9": "10",
    "9.5": "10.5",
    "10": "11",
    "10.5": "11.5",
    "11": "12",
    "11.5": "12.5",
    "12": "13",
    "12.5": "13.5",
    "13": "14",
    "13.5": "14.5",
    "14": "15",
}

eu_to_us_womens_shoes = {
    "34": "3",
    "34.5": "3.5",
    "35": "4",
    "35.5": "4.5",
    "36": "5",
    "36.5": "5.5",
    "37": "6",
    "37.5": "6.5",
    "38": "7",
    "38.5": "7.5",
    "39": "8",
    "39.5": "8.5",
    "40": "9",
    "40.5": "9.5",
    "41": "10",
    "41.5": "10.5",
    "42": "11",
    "42.5": "11.5",
    "43": "12",
    "43.5": "12",
}

uk_to_us_womens_shoes = {
    "2": "4",
    "2.5": "4.5",
    "3": "5",
    "3.5": "5.5",
    "4": "6",
    "4.5": "6.5",
    "5": "7",
    "5.5": "7.5",
    "6": "8",
    "6.5": "8.5",
    "7": "9",
    "7.5": "9.5",
    "8": "10",
    "8.5": "10.5",
    "9": "11",
}

it_to_us_womens_shoes = {
    "33": "3",
    "33.5": "3.5",
    "34": "4",
    "34.5": "4.5",
    "35": "5",
    "35.5": "5.5",
    "36": "6",
    "36.5": "6.5",
    "37": "7",
    "37.5": "7.5",
    "38": "8",
    "38": "8",
    "38.5": "8.5",
    "39": "9",
    "39.5": "9.5",
    "40": "10",
    "40.5": "10.5",
    "41": "11",
    "41.5": "11.5",
    "42": "12",
}

eu_to_us_mens_clothing = {
    "42": "32",
    "44": "34",
    "46": "36",
    "48": "38",
    "50": "40",
    "52": "42",
    "54": "44",
    "56": "46",
}

international_to_us_mens_clothing = {
    "xxs": "32",
    "xs": "34",
    "s": "36",
    "m": "38",
    "l": "40",
    "xl": "42",
    "xxl": "44",
    "2xl": "44",
    "3xl": "46",
    "xxxl": "46",
    "3xl": "46",
    "4xl": "48",
}

international_to_us_mens_pants = {
    "xs": "28",
    "s": "30",
    "m": "32",
    "l": "34",
    "xl": "36",
    "2xl": "38",
    "xxl": "38",
    "3xl": "40",
    "4xl": "42",
}

# Taken from https://www.vestiairecollective.com/men-clothing/shirts/aquascutum/white-synthetic-aquascutum-shirt-30595643.shtml
eu_to_us_mens_clothing_collar = {
    "37": "14.5",
    "38": "15",
    "39": "15.5",
    "40": "15.5",
    "41": "16",
    "42": "16.5",
    "43": "17",
    "44": "17.5",
}

zero_to_five_to_us_mens_clothing = {
    "0": "34",
    "1": "36",
    "2": "38",
    "3": "40",
    "4": "42",
    "5": "44",
    "6": "46",
}

kr_to_us_mens_clothing = {
    "xs": "34",
    "s": "36",
    "m": "38",
    "l": "40",
    "xl": "42",
    "xxl": "44",
    "xxxl": "46",
}

zero_to_five_to_us_womens_clothing = {
    "00000": "00",
    "0000": "0",
    "000": "1",
    "00": "2",
    "0": "4",
    "1": "6",
    "2": "8",
    "3": "10",
    "4": "12",
}

international_to_us_womens_pants = {
    "xs": "0",
    "s": "4",
    "m": "8",
    "l": "12",
    "xl": "16",
    "2xl": "16",
    "xxl": "20",
    "xxxl": "22",
    "3xl": "22",
}

international_to_us_womens_clothing = {
    "4xs": "00",
    "3xs": "0",
    "xxs": "1",
    "xs": "2",
    "s": "4",
    "m": "6",
    "l": "8",
    "xl": "10",
    "xxl": "12",
    "2xl": "12",
    "3xl": "14",
    "xxxl": "14",
    "4xl": "18",
    "5xl": "5xl",
    "6xl": "6xl",
    "7xl": "7xl",
    "8xl": "8xl",
    "9xl": "9xl",
}

fr_to_us_womens_clothing = {
    "28": "00",
    "30": "0",
    "32": "1",
    "34": "2",
    "36": "4",
    "38": "6",
    "40": "8",
    "42": "10",
    "44": "12",
    "46": "14-16",
    "48": "18",
    "50": "5XL",
    "52": "6XL",
    "54": "7XL",
    "56": "8XL",
    "58": "9XL",
}

uk_to_us_womens_clothing = {
    "2": "00",
    "3": "0",
    "4": "1",
    "6": "2",
    "8": "4",
    "10": "6",
    "12": "8",
    "14": "10",
    "16": "12",
    "18": "14-16",
    "19": "14-16",
    "20": "14-16",
    "22": "18",
    "24": "5XL",
    "26": "6XL",
    "28": "7XL",
    "30": "9XL",
}

it_to_us_womens_clothing = {
    "32": "00",
    "34": "0",
    "36": "1",
    "38": "2",
    "40": "4",
    "42": "6",
    "44": "8",
    "46": "10",
    "48": "12",
    "50": "14-16",
    "52": "14-16",
    "54": "18",
    "56": "18",
    "58": "6XL",
    "60": "7XL",
}

kr_to_us_womens_clothing = {
    "4xs": "00",
    "3xs": "0",
    "xxs": "1",
    "xs": "2",
    "s": "4",
    "m": "6",
    "l": "8",
    "xl": "10",
    "xxl": "12",
    "3xl": "14",
    "4xl": "18",
    "5xl": "5XL",
    "6xl": "6XL",
    "7xl": "7XL",
    "8xl": "8XL",
    "9xl": "9XL",
}


fr_to_us_pants_size = {
    "32": "22",
    "33": "23",
    "34": "24",
    "35": "25",
    "36": "26",
    "37": "27",
    "38": "28",
    "39": "29",
    "40": "30",
    "41": "31",
    "42": "32",
    "43": "33",
    "44": "34",
    "45": "35",
    "46": "36",
    "47": "37",
    "48": "38",
}

size_chart_conversion_diameter_to_us = {
    14.1: 3,
    14.5: 3.5,
    14.9: 4,
    15.3: 4.5,
    15.7: 5,
    16.1: 5.5,
    16.5: 6,
    16.9: 6.5,
    17.3: 7,
    17.7: 7.5,
    18.1: 8,
    18.5: 8.5,
    19.0: 9,
    19.4: 9.5,
    19.8: 10,
    20.2: 10.5,
    20.6: 11,
    21.0: 11.5,
    21.4: 12,
    21.8: 12.5,
    22.2: 13,
}

size_chart_conversion_circumference_to_us = {
    46.2: 3,
    46.4: 3.5,
    46.8: 4,
    48: 4.5,
    48.7: 5,
    50: 5.5,
    51.2: 6,
    52.5: 6.5,
    53.8: 7,
    55.1: 7.5,
    56.3: 8,
    57.6: 8.5,
    59.5: 9,
    60.2: 9.5,
    61.4: 10,
    62.7: 10.5,
    64: 11,
    65.3: 11.5,
    66.6: 12,
    67.8: 12.5,
    68.5: 13,
}


def convert_to_us_ring_size(diameter_mm, size_chart):

    sorted_diameters = sorted(size_chart.keys())

    # If the diameter is exactly in the chart, return the corresponding size
    if diameter_mm in size_chart:
        return str(size_chart[diameter_mm])

    # If the diameter is outside the chart range, return None
    if diameter_mm < sorted_diameters[0] or diameter_mm > sorted_diameters[-1]:
        return None  # or raise ValueError("Diameter out of range")

    # Find closest value
    for i in range(len(sorted_diameters) - 1):
        if sorted_diameters[i] <= diameter_mm <= sorted_diameters[i + 1]:
            return str(size_chart[sorted_diameters[i + 1]])

    return None
