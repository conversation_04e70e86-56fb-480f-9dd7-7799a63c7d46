import re

from assets.color_syn_map import COLOR_SYN_MAP
from models.product import CleanProductSchema, COLOR_LIST
from util.stat_logger import StatLogger, NON_UNIFIED_COLOR_COUNT_KEY


class ColorCurator:
    def __init__(self, item: CleanProductSchema):
        self.item = item

    def get_colors(self) -> tuple[str | None, list[str]]:
        primary_color = None
        colors = []

        if "color" not in self.item or self.item["color"] is None:
            StatLogger.add_non_unified_count(NON_UNIFIED_COLOR_COUNT_KEY)
            primary_color, colors = self.get_color_from_desc_and_title()
        else:
            if self.item["color"].lower() in [
                "multi",
                "multi-color",
                "multicolor",
                "multicolour",
            ]:
                primary_color, colors = self.get_color_from_desc_and_title()
            else:
                color_tokens = [
                    c.strip()
                    for c in self.item["color"].lower().replace("/", ",").split(",")
                ]
                for color_token in color_tokens:
                    color_token_lower = color_token.lower()
                    if color_token_lower in COLOR_LIST:
                        colors.append(color_token_lower)
                    else:
                        color = COLOR_SYN_MAP.get(color_token_lower)
                        if color:
                            colors.append(color)
                if len(colors) > 0:
                    primary_color = colors[0]

        return primary_color.upper() if primary_color else None, [
            c.upper() for c in colors
        ]

    def get_color_from_desc_and_title(self) -> tuple[str, list]:
        primary_color = None
        name = self.item.get("name", "").lower()
        name_colors = find_colors(name)
        description = self.item.get("description", "").lower()
        desc_colors = find_colors(description)
        colors = dedup_in_order(name_colors + desc_colors)
        if len(colors) > 0:
            primary_color = colors[0]
        StatLogger.add_non_unified_count(NON_UNIFIED_COLOR_COUNT_KEY)
        return primary_color, colors


def find_colors(input):
    pattern = r"\b(?:" + "|".join(list(COLOR_SYN_MAP.keys())) + r")\b"
    matches = re.findall(pattern, input, flags=re.IGNORECASE)
    return dedup_in_order([COLOR_SYN_MAP.get(m.lower()) for m in matches])


def dedup_in_order(color_list):
    found_colors = set()
    colors_found_in_order = []
    for color in color_list:
        if color not in found_colors:
            found_colors.add(color)
            colors_found_in_order.append(color)

    return colors_found_in_order
