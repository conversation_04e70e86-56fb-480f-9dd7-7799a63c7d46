from models.product import CleanProductSchema


SECONDHAND_RETAILER_TO_IMAGE_QUALITY = {
    "the_real_real": 1,
    "vestiaire_collective": 1,
    "the_luxury_closet": 1,
    "luxe_collective": 1,
    "rebag": 1,
    "farfetch": 1,
    "stockx": 1,
    "hewi": 1,
    "bopf": 0.75,
    "grailed": 0.2,
    "ebay": 0,
    "etsy": 0,
    "poshmark": 0,
    "shop_premium_outlets": 1,
}


class ImageQualityCurator:
    def __init__(self, item: CleanProductSchema):
        self.item = item
        self.retailer = self.item.get("secondhand_retailer")

    def get_image_quality(self):
        return SECONDHAND_RETAILER_TO_IMAGE_QUALITY.get(self.retailer, 0)
