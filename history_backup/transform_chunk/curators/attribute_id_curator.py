from assets.category_mappings import RAW_CATEGORY_TO_ATTRIBUTE_MAPPING
from models.product import CleanProductSchema


class AttributeIdCurator:
    def __init__(self, item: CleanProductSchema, attribute_id_mapper: dict):
        self.item = item
        self.partner = self.item.get("secondhand_retailer")
        self.attribute_id_mapper = attribute_id_mapper

    def get_attribute_display_name_and_id(self, raw_category):
        if raw_category is not None:
            return self.attribute_id_mapper.get(self.partner, {}).get(raw_category)
