import logging
import urllib.parse

from marshmallow import ValidationError

from models.product import CleanProductSchema, validate_url_or_empty


class ProductUrlCurator:

    CHARACTERS_TO_REMOVE = ["\n", "\xa0", "\t"]

    def __init__(self, item: CleanProductSchema):
        self.item = item

    def get_product_url(self) -> str:
        product_url = self.item["product_url"]
        for character in self.CHARACTERS_TO_REMOVE:
            product_url = product_url.replace(character, "")
        return product_url

    def get_img_url(self):
        return encode_characters(self.item.get("img_url"))

    def get_additional_img_urls(self):
        r = []
        for url in self.item.get("additional_img_urls", []):
            try:
                encoded = encode_characters(url)
                validate_url_or_empty(encoded)
                r.append(encoded)
            except ValidationError as err:
                logging.warning(
                    f"Url validation errors occurred: {err.messages} for additional url in: {self.item}"
                )
        return r


def is_encoded(s):
    return s != urllib.parse.unquote(s)


def encode_characters(url_str):
    # Parse the URL into components
    parsed_url = urllib.parse.urlparse(url_str)
    # Encode each component separately
    encoded_path = urllib.parse.quote(parsed_url.path, safe="/:")
    if is_encoded(parsed_url.query):
        encoded_query = parsed_url.query
    else:
        encoded_query = urllib.parse.quote_plus(parsed_url.query, safe="=&")
    # Reconstruct the URL with encoded components
    encoded_url = urllib.parse.urlunparse(
        (
            parsed_url.scheme,
            parsed_url.netloc,
            encoded_path,
            parsed_url.params,
            encoded_query,
            parsed_url.fragment,
        )
    )
    return encoded_url
