from enum import Enum

from marshmallow import Schema, fields, validate, INCLUDE
from datetime import datetime


class Condition(Enum):
    NEW_WITH_TAG = "NEW_WITH_TAG"
    PRISTINE = "PRISTINE"
    VERY_GOOD = "VERY_GOOD"
    GOOD = "GOOD"


class Color(Enum):
    BLACK = "BLACK"
    GREY = "GREY"
    BROWN = "BROWN"
    RED = "RED"
    BLUE = "BLUE"
    PURPLE = "PURPLE"
    PINK = "PINK"
    ORANGE = "ORANGE"
    WHITE = "WHITE"
    GREEN = "GREEN"
    YELLOW = "YELLOW"
    GOLD = "GOLD"
    SILVER = "SILVER"
    BEIGE = "BEIGE"
    PRINT = "PRINT"


COLOR_LIST = [c.value for c in Color]


class Gender(Enum):
    MEN = "MEN"
    WOMEN = "WOMEN"
    UNISEX = "UNISEX"


class CategoryBroad(Enum):
    PANTS = "pants"
    CLOTHING = "clothing"
    SHOES = "shoe"
    RINGS = "ring"
    ACCESSORIES = "accessories"


class Size(Schema):
    raw_size_system = fields.Str(allow_none=True)
    raw_size_value = fields.Str(allow_none=True)
    us_size_value = fields.Str(allow_none=True)
    mapped_size_system = fields.Str(allow_none=True)
    id = fields.Str(allow_none=True)


class Brand(Schema):
    raw_brand = fields.Str(allow_none=True)
    primary_brand = fields.Str(allow_none=True)
    brands = fields.List(fields.Str(), allow_none=True)
    id = fields.Str(allow_none=True)


"""
The CleanProduct is an intermediary representation of the Product,
mostly for us to validate internally that the desired fields have been properly
extracted.
"""


class CleanProductSchema(Schema):
    # Primary fields
    id = fields.String(required=True)
    name = fields.String(required=True)
    description = fields.String(allow_none=True)
    price_usd = fields.Decimal(as_string=True)
    product_url = fields.String(allow_none=True)
    img_url = fields.String(allow_none=True)
    additional_img_urls = fields.List(fields.String())
    firsthand_retailer = fields.String(allow_none=True)
    secondhand_retailer = fields.String(allow_none=True)

    # Boolean fields
    is_authenticated = fields.Boolean(allow_none=True)
    is_free_shipping = fields.Boolean(allow_none=True)
    is_returnable = fields.Boolean(allow_none=True)
    is_vintage = fields.Boolean(allow_none=True)
    is_available = fields.Boolean(allow_none=True)
    is_approved_seller = fields.Boolean(allow_none=True)

    # Raw fields
    color = fields.String(allow_none=True)
    brand = fields.String(allow_none=True)
    category = fields.String(allow_none=True)
    category_broad = fields.String(allow_none=True)
    condition = fields.String(allow_none=True)
    gender = fields.String(allow_none=True)
    raw_size = fields.String(allow_none=True)
    raw_size_system = fields.String(allow_none=True)
    material = fields.String(allow_none=True)
    raw_id = fields.String(allow_none=True)

    created_at = fields.DateTime(load_default=datetime.now)
    updated_at = fields.DateTime(load_default=datetime.now)
    last_seen_at = fields.DateTime(required=True)
    sold_at = fields.DateTime(allow_none=True)


def validate_url_or_empty(url):
    if not url:  # If url is None or empty string
        return True
    return validate.URL()(url)


# Marshmallow schema equivalent to Pydantic Product model
class ProductSchema(Schema):
    id = fields.String(required=True)
    name = fields.String(required=True, validate=validate.Length(max=256))
    description = fields.String(allow_none=True)
    price_usd = fields.Decimal()
    product_url = fields.String(validate=validate_url_or_empty)
    img_url = fields.String(validate=validate_url_or_empty)
    additional_img_urls = fields.List(fields.Url())
    firsthand_retailer_id = fields.String(allow_none=True)
    secondhand_retailer_id = fields.String(allow_none=True)

    is_authenticated = fields.Boolean(load_default=False)
    is_free_shipping = fields.Boolean(load_default=False)
    is_returnable = fields.Boolean(load_default=False)
    is_vintage = fields.Boolean(load_default=False)

    category_raw = fields.String(allow_none=True)
    category_broad = fields.String(allow_none=True)
    category_specific = fields.String(allow_none=True)
    condition = fields.String(
        allow_none=True, validate=validate.OneOf([c.value for c in Condition])
    )
    gender = fields.String(
        allow_none=True, validate=validate.OneOf([g.value for g in Gender])
    )
    size = fields.Nested(Size(), allow_none=True)
    brand = fields.Nested(Brand(), allow_none=True)
    brand_id = fields.Nested(Brand(), allow_none=True)
    primary_color = fields.String(
        allow_none=True, validate=validate.OneOf([c.value for c in Color])
    )
    colors = fields.List(
        fields.String(validate=validate.OneOf([c.value for c in Color]))
    )
    raw_color = fields.String(allow_none=True)
    image_quality = fields.Decimal()

    # Attributes
    attribute_list = fields.List(fields.Raw(), allow_none=True)

    # Meta
    meta = fields.Dict(allow_none=True)

    created_at = fields.DateTime(load_default=datetime.now)
    updated_at = fields.DateTime(load_default=datetime.now)
    last_seen_at = fields.DateTime(required=True)
    sold_at = fields.DateTime(allow_none=True)

    # TODO: Add brands, colors - determine how these should be updated

    class Meta:
        unknown = INCLUDE  # Include unknown fields
