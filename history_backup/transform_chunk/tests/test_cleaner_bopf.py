import pytest

from cleaners.bopf_cleaner import (
    <PERSON><PERSON><PERSON>leaner,
    extract_condition,
    BOPF_CONDITION_MAP,
)
from enums import Partners
from models.product import Condition


def get_mock_item():
    return {
        "Id": "product_11994_44243660407016",
        "CatalogItemId": "44243660407016",
        "Name": "Hermès Chypre caramel and black leather sandals, 37.5",
        "Product Description": "<p>Hermes Chypre caramel and black leather sandals with anatomical rubber sole and adjustable strap. <br> <br><strong>Attributes</strong> <br><strong>Material</strong>: Leather <br><strong>Color</strong>: Caramel and Black <br><strong>Condition Desc.</strong>: Brand New <br><strong>Size</strong>: 37.5 <br><strong>Inclusions:</strong> Dustbag, Box</p>",
        "MultiPack": "0",
        "Bullets": [],
        "Labels": [
            "Brand New_Shoes",
            " Brands_Hermes",
            " ConsignCloud",
            " Flats",
            " Sandals",
            " Size_37.5",
        ],
        "Manufacturer": "Hermès",
        "Url": "https://bopfbusinessofprelovedfashionaffiliateprogram.sjv.io/c/4095344/1616918/18746?prodsku=44243660407016&u=https%3A%2F%2Fbusinessofprelovedfashion.myshopify.com%2Fproducts%2Fherms-chypre-caramel-and-black-leather-sandals-375-6518&intsrc=APIG_11994",
        "MobileUrl": "",
        "ImageUrl": "https://cdn.shopify.com/s/files/1/0541/3715/9849/products/FullSizeRender_209.jpg?v=1674469985",
        "ProductBid": "",
        "AdditionalImageUrls": [
            "https://cdn.shopify.com/s/files/1/0541/3715/9849/products/FullSizeRender_2015.jpg?v=1674469985",
            "https://cdn.shopify.com/s/files/1/0541/3715/9849/products/FullSizeRender_2010.jpg?v=1674469985",
            "https://cdn.shopify.com/s/files/1/0541/3715/9849/products/FullSizeRender_2011.jpg?v=1674467662",
            "https://cdn.shopify.com/s/files/1/0541/3715/9849/products/FullSizeRender_2012.jpg?v=1674467662",
            "https://cdn.shopify.com/s/files/1/0541/3715/9849/products/FullSizeRender_2013.jpg?v=1674467662",
        ],
        "Promotions": [],
        "CurrentPrice": "4599.00",
        "OriginalPrice": "",
        "DiscountPercentage": "",
        "Currency": "USD",
        "StockAvailability": "OutOfStock",
        "EstimatedShipDate": "",
        "LaunchDate": "2023-01-23T05:54:22-08:00",
        "ShippingWeight": "0.0",
        "Category": "Sandals",
        "IsParent": False,
        "ItemGroupId": "",
        "Colors": [],
        "Uri": "/Mediapartners/IRpruqobbVoM4095344WQVnfxWqgDhpWt1/Catalogs/11994/Items/product_11994_44243660407016",
    }


@pytest.fixture
def cleaner():
    return BopfCleaner(Partners.BOPF.value, get_mock_item(), "test_file.xml")


def test_get_condition(cleaner):
    assert cleaner.get_condition() == Condition.PRISTINE.value


def test_get_authenticity(cleaner):
    assert cleaner.is_authenticated() is True


@pytest.mark.parametrize(
    "description, expected_condition",
    [
        (
            "<p>3.1 Phillip Lim white embroidered detail zip up cardigan. "
            "Size 2 (Small)</p><p><b> Attributes </b></p><b>Material"
            "</b> : 100% Cotton<br><b>Color</b> : White<br><b>Condition Desc.</b> : Excellent<br>",
            Condition.PRISTINE.value,
        ),
        (
            "<p>7RP L'Insert for Birkin 30 Tourterelle <br> <br>"
            "L'Insert is made by hand, it is made of the Italian"
            " high-tech material Alcantara, which is water-repellent and highly resistant to"
            " color transfer. <br> <br>This one has been made to fit a Hermes Birkin 30 Tourterelle</p>",
            None,
        ),
        (
            "<p><b>Description</b> : A.L.C. black embroidered pencil skirt,"
            " size 0</p><p><b></b><b> </b><b> </b><b> Attributes </b></p><b>Color</b> : Black<br>"
            "<p>Acler silver one shoulder ruffle layered strappy dress, "
            "zipper on the side <br> <br><strong>Attributes</strong> "
            "<br><strong>Material</strong>: Silk <br><strong>Color</strong>: Silver "
            "<br><strong>Condition Desc.</strong>: Very good <br><strong>Size:</strong> US10</p>",
            Condition.VERY_GOOD.value,
        ),
    ],
)
def test_extract_condition_from_description(description, expected_condition):
    assert extract_condition(description, BOPF_CONDITION_MAP) == expected_condition
