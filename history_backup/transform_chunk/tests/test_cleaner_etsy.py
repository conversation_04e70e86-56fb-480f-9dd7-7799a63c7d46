import pytest
from cleaners.etsy_cleaner import EtsyCleaner
from enums import Partners
from tests.utils import compare_dict_values
from decimal import Decimal
from datetime import datetime


def get_mock_item(in_stock, stock_quantity):
    return {
        "in_stock": in_stock,
        "stock_quantity": stock_quantity,
        "product_name": "Test Product",
        "search_price": "$99.99",
        "product_price_old": "199.99",
        "aw_deep_link": "http://example.com/product",
        "aw_image_url": "http://example.com/image.jpg",
        "aw_thumb_url": "http://example.com/image2.jpg",
        "alternate_image": "http://example.com/image3.jpg",
        "description": "Test Description",
        "Fashion:material": "Test Material",
        "product_type": "Clothing,women's Clothing,dresses",
        "aw_product_id": "12345",
    }


@pytest.fixture
def mock_item():
    return get_mock_item(1, 2)


@pytest.fixture
def etsy_cleaner(mock_item):
    return EtsyCleaner(Partners.ETSY.value, mock_item, "test_1712808874_0.csv")


@pytest.fixture
def etsy_cleaner_in_stock():
    return EtsyCleaner(
        Partners.ETSY.value, get_mock_item(1, 1), "test_1712808874_0.csv"
    )


@pytest.fixture
def etsy_cleaner_out_of_stock():
    return EtsyCleaner(
        Partners.ETSY.value, get_mock_item(0, 0), "test_1712808874_0.csv"
    )


def test_create_item_available(etsy_cleaner_in_stock):
    expected = {
        "id": f"{Partners.ETSY.value}-12345",
        "secondhand_retailer": Partners.ETSY.value,
        "name": "Test Product",
        "product_url": "http://example.com/product",
        "img_url": "http://example.com/image.jpg",
        "additional_img_urls": [
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg",
        ],
        "description": "Test Description",
        "raw_id": "12345",
        "material": "Test Material",
        "price_usd": Decimal(99.99),
        "created_at": datetime.now(),
        "last_seen_at": datetime.now(),
        "updated_at": datetime.now(),
        "is_available": True,
        "is_authenticated": False,
        "is_returnable": False,
        "category": "Clothing,women's Clothing,dresses",
        "category_broad": "clothing",
        "is_approved_seller": True,
    }
    actual = etsy_cleaner_in_stock.create_item()
    assert compare_dict_values(actual=actual, expected=expected)


def test_create_item_not_available(etsy_cleaner_out_of_stock):
    assert etsy_cleaner_out_of_stock.create_item() is not None


def test_is_available(etsy_cleaner):
    assert etsy_cleaner.is_available()


def test_is_authenticated(etsy_cleaner):
    assert etsy_cleaner.is_authenticated() is False


def test_get_name(etsy_cleaner):
    assert etsy_cleaner.get_name() == "Test Product"


def test_get_price(etsy_cleaner):
    assert etsy_cleaner.get_price() == 99.99


def test_get_product_url(etsy_cleaner):
    assert etsy_cleaner.get_product_url() == "http://example.com/product"


def test_get_img_url(etsy_cleaner):
    assert etsy_cleaner.get_img_url() == "http://example.com/image.jpg"


def test_get_additional_img_urls(etsy_cleaner):
    assert etsy_cleaner.get_additional_img_urls() == [
        "http://example.com/image2.jpg",
        "http://example.com/image3.jpg",
    ]


def test_get_description(etsy_cleaner):
    assert etsy_cleaner.get_description() == "Test Description"


def test_get_material(etsy_cleaner):
    assert etsy_cleaner.get_material() == "Test Material"


def test_get_raw_id(etsy_cleaner):
    assert etsy_cleaner.get_raw_id() == "12345"
