import pytest

from curators.image_quality_curator import ImageQualityCurator
from models.product import CleanProductSchema
from tests.utils import get_minimal_mock_clean_product


@pytest.fixture
def product_no_secondhand_retailer():
    product = get_minimal_mock_clean_product()
    product["secondhand_retailer"] = None
    return CleanProductSchema().load(product)


@pytest.mark.parametrize(
    "secondhand_retailer_value, expected_image_quality",
    [
        ("the_real_real", 1),
        ("grailed", 0.2),
        ("unknown retailer", 0),
    ],
)
def test_curator_image_quality(
    secondhand_retailer_value, expected_image_quality, product_no_secondhand_retailer
):
    product_no_secondhand_retailer["secondhand_retailer"] = secondhand_retailer_value
    curator = ImageQualityCurator(product_no_secondhand_retailer)
    assert curator.get_image_quality() == expected_image_quality
