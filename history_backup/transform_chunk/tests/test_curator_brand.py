import pytest

from curators.brand_curator import BrandCurator
from models.product import CleanProductSchema
from tests.utils import get_minimal_mock_clean_product


@pytest.fixture
def product_no_brand():
    product = get_minimal_mock_clean_product()
    product["brand"] = None
    return CleanProductSchema().load(product)


@pytest.mark.parametrize(
    "brand_value, expected_primary_brand, expected_brands, expected_id",
    [
        ("", "Unavailable", [], None),
        ("Gucci", "Gucci", ["Gucci"], "456"),
        ("Guccie", "Gucci", ["Gucci"], "456"),
        ("gucci", "Gucci", ["Gucci"], "456"),
        ("gucci pre-owned", "Gucci", ["Gucci"], "456"),
        (
            "Adidas x A Bathing Ape x Off White",
            "Adidas",
            ["Adidas", "A Bathing Ape", "Off White"],
            "123",
        ),
        (
            "Polo By Ralph Lauren",
            "Ralph Lauren",
            ["Ralph Lauren"],
            "789",
        ),
    ],
)
def test_curator_brand(
    brand_value, expected_primary_brand, expected_brands, expected_id, product_no_brand
):
    brand_id_mapper = {
        "adidas": "123",
        "gucci": "456",
        "ralph lauren": "789",
        "a bathing ape": "924",
        "off white": "1425",
    }
    brand_list = ["Adidas", "Gucci", "Ralph Lauren", "A Bathing Ape", "Off White"]
    curator = BrandCurator(product_no_brand, brand_id_mapper, brand_list)
    product_no_brand["brand"] = brand_value
    assert curator.get_brand().get("raw_brand") == brand_value
    assert curator.get_brand()["primary_brand"] == expected_primary_brand
    assert curator.get_brand().get("brands") == expected_brands
    assert curator.get_brand().get("id") == expected_id
