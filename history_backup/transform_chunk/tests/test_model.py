from models.product import ProductSchema, CleanProductSchema
import pytest
from marshmallow import ValidationError
from datetime import datetime
from decimal import Decimal
from tests.utils import (
    compare_dates,
    get_minimal_mock_product,
    get_minimal_mock_clean_product,
)


def test_product_model_validation():
    sample_product_data = {
        "id": "unique-product-id",
        "name": "Example Product",
        "description": "This is an example product description.",
        "price_usd": 19.99,
        "product_url": "http://example.com/product",
        "img_url": "http://example.com/image.jpg",
        "additional_img_urls": [
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg",
        ],
        "firsthand_retailer_id": None,
        "secondhand_retailer_id": "secondhand-retailer-id",
        "category_id": None,
        "condition": "NEW_WITH_TAG",
        "gender": "UNISEX",
        "is_authenticated": True,
        "is_free_shipping": True,
        "is_returnable": False,
        "is_vintage": False,
        "primary_brand_id": None,
        "primary_color": "RED",
        "size_id": None,
        "colors": ["RED", "BLUE"],
        "created_at": "2023-10-01T12:00:00",
        "updated_at": "2023-10-01T12:00:00",
        "last_seen_at": "2023-10-01T12:00:00",
        "sold_at": None,
    }

    # Creating a new instance of ProductSchema
    product_schema = ProductSchema()
    product = product_schema.load(sample_product_data)
    assert product["id"] == sample_product_data["id"]


def test_product_default_created_at():
    schema = CleanProductSchema()
    input_data = get_minimal_mock_product()
    result = schema.load(input_data)
    expected_created_at = datetime.now()
    assert compare_dates(expected_created_at, result["created_at"])


def test_invalid_url():
    schema = ProductSchema()
    input_data = get_minimal_mock_product()
    input_data["product_url"] = "not-a-valid-url"  # This should fail
    with pytest.raises(ValidationError) as excinfo:
        schema.load(input_data)
    assert "product_url" in excinfo.value.messages
    assert excinfo.value.messages["product_url"][0] == "Not a valid URL."


# Clean Product Tests
def test_clean_product_model_validation():
    schema = CleanProductSchema()
    input_data = {
        "id": "test-id",
        "name": "Test Product",
        "description": "A description of the test product.",
        "price_usd": Decimal("29.99"),
        "product_url": "http://example.com/product",
        "img_url": "http://example.com/image.jpg",
        "additional_img_urls": ["http://example.com/image2.jpg"],
        "firsthand_retailer": "Retailer A",
        "secondhand_retailer": "Retailer B",
        "category": "Electronics",
        "condition": "new_with_tag",
        "gender": "unisex",
        "is_authenticated": True,
        "is_free_shipping": True,
        "is_returnable": False,
        "is_vintage": False,
        "brand": "Brand X",
        "color": "blue",
        "raw_size": "size-large",
        "created_at": str(datetime.now()),
        "updated_at": str(datetime.now()),
        "last_seen_at": str(datetime.now()),
        "sold_at": None,
    }
    result = schema.load(input_data)
    assert result["id"] == "test-id"


def test_missing_required_field():
    schema = CleanProductSchema()
    input_data = get_minimal_mock_clean_product()
    del input_data["name"]  # Remove required field
    with pytest.raises(ValidationError) as excinfo:
        schema.load(input_data)
    assert "name" in excinfo.value.messages
    assert excinfo.value.messages["name"][0] == "Missing data for required field."
