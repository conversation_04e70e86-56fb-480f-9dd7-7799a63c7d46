import pytest

from cleaners.base_cleaner import extract_condition
from cleaners.hewi_cleaner import HEWI_CONDITION_MAP
from models.product import Condition
from cleaners.hewi_cleaner import HewiCleaner
from enums import Partners


def get_mock_item():
    {
        "Id": "product_10199_9192",
        "CatalogId": "10199",
        "CampaignId": "16143",
        "CampaignName": "Hardly Ever Worn It",
        "CatalogItemId": "9192",
        "Name": "Bottega Veneta Black Platform Lace-Up Ankle Boots Size 38",
        "Description": "Black leather bottega veneta semi pointed-toe platform ankle boots with covered heels and lace-up closures at uppers. bottega veneta's beautifully crafted\u00a0black leather\u00a0ankle boots will give every outfit an instant style lift. wear this lace-up pair now with floral-print dresses and bare legs, adding a chunky sweater and opaque tights when the weather cools. fabulous condition with minimal signs of wear (soles and a tiny scratch on the heels), none of them visible when worn",
        "MultiPack": "",
        "Bullets": [],
        "Labels": ["", "20", "", "false"],
        "Manufacturer": "Bottega Veneta",
        "Url": "https://hardlyeverwornit.pxf.io/c/4095344/1371671/16143?prodsku=9192&u=https%3A%2F%2Fhardlyeverwornit.com%2Fproducts%2Fbottega-veneta-black-platform-laceup-ankle-boots&intsrc=APIG_10199",
        "MobileUrl": "",
        "ImageUrl": "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_1.jpg",
        "ProductBid": "",
        "AdditionalImageUrls": [
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_2.jpg",
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_3.jpg",
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_4.jpg",
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_5.jpg",
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_6.jpg",
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_7.jpg",
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_8.jpg",
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_9.jpg",
            "https://images.hardlyeverwornit.com/gaufrette/images/222/a222644_10.jpg",
        ],
        "Promotions": [],
        "CurrentPrice": "250.00",
        "OriginalPrice": "250.00",
        "DiscountPercentage": "",
        "Currency": "GBP",
        "StockAvailability": "InStock",
        "EstimatedShipDate": "",
        "LaunchDate": "",
        "ExpirationDate": "",
        "Gtin": "",
        "GtinType": "",
        "Asin": "",
        "Mpn": "",
        "ShippingRate": "10.00",
        "ShippingWeight": "",
        "ShippingWeightUnit": "",
        "ShippingLength": "",
        "ShippingWidth": "",
        "ShippingHeight": "",
        "ShippingLengthUnit": "",
        "ShippingLabel": "",
        "Category": "187",
        "SubCategory": "",
        "AdvertiserFormatCategories": "",
        "OriginalFormatCategory": "187",
        "OriginalFormatCategoryId": "",
        "ParentName": "",
        "ParentSku": "",
        "IsParent": False,
        "ItemGroupId": "",
        "Colors": ["Black"],
        "Material": "leather",
        "Pattern": "",
        "Size": "38",
        "SizeUnit": "",
        "Weight": "",
        "WeightUnit": "",
        "Condition": "Used",
        "AgeGroup": "Adult",
        "AgeRangeMin": "0",
        "AgeRangeMax": "0",
        "AgeRangeUnit": "",
        "Gender": "Female",
        "Adult": "",
        "Text1": "",
        "Text2": "",
        "Text3": "",
        "Numeric1": "",
        "Numeric2": "",
        "Numeric3": "",
        "Money1": "",
        "Money2": "",
        "Money3": "",
        "Uri": "/Mediapartners/IRpruqobbVoM4095344WQVnfxWqgDhpWt1/Catalogs/10199/Items/product_10199_9192",
    }


@pytest.fixture
def cleaner():
    return HewiCleaner(Partners.HEWI.value, get_mock_item(), "test_file.jsonl")


@pytest.mark.parametrize(
    "product_description, expected_condition",
    [
        (
            "Classic olive green suede ankle boots. hardly ever worn. look new. "
            "a few wear marks on sole of boots (please refer to photographs for more detail",
            Condition.PRISTINE.value,
        ),
        (
            # Note that we return Pristine because never worn > very good
            "Miu miu pink glitter lux calzature donna stud sneakers in very good condition, never worn.",
            Condition.PRISTINE.value,
        ),
        (
            "worn a few times but overall in very good condition. please refer to photos",
            Condition.VERY_GOOD.value,
        ),
        (
            "Miu miu unworn still with tags size 38 crepe",
            Condition.NEW_WITH_TAG.value,
        ),
    ],
)
def test_extract_condition(product_description, expected_condition):
    assert (
        extract_condition(product_description, HEWI_CONDITION_MAP) == expected_condition
    )


def test_is_authenticated(cleaner):
    assert cleaner.is_authenticated() is True
