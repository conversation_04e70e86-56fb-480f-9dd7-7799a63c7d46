import pytest

from curators.product_url_curator import ProductUrlCurator, encode_characters
from models.product import CleanProductSchema, validate_url_or_empty
from tests.utils import get_minimal_mock_clean_product


@pytest.fixture
def product():
    product = get_minimal_mock_clean_product()
    return CleanProductSchema().load(product)


@pytest.mark.parametrize(
    "product_url_value",
    [
        "https://theluxurycloset.com/us-en/women/chanel-black\nblue",
        "https://theluxurycloset.com/us-en/women/chanel-black\xa0blue",
    ],
)
def test_curator_product_url(product_url_value, product):
    product["product_url"] = product_url_value
    curator = ProductUrlCurator(product)
    assert (
        curator.get_product_url()
        == "https://theluxurycloset.com/us-en/women/chanel-blackblue"
    )


def test_encode_characters_with_spaces():
    img_url_w_spaces = (
        "https://images.stockx.com/images/Asics-Gel-Lyte-III-Atmos-Duck Camo.jpg,149.00"
    )
    encoded = encode_characters(img_url_w_spaces)
    assert validate_url_or_empty(encoded)


def test_encode_characters_2(product):
    imgs = [
        "https://d2mlaxtxr0fyi.cloudfront.net/Alyssa/Denimist/Jeans-DSW1200-Yellow--800-2.jpg",
        "https://d2mlaxtxr0fyi.cloudfront.net/Alyssa/Denimist/Jeans-DSW1200-Yellow--800-3.jpg",
        "https://d2mlaxtxr0fyi.cloudfront.net/Alyssa/Denimist/Jeans-DSW1200-Yellow--800-4.jpg",
        "https://d2mlaxtxr0fyi.cloudfront.net/Alyssa/Denimist/Jeans-DSW1200-Yellow--800-5.jpg",
        "https://d2mlaxtxr0fyi.cloudfront.net/Alyssa/Denimist/Jeans-DSW1200-Yellow--800-6.jpg",
        "https://walkintofashion/Alyssa/Denimist/Denimist%2520Size%2520Chart.jpg",
    ]
    product["additional_img_urls"] = imgs
    curator = ProductUrlCurator(product)
    additional_img_urls = curator.get_additional_img_urls()
    assert additional_img_urls == imgs[:-1]


def test_encode_characters_already_encoded():
    already_encoded = "https://images2.productserve.com/?w=200&h=200&bg=white&trim=5&t=letterbox&url=ssl%3Ai.etsystatic.com%2F37868476%2Fr%2Fil%2Fb12004%2F4685588477%2Fil_fullxfull.4685588477_i5af.jpg&feedId=87273&k=c1542449d6fed6fed4379060f545ae5c532c3b9e"
    encoded = encode_characters(already_encoded)
    assert already_encoded == encoded
