import pytest
from cleaners.poshmark_cleaner import PoshmarkCleaner
from enums import Partners
from models.product import Condition
from tests.utils import compare_dict_values
from decimal import Decimal
from datetime import datetime


def get_mock_item(price="$99.99"):
    return {
        "title": "Test Product",
        "price": price,
        "url": "http://example.com/product",
        "image_url": "http://example.com/image.jpg",
        "additional_image_link": "http://example.com/image2.jpg",
        "description": "Test Description",
        "id": "12345",
        "category": "clothing",
        "condition": "new",
        "posh_pro_status": "posh_pro",
    }


@pytest.fixture
def mock_item():
    return get_mock_item()


@pytest.fixture
def poshmark_cleaner(mock_item):
    return PoshmarkCleaner(Partners.POSHMARK.value, mock_item, "test_1712808874_0.csv")


@pytest.fixture
def poshmark_cleaner_price_above_500(mock_item):
    return PoshmarkCleaner(
        Partners.POSHMARK.value, get_mock_item("$500.00"), "test_1712808874_0.csv"
    )


@pytest.fixture
def poshmark_cleaner_in_stock():
    return PoshmarkCleaner(
        Partners.POSHMARK.value, get_mock_item(), "test_1712808874_0.csv"
    )


def test_create_item_available(poshmark_cleaner_in_stock):
    expected = {
        "id": f"{Partners.POSHMARK.value}-12345",
        "secondhand_retailer": Partners.POSHMARK.value,
        "name": "Test Product",
        "product_url": "http://example.com/product",
        "img_url": "http://example.com/image.jpg",
        "additional_img_urls": [],
        "description": "Test Description",
        "raw_id": "12345",
        "price_usd": Decimal(99.99),
        "created_at": datetime.now(),
        "last_seen_at": datetime.now(),
        "updated_at": datetime.now(),
        "is_available": True,
        "is_authenticated": False,
        "is_returnable": True,
        "category": "clothing",
        "category_broad": "clothing",
        "condition": Condition.PRISTINE.value,
        "is_approved_seller": True,
    }
    actual = poshmark_cleaner_in_stock.create_item()
    assert compare_dict_values(actual=actual, expected=expected)


def test_is_available(poshmark_cleaner):
    assert poshmark_cleaner.is_available()


def test_get_name(poshmark_cleaner):
    assert poshmark_cleaner.get_name() == "Test Product"


def test_get_price(poshmark_cleaner):
    assert poshmark_cleaner.get_price() == 99.99


def test_get_product_url(poshmark_cleaner):
    assert poshmark_cleaner.get_product_url() == "http://example.com/product"


def test_get_img_url(poshmark_cleaner):
    assert poshmark_cleaner.get_img_url() == "http://example.com/image.jpg"


def test_get_additional_img_urls(poshmark_cleaner):
    assert poshmark_cleaner.get_additional_img_urls() == []


def test_get_description(poshmark_cleaner):
    assert poshmark_cleaner.get_description() == "Test Description"


def test_get_raw_id(poshmark_cleaner):
    assert poshmark_cleaner.get_raw_id() == "12345"


def test_get_condition(poshmark_cleaner):
    assert poshmark_cleaner.get_condition() == Condition.PRISTINE.value


def test_is_authenticated(poshmark_cleaner, poshmark_cleaner_price_above_500):
    assert poshmark_cleaner.is_authenticated() is False
    assert poshmark_cleaner_price_above_500.is_authenticated() is True
