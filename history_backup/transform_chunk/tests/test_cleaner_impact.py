import pytest

from cleaners.base_cleaner import get_category_broad_from_category_raw
from cleaners.impact_cleaner import (
    ImpactCleaner,
    Availability,
)
from enums import Partners
from models.product import CategoryBroad
from tests.utils import compare_dict_values
from decimal import Decimal
from datetime import datetime


def get_mock_item(availability):
    return {
        "Stock Availability": availability,
        "Product Name": "Test Product",
        "Current Price": "$99.99",
        "Original Price": "199.99",
        "Product URL": "http://example.com/product",
        "Image URL": "http://example.com/image.jpg",
        "Alternative Image URL 1": "http://example.com/image2.jpg",
        "Alternative Image URL 2": "http://example.com/image3.jpg",
        "Product Description": "Test Description",
        "Material": "Test Material",
        "Unique Merchant SKU": "12345",
        "Category": "Apparel & Accessories > Clothing > Shirts & Tops",
    }


@pytest.fixture
def mock_item():
    return get_mock_item(Availability.IN_STOCK.value)


@pytest.fixture
def impact_cleaner(mock_item):
    return ImpactCleaner(Partners.GRAILED.value, mock_item, "test_1712808874_0.csv")


@pytest.fixture
def impact_cleaner_in_stock():
    return ImpactCleaner(
        Partners.GRAILED.value,
        get_mock_item(Availability.IN_STOCK.value),
        "test_1712808874_0.csv",
    )


@pytest.fixture
def impact_cleaner_out_of_stock():
    return ImpactCleaner(
        Partners.GRAILED.value,
        get_mock_item(Availability.OUT_OF_STOCK.value),
        "test_1712808874_0.csv",
    )


def test_create_item_available(impact_cleaner_in_stock):
    expected = {
        "id": f"{Partners.GRAILED.value}-12345",
        "secondhand_retailer": Partners.GRAILED.value,
        "name": "Test Product",
        "product_url": "http://example.com/product",
        "img_url": "http://example.com/image.jpg",
        "additional_img_urls": [
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg",
        ],
        "description": "Test Description",
        "raw_id": "12345",
        "material": "Test Material",
        "price_usd": Decimal(99.99),
        "created_at": datetime.now(),
        "last_seen_at": datetime.now(),
        "updated_at": datetime.now(),
        "is_available": True,
        "is_authenticated": False,
        "is_returnable": False,
        "category": "Apparel & Accessories > Clothing > Shirts & Tops",
        "category_broad": "clothing",
        "is_approved_seller": True,
    }
    actual = impact_cleaner_in_stock.create_item()
    assert compare_dict_values(actual=actual, expected=expected)


def test_create_item_not_available(impact_cleaner_out_of_stock):
    assert impact_cleaner_out_of_stock.create_item() is not None


def test_is_available(impact_cleaner):
    assert impact_cleaner.is_available()


def test_get_name(impact_cleaner):
    assert impact_cleaner.get_name() == "Test Product"


def test_get_price(impact_cleaner):
    assert impact_cleaner.get_price() == 99.99


def test_get_product_url(impact_cleaner):
    assert impact_cleaner.get_product_url() == "http://example.com/product"


def test_get_img_url(impact_cleaner):
    assert impact_cleaner.get_img_url() == "http://example.com/image.jpg"


def test_get_additional_img_urls(impact_cleaner):
    assert impact_cleaner.get_additional_img_urls() == [
        "http://example.com/image2.jpg",
        "http://example.com/image3.jpg",
    ]


def test_get_description(impact_cleaner):
    assert impact_cleaner.get_description() == "Test Description"


def test_get_material(impact_cleaner):
    assert impact_cleaner.get_material() == "Test Material"


def test_get_raw_id(impact_cleaner):
    assert impact_cleaner.get_raw_id() == "12345"


# TheRealReal data for testing Impact category broad
@pytest.mark.parametrize(
    "raw_category",
    ["Apparel & Accessories > Clothing > Pants"],
)
def test_get_category_broad_from_category_raw_for_pants(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.PANTS.value


@pytest.mark.parametrize(
    "raw_category",
    ["Apparel & Accessories > Shoes"],
)
def test_get_category_broad_from_category_raw_for_pants_for_shoes(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.SHOES.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Apparel & Accessories > Clothing > Sleepwear & Loungewear > Robes",
        "Apparel & Accessories > Clothing > Shirts & Tops",
        "Apparel & Accessories > Clothing > Outerwear > Coats & Jackets",
        "Apparel & Accessories > Clothing > Baby & Toddler Clothing > Baby & Toddler Tops",
        "Apparel & Accessories > Clothing > Outerwear",
        "Apparel & Accessories > Clothing > Suits",
        "Apparel & Accessories > Clothing > Outerwear > Vests",
        "Apparel & Accessories > Clothing > Skirts",
        "Apparel & Accessories > Clothing > Shorts",
        "Apparel & Accessories > Clothing > Swimwear",
        "Apparel & Accessories > Clothing",
    ],
)
def test_get_category_broad_impact_clothing_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.CLOTHING.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Apparel & Accessories > Clothing Accessories > Belts",
        "Luggage & Bags > Luggage Accessories > Travel Pouches",
        "Luggage & Bags > Messenger Bags",
        "Apparel & Accessories > Jewelry > Watch Accessories > Watch Bands",
        "Luggage & Bags > Suitcases",
        "Luggage & Bags > Cosmetic & Toiletry Bags",
        "Home & Garden > Kitchen & Dining > Food & Beverage Carriers > Lunch Boxes & Totes",
        "Apparel & Accessories > Clothing Accessories > Baby & "
        "Toddler Clothing Accessories > Baby & Toddler Hats",
        "Luggage & Bags > Duffel Bags",
        "Apparel & Accessories > Clothing Accessories > Sunglasses",
        "Apparel & Accessories > Jewelry > Watch Accessories",
        "Luggage & Bags > Luggage Accessories",
        "Apparel & Accessories > Clothing Accessories > Hats",
        "Sporting Goods > Outdoor Recreation > Winter Sports & Activities "
        "> Skiing & Snowboarding > Ski & Snowboard Goggles",
        "Apparel & Accessories > Jewelry > Watches",
        "Luggage & Bags > Backpacks",
        "Luggage & Bags",
        "Apparel & Accessories > Handbags, Wallets & Cases > Handbags",
        "Luggage & Bags > Garment Bags",
        "Luggage & Bags > Fanny Packs",
        "Vehicles & Parts > Vehicle Parts & Accessories > Vehicle Safety "
        "& Security > Motorcycle Protective Gear > Motorcycle Goggles",
        "Luggage & Bags > Shopping Totes",
        "Apparel & Accessories > Handbags, Wallets & Cases",
        "Luggage & Bags > Briefcases",
        "Health & Beauty > Personal Care > Sleeping Aids > Eye Masks",
    ],
)
def test_get_category_broad_impact_accessories_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.ACCESSORIES.value


def test_is_authenticated(impact_cleaner):
    assert impact_cleaner.is_authenticated() is False
