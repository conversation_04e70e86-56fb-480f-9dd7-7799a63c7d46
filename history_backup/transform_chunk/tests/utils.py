from datetime import datetime
from decimal import Decimal


def get_minimal_mock_product():
    return {"id": "id", "name": "Product Name", "last_seen_at": str(datetime.now())}


def get_minimal_mock_clean_product():
    return {"id": "id", "name": "Product Name", "last_seen_at": str(datetime.now())}


def compare_dates(date1, date2, threshold_minutes=2):
    """
    Compare two datetime objects to determine if they are within a certain threshold of minutes.

    Args:
    date1 (datetime): The first datetime object.
    date2 (datetime): The second datetime object.
    threshold_minutes (int): The number of minutes as a threshold for comparison.

    Returns:
    bool: True if the dates are within the threshold, otherwise False.
    """
    # Calculate the difference in time between the two dates and convert to minutes
    delta = abs(date1 - date2).total_seconds() / 60

    # Check if the absolute difference in minutes is less than or equal to the threshold
    return delta <= threshold_minutes


def compare_decimals(dec1, dec2, threshold=0.001):
    """
    Compare two decimal.Decimal objects to determine if they are within a certain threshold.

    Args:
    dec1 (decimal.Decimal): The first decimal number.
    dec2 (decimal.Decimal): The second decimal number.
    threshold (decimal.Decimal): The threshold for comparison as a decimal.Decimal object.

    Returns:
    bool: True if the decimals are within the threshold, otherwise False.
    """
    # Calculate the absolute difference between the two decimals
    difference = abs(dec1 - dec2)

    # Check if the absolute difference is less than or equal to the threshold
    return difference <= threshold


def compare_dict_values(expected, actual, threshold_minutes=2):
    """
    Compares two dictionaries for value equality, using a specific comparison for
    datetime values. If strict, all keys must be present.
    """
    if actual.keys() != expected.keys():
        print(
            "Fields in actual but not in expected: ",
            set(actual.keys()).difference(set(expected.keys())),
        )
        print(
            "Fields in expected but not in actual: ",
            set(expected.keys()).difference(set(actual.keys())),
        )
        return False

    for key in expected:
        if key not in actual:
            continue
        val1, val2 = actual[key], expected[key]

        if type(val1) != type(val2):  # noqa
            print("Types different", val1, val2, type(val1), type(val2))
            return False
        elif isinstance(val1, Decimal) and isinstance(val2, Decimal):
            if not compare_decimals(val1, val2):
                print("Decimals different", val1, val2)
                return False
        elif isinstance(val1, datetime) and isinstance(val2, datetime):
            if not compare_dates(val1, val2, threshold_minutes):
                print("Dates different", val1, val2)
                return False
        elif val1 != val2:
            print("Values different", key, val1, val2)
            return False
    return True
