import pytest

from curators.gender_curator import GenderCurator
from models.product import CleanProductSchema, Gender
from tests.utils import get_minimal_mock_clean_product


@pytest.fixture
def product():
    product = get_minimal_mock_clean_product()
    return CleanProductSchema().load(product)


@pytest.mark.parametrize(
    "gender_value", ["male", "man", "men", "Men", "MEN", "mens", "MENS", "men's"]
)
def test_curator_male(gender_value, product):
    product["gender"] = gender_value
    curator = GenderCurator(product)
    assert curator.get_gender() == Gender.MEN.value


@pytest.mark.parametrize(
    "gender_value", ["female", "women", "woman", "womens", "women's"]
)
def test_curator_female(gender_value, product):
    product["gender"] = gender_value
    curator = GenderCurator(product)
    assert curator.get_gender() == Gender.WOMEN.value


def test_curator_unisex(product):
    product["gender"] = "unisex"
    curator = Gender<PERSON>urator(product)
    assert curator.get_gender() == Gender.UNISEX.value
