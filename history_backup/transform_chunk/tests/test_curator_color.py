import pytest
from models.product import CleanProductSchema, Color
from curators.color_curator import ColorCurator
from tests.utils import get_minimal_mock_clean_product


@pytest.fixture
def product_no_color():
    product = get_minimal_mock_clean_product()
    product["color"] = None
    return CleanProductSchema().load(product)


@pytest.mark.parametrize(
    "color_value, expected_primary_color, expected_colors",
    [
        ("Brown", Color.BROWN.value, [Color.BROWN.value]),
        (
            "Red, White, Black",
            Color.RED.value,
            [Color.RED.value, Color.WHITE.value, Color.BLACK.value],
        ),
        ("Dusty Rose", Color.RED.value, [Color.RED.value]),
        ("Multicolor", None, []),
        ("Green", Color.GREEN.value, [Color.GREEN.value]),
        (
            "Bone/Navy",
            Color.WHITE.value,
            [Color.WHITE.value, Color.BLUE.value],
        ),
        (
            "Blue/ Green/ Pattern Prints/ White",
            Color.BLUE.value,
            [Color.BLUE.value, Color.GREEN.value, Color.PRINT.value, Color.WHITE.value],
        ),
    ],
)
def test_curator_color(
    color_value, expected_primary_color, expected_colors, product_no_color
):
    product_no_color["color"] = color_value
    curator = ColorCurator(product_no_color)
    primary_color, colors = curator.get_colors()
    assert expected_primary_color == primary_color
    assert expected_colors == colors


@pytest.mark.parametrize(
    "color_value, description, expected_primary_color, expected_colors",
    [
        ("Multicolor", "Blue Jeans", Color.BLUE.value, [Color.BLUE.value]),
        (
            "Multicolor",
            "Dust Blue Jeans",
            Color.GREY.value,
            [Color.GREY.value, Color.BLUE.value],
        ),
        (
            "Multicolor",
            "Reformation’s tank top is the perfect staple your well-edited wardrobe needs. "
            "It is cut from fine ribbed fabric with a square neckline for an alluring and "
            "refreshing appearance. This top is finished with bold pink and white stripes "
            "for an eye-catching appeal. It is great for a day out with friends. "
            "Reformation Striped Tank Top in Pink and White Tencel Condition: "
            "Excellent Sign of wear: No Size: XS Color: Pink and White Material: "
            "Tencel Sku: 148679",
            Color.PINK.value,
            [Color.PINK.value, Color.WHITE.value],
        ),
    ],
)
def test_curator_color_with_description(
    color_value,
    description,
    expected_primary_color,
    expected_colors,
    product_no_color,
):
    product_no_color["color"] = color_value
    product_no_color["description"] = description
    curator = ColorCurator(product_no_color)
    primary_color, colors = curator.get_colors()
    assert expected_primary_color == primary_color
    # assert expected_colors == colors


@pytest.mark.parametrize(
    "color_value, name, expected_primary_color, expected_colors",
    [
        ("Multicolor", "Blue Jeans", Color.BLUE.value, [Color.BLUE.value]),
        (
            "Multicolor",
            "Dust Blue Jeans",
            Color.GREY.value,
            [Color.GREY.value, Color.BLUE.value],
        ),
        ("Multicolor", "Liu.Jo Silk shirt", Color.WHITE.value, [Color.WHITE.value]),
    ],
)
def test_curator_color_with_name(
    color_value,
    name,
    expected_primary_color,
    expected_colors,
    product_no_color,
):
    product_no_color["color"] = color_value
    product_no_color["name"] = name
    curator = ColorCurator(product_no_color)
    primary_color, colors = curator.get_colors()
    assert expected_primary_color == primary_color
    assert expected_colors == colors


@pytest.mark.parametrize(
    "color_value, name, description, expected_primary_color, expected_colors",
    [
        (
            "Multicolor",
            "Nicole Farhi Silk mid-length dress",
            "Nicole Farhi's dress, made from a high quality silk blend (69% cotton, 31% silk) "
            "and 100% silk lining. The abstract camel black pattern makes this dress unique. "
            "It's fitted beautifully, with a flared skirt - perfect for casual weekends and vacations."
            " This dress is unworn condition - no marks or damage as seen in the photos. "
            "I will be listing more high quality items from my personal collection (shoes, handbags, clothing)",
            Color.WHITE.value,
            [Color.WHITE.value, Color.BLACK.value],
        ),
    ],
)
def test_curator_color_with_description_and_name(
    color_value,
    name,
    description,
    expected_primary_color,
    expected_colors,
    product_no_color,
):
    product_no_color["color"] = color_value
    product_no_color["name"] = name
    product_no_color["description"] = description
    curator = ColorCurator(product_no_color)
    primary_color, colors = curator.get_colors()
    assert expected_primary_color == primary_color
    assert expected_colors == colors
