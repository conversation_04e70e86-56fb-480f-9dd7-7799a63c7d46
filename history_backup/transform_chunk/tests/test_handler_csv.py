import json
import os

import pytest

from cleaners.impact_cleaner import ImpactCleaner
from cleaners.luxe_collective_cleaner import LuxeCollectiveCleaner
from field_id_mappers.field_id_mapper import FieldIdMapper
from file_handlers.csv_handler import (
    CSVHandler,
)
from enums import Partners
from tests.mock_data.sample_size_mapper import SAMPLE_SIZE_MAPPER


@pytest.fixture
def csv_file_path():
    return os.getcwd() + "/tests/mock_data/luxe_example.csv"


@pytest.fixture
def csv_stockx_file_path():
    return os.getcwd() + "/tests/mock_data/stockx_example.csv"


@pytest.fixture
def csv_stockx_file_path_with_blacklisted_category():
    return os.getcwd() + "/tests/mock_data/stockx_example_blacklisted.csv"


@pytest.fixture
def csv_hewi_file_path():
    return os.getcwd() + "/tests/mock_data/hewi_example.csv"


@pytest.fixture
def jsonl_file_path():
    return os.getcwd() + "test_output.jsonl"


def test_parse_and_process(csv_file_path, jsonl_file_path):
    field_id_mappers = FieldIdMapper(SAMPLE_SIZE_MAPPER, {}, [], {})
    handler = CSVHandler(
        Partners.LUXE_COLLECTIVE.value,
        LuxeCollectiveCleaner,
        "test_1712808874_0.csv",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(csv_file_path, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 2
        first_item = json.loads(lines[0].strip())
        assert (
            first_item["name"]
            == "Adidas Adidas Superstar Mickey Mouse White White Leather EU 42 UK 8"
        )
        assert first_item["description"].startswith(
            "<p><strong>Brand: </strong>Adidas<br><strong>Style: </strong>Adidas Superstar Mickey "
            + "Mouse White<br><strong>Size: </strong>EU 42 UK 8<br><strong>Condition: </strong>"
            + "Unused<br><strong>Colour: </strong>White<br><strong>Material: </strong>Leather<br>"
            + "<strong>Comes With: </strong>Original Box and Dustbag<br></p>"
        )
        assert first_item["product_url"].startswith(
            "https://luxe-collective.pxf.io/c/4095344/1432797/17008?prodsku=44462136656120"
        )
        assert (
            first_item["img_url"]
            == "https://cdn.shopify.com/s/files/1/0459/8398/9928/files/CONS-0154_1.jpg?v=1708505836"
        )
        assert first_item["price_usd"] == 85.0
        assert first_item["meta"] == {"filepath": "test_1712808874_0.csv"}
        assert first_item["gender"] == "MEN"

        second_Item = json.loads(lines[1].strip())
        assert second_Item["gender"] == "WOMEN"
        assert second_Item["category_broad"] == "shoe"
        assert second_Item["size"] == {
            "id": "clx0p5dwc0013mxms8i2yn51z",
            "mapped_size_system": "",
            "raw_size_system": None,
            "raw_size_value": "EU 38 UK 5",
            "us_size_value": "7",
        }

    # Clean up the test JSONL file after the test
    os.remove(jsonl_file_path)


def test_parse_and_process_stockx(csv_stockx_file_path, jsonl_file_path):
    field_id_mappers = FieldIdMapper({}, {}, [], {})
    handler = CSVHandler(
        Partners.STOCKX.value,
        ImpactCleaner,
        "test_1712808874_0.csv",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(csv_stockx_file_path, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 1
        first_item = json.loads(lines[0].strip())
        assert (
            first_item["name"]
            == "100% Soft Dumpster Fire Slime Green Hot Topic Exclusive Vinyl Figure"
        )
        assert (
            first_item["img_url"]
            == "https://images.stockx.com/images/Asics-Gel-Lyte-III-Atmos-Duck%20Camo.jpg"
        )
        assert first_item["price_usd"] == 149.0

    # Clean up the test JSONL file after the test
    os.remove(jsonl_file_path)


def test_parse_and_process_stockx_blacklisted(
    csv_stockx_file_path_with_blacklisted_category, jsonl_file_path
):
    field_id_mappers = FieldIdMapper({}, {}, [], {})
    handler = CSVHandler(
        Partners.STOCKX.value,
        ImpactCleaner,
        "test_1712808874_0.csv",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(
        csv_stockx_file_path_with_blacklisted_category, jsonl_file_path
    )

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 0

    # Clean up the test JSONL file after the test
    os.remove(jsonl_file_path)


def test_parse_and_process_hewi(csv_hewi_file_path, jsonl_file_path):
    field_id_mappers = FieldIdMapper({"women_clothing_1": "some_id"}, {}, [], {})
    handler = CSVHandler(
        Partners.HEWI.value,
        ImpactCleaner,
        "test_1712808874_0.csv",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(csv_hewi_file_path, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 1
        first_item = json.loads(lines[0].strip())
        assert (
            first_item["name"]
            == "Alice + Olivia Coral Zumi Halterneck Maxi Dress Size XXS"
        )
        assert first_item["size"]["raw_size_value"] == "XXS"
        assert first_item["size"]["us_size_value"] == "1"
        assert first_item["size"]["id"] == "some_id"

    # Clean up the test JSONL file after the test
    os.remove(jsonl_file_path)
