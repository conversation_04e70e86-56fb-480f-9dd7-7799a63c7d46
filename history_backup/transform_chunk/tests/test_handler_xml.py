import json
import os
import pytest

from cleaners.farfetch_cleaner import FarfetchCleaner
from cleaners.the_luxury_closet_cleaner_cj import TheLuxuryClosetCleanerCJ
from cleaners.vestiaire_collective_cleaner import VestiaireCollectiveCleaner
from field_id_mappers.field_id_mapper import FieldIdMapper
from file_handlers.xml_handler import (
    XMLHandler,
)
from cleaners.the_luxury_closet_cleaner import TheLuxuryClosetCleaner
from enums import Partners
from tests.mock_data.sample_size_mapper import SAMPLE_SIZE_MAPPER


@pytest.fixture
def xml_file_path():
    return os.getcwd() + "/tests/mock_data/vestiaire_example.xml"


@pytest.fixture
def xml_file_path_item_tag():
    return os.getcwd() + "/tests/mock_data/the_luxury_closet_cj.xml"


@pytest.fixture
def xml_file_path_item_farfetch():
    return os.getcwd() + "/tests/mock_data/farfetch_example.xml"


@pytest.fixture
def xml_file_path_item_farfetch_one_size():
    return os.getcwd() + "/tests/mock_data/farfetch_one_size_example.xml"


@pytest.fixture
def xml_file_path_empty():
    return os.getcwd() + "/tests/mock_data/empty_file.xml"


@pytest.fixture
def jsonl_file_path():
    return "test_output.jsonl"


def test_parse_and_process(xml_file_path, jsonl_file_path):
    field_id_mappers = FieldIdMapper({}, {}, [], {})
    handler = XMLHandler(
        Partners.VESTIAIRE_COLLECTIVE.value,
        VestiaireCollectiveCleaner,
        "test_1712808874_0.csv",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(xml_file_path, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 2
        first_item = json.loads(lines[0].strip())
        assert first_item["name"] == "Valentino Garavani Leather trainers"
        assert (
            first_item["description"]
            == "worn a few times, grey-white, very comfortable, paid €650"
        )
        assert first_item["product_url"].startswith(
            "https://www.anrdoezrs.net/click-100832946-14518558"
        )
        assert first_item["img_url"].endswith("42569304-1_2.jpg")
        assert first_item["price_usd"] == 1233.70
        assert first_item["is_authenticated"] is True

        second_item = json.loads(lines[1].strip())
        assert second_item["price_usd"] == 177.23
        assert second_item["is_authenticated"] is False

    # Clean up the test JSONL file after the test
    os.remove(jsonl_file_path)


def test_parse_and_process_different_tag(xml_file_path_item_tag, jsonl_file_path):
    field_id_mappers = FieldIdMapper({}, {}, [], {})
    handler = XMLHandler(
        Partners.THE_LUXURY_CLOSET.value,
        TheLuxuryClosetCleanerCJ,
        "test_1712808874_0.xml",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(xml_file_path_item_tag, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 1
        first_item = json.loads(lines[0].strip())
        assert (
            first_item["name"]
            == "Salvatore Ferragamo Black Textured Leather Penny Loafers Size 44.5"
        )
        assert (
            first_item["description"]
            == "Made from black textured leather, these loafers from "
            "Salvatore Ferragamo are smart and comfortable. "
            "They feature penny keeper straps on the vamps, snug, "
            "labeled insoles, and leather outsoles. Flaunt them with "
            "both your casual and formal looks."
        )
        assert first_item["product_url"].startswith(
            "https://www.dpbolvw.net/click-100832946-15447452?url=https%3A%2F%2Ftheluxurycloset.com%2Fus-en%2Fmen%2Fsalvatore-ferragamo-black-textured-leather-penny-loafers-size-445-p326982"
        )
        assert first_item["img_url"].endswith(
            "luxury-men-salvatore-ferragamo-used-shoes-p326982-002.jpg"
        )
        assert first_item["price_usd"] == 292.00
        assert first_item["is_authenticated"] is True

    # Clean up the test JSONL file after the test runs
    os.remove(jsonl_file_path)


def test_parse_and_farfetch(xml_file_path_item_farfetch, jsonl_file_path):
    field_id_mappers = FieldIdMapper(SAMPLE_SIZE_MAPPER, {}, [], {})
    handler = XMLHandler(
        Partners.FARFETCH.value,
        FarfetchCleaner,
        "t.xml",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(xml_file_path_item_farfetch, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 1
        first_item = json.loads(lines[0].strip())
        assert (
            first_item["name"]
            == "CHANEL Pre-Owned - 1998 logo-lettering sheer jacket - women - Cotton/Nylon - 38 - Black"
        )
        assert (
            first_item["description"] == "1998 logo-lettering sheer jacket from"
            " Chanel Pre-Owned featuring black,"
            " cotton blend, logo lettering, semi-sheer construction,"
            " open front, three-quarter length sleeves and circa 1998. "
            "POSITIVELY CONSCIOUS: Purchasing this item continues "
            "its narrative and reduces the environmental impact of"
            " using new resources. You can be confident that you’re "
            "making a better choice for the Planet.."
        )
        assert first_item["product_url"].startswith(
            "https://prf.hn/click/camref:1101lvUFL"
        )
        assert first_item["img_url"].endswith("18555186_40118261_1000.jpg")
        assert first_item["price_usd"] == 3870
        assert first_item["size"] == {
            "id": "clx0p5jx5002qmxms82rq1ve4",
            "mapped_size_system": "FR",
            "raw_size_system": "France",
            "raw_size_value": "38",
            "us_size_value": "6",
        }

    # Clean up the test JSONL file after the test runs
    os.remove(jsonl_file_path)


def test_parse_and_farfetch_one_size(
    xml_file_path_item_farfetch_one_size, jsonl_file_path
):
    field_id_mappers = FieldIdMapper(SAMPLE_SIZE_MAPPER, {}, [], {})
    handler = XMLHandler(
        Partners.FARFETCH.value,
        FarfetchCleaner,
        "t.xml",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(xml_file_path_item_farfetch_one_size, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 1
        first_item = json.loads(lines[0].strip())
        assert (
            first_item["name"]
            == "CHANEL Pre-Owned - 1998 logo-lettering sheer jacket - women - Cotton/Nylon - 38 - Black"
        )
        assert (
            first_item["description"] == "1998 logo-lettering sheer jacket from"
            " Chanel Pre-Owned featuring black,"
            " cotton blend, logo lettering, semi-sheer construction,"
            " open front, three-quarter length sleeves and circa 1998. "
            "POSITIVELY CONSCIOUS: Purchasing this item continues "
            "its narrative and reduces the environmental impact of"
            " using new resources. You can be confident that you’re "
            "making a better choice for the Planet.."
        )
        assert first_item["product_url"].startswith(
            "https://prf.hn/click/camref:1101lvUFL"
        )
        assert first_item["img_url"].endswith("18555186_40118261_1000.jpg")
        assert first_item["price_usd"] == 3870
        assert first_item["size"] == {
            "id": "clzhj3rao000howyflohp05t9",
            "mapped_size_system": "",
            "raw_size_system": "One Size",
            "raw_size_value": "One Size",
            "us_size_value": "One Size",
        }

    # Clean up the test JSONL file after the test runs
    os.remove(jsonl_file_path)


def test_parse_and_process_empty(xml_file_path_empty, jsonl_file_path):
    field_id_mappers = FieldIdMapper({}, {}, [], {})
    handler = XMLHandler(
        Partners.VESTIAIRE_COLLECTIVE.value,
        VestiaireCollectiveCleaner,
        "test_1712808874_0.csv",
        field_id_mappers,
    )
    handler.parse_and_process(xml_file_path_empty, jsonl_file_path)
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        # Empty file should have no output lines
        assert len(lines) == 0

    os.remove(jsonl_file_path)
