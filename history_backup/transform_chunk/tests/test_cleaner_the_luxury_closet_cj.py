import pytest

from cleaners.the_luxury_closet_cleaner import (
    TheLuxuryClosetCleaner,
    extract_condition,
    THE_LUX_CLOSET_CONDITION_MAP,
)
from cleaners.the_luxury_closet_cleaner_cj import TheLuxuryClosetCleanerCJ
from enums import Partners
from models.product import Condition


def get_mock_item():
    return """
<feed>
<entry>
<program_name>The Luxury Closet</program_name>
<program_url>https://theluxurycloset.com</program_url>
<catalog_name>CJ_Test</catalog_name>
<last_updated>2024-08-23T03:48:22.477-07:00</last_updated>
<id>326982</id>
<title>Salvatore Ferragamo Black Textured Leather Penny Loafers Size 44.5</title>
<description>Made from black textured leather, these loafers from Salvatore Ferragamo are smart and comfortable. They feature penny keeper straps on the vamps, snug, labeled insoles, and leather outsoles. Flaunt them with both your casual and formal looks.</description>
<link>https://www.dpbolvw.net/click-100832946-15447452?url=https%3A%2F%2Ftheluxurycloset.com%2Fus-en%2Fmen%2Fsalvatore-ferragamo-black-textured-leather-penny-loafers-size-445-p326982</link>
<impression_url>https://www.awltovhc.com/image-100832946-15447452</impression_url>
<image_link>http://cdn.theluxurycloset.com/uploads/products/full/luxury-men-salvatore-ferragamo-used-shoes-p326982-002.jpg</image_link>
<additional_image_link>http://cdn.theluxurycloset.com/uploads/products/full/luxury-men-salvatore-ferragamo-used-shoes-p326982-009.jpg</additional_image_link>
<additional_image_link>http://cdn.theluxurycloset.com/uploads/products/full/luxury-men-salvatore-ferragamo-used-shoes-p326982-005.jpg</additional_image_link>
<additional_image_link>http://cdn.theluxurycloset.com/uploads/products/full/luxury-men-salvatore-ferragamo-used-shoes-p326982-008.jpg</additional_image_link>
<additional_image_link>http://cdn.theluxurycloset.com/uploads/products/full/luxury-men-salvatore-ferragamo-used-shoes-p326982-004.jpg</additional_image_link>
<additional_image_link>http://cdn.theluxurycloset.com/uploads/products/full/luxury-men-salvatore-ferragamo-used-shoes-p326982-003.jpg</additional_image_link>
<additional_image_link>http://cdn.theluxurycloset.com/uploads/products/full/luxury-men-salvatore-ferragamo-used-shoes-p326982-007.jpg</additional_image_link>
<additional_image_link>http://cdn.theluxurycloset.com/uploads/products/full/luxury-men-salvatore-ferragamo-used-shoes-p326982-007.jpg</additional_image_link>
<availability>in stock</availability>
<price>292.00 USD</price>
<sale_price>292.00 USD</sale_price>
<google_product_category>187</google_product_category>
<google_product_category_name>Apparel &amp; Accessories &gt; Shoes</google_product_category_name>
<product_type>Loafers &amp; Slip-Ons</product_type>
<brand>Salvatore Ferragamo</brand>
<mpn>p326982</mpn>
<identifier_exists>yes</identifier_exists>
<condition>used</condition>
<is_bundle>no</is_bundle>
<age_group>adult</age_group>
<color>Black</color>
<gender>male</gender>
<material>Leather</material>
<size>44.5</size>
<product_highlight>[]</product_highlight>
</entry>
</feed>
    """


@pytest.fixture
def cleaner():
    return TheLuxuryClosetCleanerCJ(
        Partners.THE_LUXURY_CLOSET.value, get_mock_item(), "test_file.xml"
    )


@pytest.mark.parametrize(
    "condition_detail, expected_condition",
    [
        (
            "Good - This item is in good condition with minor fading on the main "
            "fabric and minor pilling throughout the knit trim fabric.",
            Condition.VERY_GOOD.value,
        ),
        (
            # Note that we return Pristine because excellent(which maps to pristine) > very good
            "Excellent - This item is in very good condition with negligible signs of use on the main fabric.",
            Condition.PRISTINE.value,
        ),
        (
            "Pristine - This item is in excellent condition and comes with tags attached.",
            Condition.PRISTINE.value,
        ),
        (
            "Fair - This item has visible darkening on the exterior material, "
            "minor scratches on the hardware. 0verall, it is in fair condition.",
            Condition.GOOD.value,
        ),
    ],
)
def test_condition_cleaning_the_luxury_closet(condition_detail, expected_condition):
    assert (
        extract_condition(condition_detail, THE_LUX_CLOSET_CONDITION_MAP)
        == expected_condition
    )


def test_is_authenticated(cleaner):
    assert cleaner.is_authenticated() is True
