import pytest
from cleaners.ebay_cleaner import (
    EbayCleaner,
    EbayAvailability,
    get_ebay_category_broad_from_category_raw,
    ebay_product_url_affiliate_like_substitution,
)
from enums import Partners
from models.product import CategoryBroad, Condition
from tests.utils import compare_dict_values
from decimal import Decimal
from datetime import datetime


def get_mock_item(availability, price="23.74"):
    return {
        "ItemId": "v1|************|0",
        "Availability": availability,
        "Title": "Design History Women Black Casual Skirt M\x00",
        "ImageUrl": "https://i.ebayimg.com/images/g/XDgAAOSw6bBmM4Gk/s-l1600.jpg",
        "Category": "Clothing, Shoes & Accessories|Women|Women's Clothing|Skirts",
        "CategoryId": "63864",
        "BuyingOptions": "",
        "SellerUsername": "thredup",
        "SellerFeedbackPercentage": "98.5",
        "SellerFeedbackScore": "222135",
        "GTIN": "",
        "Brand": "Design History",
        "MPN": "",
        "EPID": "",
        "ConditionId": "3000",
        "Condition": "USED",
        "PriceValue": price,
        "PriceCurrency": "USD",
        "PrimaryItemGroupId": "",
        "PrimaryItemGroupType": "",
        "ItemEndDate": "",
        "SellerItemRevision": "",
        "ItemLocationCountry": "US",
        "SellerTrustLevel": "ABOVE_STANDARD",
        "ImageAlteringProhibited": "false",
        "EstimatedAvailableQuantity": "1",
        "AvailabilityThresholdType": "",
        "AvailabilityThreshold": "",
        "ItemSnapshotDate": "2024-05-02T13:05:27.556Z",
        "OriginalPriceValue": "115.0",
        "OriginalPriceCurrency": "USD",
        "DiscountAmount": "91.26",
        "DiscountPercentage": "79.00",
        "ReturnsAccepted": "TRUE",
        "ReturnPeriodValue": "30",
        "ReturnPeriodUnit": "CALENDAR_DAY",
        "ReturnShippingCostPayer": "SELLER",
        "EnergyEfficiencyClass": "",
        "AdditionalImageUrls": "https://i.ebayimg.com/images/"
        "g/GyMAAOSwM7VmM4Gj/s-l1600.jpg|"
        "https://i.ebayimg.com/images/g/GyMAAOSwM7VmM4Gj/s-l1605.jpg",
        "DeliveryOptions": "SHIP_TO_HOME",
        "ShipToIncludedRegions": "COUNTRY:US;",
        "AcceptedPaymentMethods": "",
        "QualifiedPrograms": "",
        "LotSize": "",
        "ShippingCarrierCode": "",
        "ShippingServiceCode": "",
        "ShippingType": "STANDARD",
        "ShippingCost": "0.00",
        "ShippingCostType": "FIXED",
        "AdditionalShippingCostPerUnit": "",
        "QuantityUsedForEstimate": "",
        "UnitPrice": "",
        "UnitPricingMeasure": "",
        "InferredEPID": "",
        "ItemCreationDate": "2024-05-02T12:00:33.000Z",
        "LegacyItemId": "************",
        "Alerts": "",
        "SellerAccountType": "",
        "TyreLabelImageUrl": "",
        "AgeGroup": "",
        "Color": "Black",
        "Pattern": "Solid",
        "Size": "M",
        "Gender": "Women",
        "Material": "Viscose",
        "TotalUnits": "",
        "DefaultImageUrl": "https://i.ebayimg.com/images/g/XDgAAOSw6bBmM4Gk/s-l1600.jpg",
        "ItemWebUrl": "https://www.ebay.com/itm/************?var=0",
        "ItemAffiliateWebUrl": "https://www.ebay.com/itm/************?"
        "var=0&mkevt=1&mkcid=1&mkrid=711-53200-"
        "19255-0&campid=CAMPAIGNID&customid="
        "CUSTOMID&toolid=20006",
        "Description": "The Largest Online Consignment &amp; Thrift Store",
        "SellerUserId": "U89JVUAITnu",
    }


@pytest.fixture
def mock_item():
    return get_mock_item(EbayAvailability.AVAILABLE.value)


@pytest.fixture
def mock_item_unavailable():
    return get_mock_item(EbayAvailability.UNAVAILABLE.value)


@pytest.fixture
def mock_item_price_above_500():
    return get_mock_item(EbayAvailability.AVAILABLE.value, price="500.00")


@pytest.fixture
def ebay_cleaner_price_above_500(mock_item_price_above_500):
    return EbayCleaner(
        Partners.EBAY.value, mock_item_price_above_500, "ebay_1714766400_6.tsv"
    )


@pytest.fixture
def ebay_cleaner_available_item(mock_item):
    return EbayCleaner(Partners.EBAY.value, mock_item, "ebay_1714766400_6.tsv")


@pytest.fixture
def ebay_cleaner_unavailable_item(mock_item_unavailable):
    return EbayCleaner(
        Partners.EBAY.value, mock_item_unavailable, "ebay_1714766400_6.tsv"
    )


def test_ebay_create_item_available(ebay_cleaner_available_item):
    expected = {
        "id": "ebay-v1|************|0",
        "name": "Design History Women Black Casual Skirt M",
        "description": "The Largest Online Consignment &amp; Thrift Store",
        "price_usd": Decimal("23.74"),
        "product_url": "https://www.ebay.com/itm/************?"
        "var=0&mkevt=1&mkcid=1&mkrid=711-53200-"
        "19255-0&campid=CAMPAIGNID&customid="
        "CUSTOMID&toolid=20006",
        "img_url": "https://i.ebayimg.com/images/g/XDgAAOSw6bBmM4Gk/s-l1600.jpg",
        "additional_img_urls": [
            "https://i.ebayimg.com/images/g/GyMAAOSwM7VmM4Gj/s-l1600.jpg",
            "https://i.ebayimg.com/images/g/GyMAAOSwM7VmM4Gj/s-l1605.jpg",
        ],
        "secondhand_retailer": "ebay",
        "is_available": True,
        "is_authenticated": False,
        "is_returnable": True,
        "color": "Black",
        "gender": "Women",
        "category": "Clothing, Shoes & Accessories|Women|Women's Clothing|Skirts",
        "brand": "Design History",
        "category_broad": "clothing",
        "condition": Condition.GOOD.value,
        "material": "Viscose",
        "raw_id": "v1|************|0",
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "last_seen_at": datetime.fromtimestamp(1714766400),
        "raw_size": "M",
        "is_approved_seller": False,
    }
    expected["product_url"] = ebay_product_url_affiliate_like_substitution(
        expected["product_url"]
    )
    actual = ebay_cleaner_available_item.create_item()
    assert compare_dict_values(actual=actual, expected=expected)


def test_create_item_not_available(ebay_cleaner_unavailable_item):
    assert ebay_cleaner_unavailable_item.create_item() is not None


def test_is_available(ebay_cleaner_available_item):
    assert ebay_cleaner_available_item.is_available()


def test_get_name(ebay_cleaner_available_item):
    assert (
        ebay_cleaner_available_item.get_name()
        == "Design History Women Black Casual Skirt M"
    )


def test_get_price(ebay_cleaner_available_item):
    assert ebay_cleaner_available_item.get_price() == 23.74


def test_get_authenticity(ebay_cleaner_available_item, ebay_cleaner_price_above_500):
    assert ebay_cleaner_available_item.is_authenticated() is False
    assert ebay_cleaner_price_above_500.is_authenticated() is True


def test_get_product_url(ebay_cleaner_available_item):
    assert (
        ebay_cleaner_available_item.get_product_url()
        == ebay_product_url_affiliate_like_substitution(
            "https://www.ebay.com/itm/************?"
            "var=0&mkevt=1&mkcid=1&mkrid=711-53200-"
            "19255-0&campid=CAMPAIGNID&customid="
            "CUSTOMID&toolid=20006"
        )
    )


def test_affiliate_url_replacement():
    link = (
        "https://www.ebay.com/itm/************?var=0&mkevt=1&mkcid="
        "1&mkrid=711-53200-19255-0&campid=CAMPAIGNID&customid=CUSTOMID&toolid=20006"
    )
    assert (
        ebay_product_url_affiliate_like_substitution(link)
        == "https://www.ebay.com/itm/************?var=0&mkevt"
        "=1&mkcid=1&mkrid=711-53200-19255-0&campid=5339031575&toolid=20006"
    )


def test_get_img_url(ebay_cleaner_available_item):
    assert (
        ebay_cleaner_available_item.get_img_url()
        == "https://i.ebayimg.com/images/g/XDgAAOSw6bBmM4Gk/s-l1600.jpg"
    )


def test_get_additional_img_urls(ebay_cleaner_available_item):
    assert ebay_cleaner_available_item.get_additional_img_urls() == [
        "https://i.ebayimg.com/images/g/GyMAAOSwM7VmM4Gj/s-l1600.jpg",
        "https://i.ebayimg.com/images/g/GyMAAOSwM7VmM4Gj/s-l1605.jpg",
    ]


def test_get_description(ebay_cleaner_available_item):
    assert (
        ebay_cleaner_available_item.get_description()
        == "The Largest Online Consignment &amp; Thrift Store"
    )


def test_get_material(ebay_cleaner_available_item):
    assert ebay_cleaner_available_item.get_material() == "Viscose"


def test_get_raw_id(ebay_cleaner_available_item):
    assert ebay_cleaner_available_item.get_raw_id() == "v1|************|0"


def test_get_condition(ebay_cleaner_available_item):
    assert ebay_cleaner_available_item.get_condition() == Condition.GOOD.value


def test_get_condition_pristine(ebay_cleaner_available_item):
    ebay_cleaner_available_item.item["Condition"] = "NEW_OTHERS"
    assert ebay_cleaner_available_item.get_condition() == Condition.PRISTINE.value


def test_get_condition_new_w_defects(ebay_cleaner_available_item):
    ebay_cleaner_available_item.item["Condition"] = "NEW_WITH_DEFECTS"
    assert ebay_cleaner_available_item.get_condition() == Condition.VERY_GOOD.value


def test_get_condition_new_w_box(ebay_cleaner_available_item):
    ebay_cleaner_available_item.item["Condition"] = "USED"
    assert ebay_cleaner_available_item.get_condition() == Condition.GOOD.value


# Ebay categories


@pytest.mark.parametrize(
    "raw_category",
    [
        "Clothing, Shoes & Accessories|Men|Men's Shoes|Boots",
        "Clothing, Shoes & Accessories|Men|Men's Shoes|Dress Shoes",
        "Clothing, Shoes & Accessories|Women|Women's Shoes|Slippers",
        "Clothing, Shoes & Accessories|Men|Men's Shoes|Slippers",
        "Clothing, Shoes & Accessories|Women|Women's Shoes|Boots",
        "Clothing, Shoes & Accessories|Women|Women's Shoes|Flats",
        "Clothing, Shoes & Accessories|Men|Men's Shoes|Athletic Shoes",
        "Clothing, Shoes & Accessories|Women|Women's Shoes|Athletic Shoes",
        "Clothing, Shoes & Accessories|Men|Men's Shoes|Casual Shoes",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal Occasion|Bridal Shoes",
    ],
)
def test_get_category_broad_ebay_shoes_from_category_raw(raw_category):
    category_broad = get_ebay_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.SHOES.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Clothing, Shoes & Accessories|Specialty|Wedding & "
        "Formal Occasion|Men's Formal Occasion|Other Men's Formal Occasion",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage "
        "Clothing|Suit Jackets & Blazers",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional Clothing|Scotland",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Shorts",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional Clothing|Germany|Men's "
        "Traditional Clothing|Shirts",
        "Clothing, Shoes & Accessories|Specialty|Dancewear|Kids' Dancewear|Tops, Shirts",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Sweaters",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Sleepwear & Robes",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's Vintage Clothing|Skirts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Women's Vintage Clothing|Swimwear",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Intimates & Sleep|Sleepwear & Robes",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional Clothing|Germany|Women's "
        "Traditional Clothing|Tops & Blouses",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage Clothing|Swimwear",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional Clothing|Germany|Women's "
        "Traditional Clothing|Skirts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's Vintage Clothing|Suits",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Shorts",
        "Clothing, Shoes & Accessories|Specialty|Uniforms & Work Clothing|Lab Coats",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's "
        "Vintage Clothing|Shirts & Tops",
        "Clothing, Shoes & Accessories|Specialty|Costumes, Reenactment, Theater|Costumes|Girls",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Activewear|Hoodies & Sweatshirts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's Vintage Clothing|Dresses",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional "
        "Clothing|India & Pakistan|Sari, Saree",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's Vintage Clothing|Swimwear",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal Occasion|Men's "
        "Formal Occasion|Tuxedo & Formal Vests",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Skirts",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Suits & Suit Separates",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's Vintage Clothing|Sleepwear",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Women's Vintage "
        "Clothing|Sleepwear & Robes",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's Vintage Clothing|Sweaters",
        "Clothing, Shoes & Accessories|Specialty|Costumes, Reenactment, Theater|Costumes|Boys",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Women's Vintage Clothing|Shorts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage Clothing|T-Shirts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage Clothing|Dress Shirts",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Sweaters",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal "
        "Occasion|Men's Formal Occasion|Tuxedo & Formal Jackets",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Women's Vintage Clothing|Skirts",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Coats, Jackets & Vests",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional "
        "Clothing|India & Pakistan|Kurta",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Activewear|Hoodies & Sweatshirts",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Coats, Jackets & Vests",
        "Clothing, Shoes & Accessories|Specialty|Dancewear|Adult Dancewear|Skirts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage Clothing|Casual Shirts",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Swimwear",
        "Clothing, Shoes & Accessories|Specialty|Dancewear|Adult Dancewear|Tops, Shirts",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional "
        "Clothing|Asia & Pacific Islands|Jackets",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's Vintage Clothing|Vests",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Dresses",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Swimwear",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal Occasion|Men's "
        "Formal Occasion|Tuxedo & Formal Shirts",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Outfits & Sets",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal Occasion|Bridesmaid Dresses",
        "Clothing, Shoes & Accessories|Specialty|Uniforms & Work Clothing|Shirts",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional Clothing|Germany|Women's "
        "Traditional Clothing|Dirndls & Dresses",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage Clothing|Shorts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Children's Vintage Clothing|Shorts",
        "Clothing, Shoes & Accessories|Specialty|Costumes, Reenactment, Theater|Costumes|Women",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage Clothing|Suits",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Shirts|Polos",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Women's Vintage Clothing|Tops",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Women's Vintage Clothing|Dresses",
        "Clothing, Shoes & Accessories|Women|Women's Clothing|Tops",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal Occasion|Men's "
        "Formal Occasion|Tuxedos & Formal Suits",
        "Clothing, Shoes & Accessories|Specialty|Uniforms & Work Clothing|Scrubs|Tops",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Shirts|Casual Button-Down Shirts",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional Clothing|India "
        "& Pakistan|Choli",
        "Clothing, Shoes & Accessories|Specialty|Vintage"
        "|Men's Vintage Clothing|Sleepwear & Robes",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Shirts|T-Shirts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage Clothing|Vests",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Shirts|Dress Shirts",
        "Clothing, Shoes & Accessories|Specialty|World & Traditional Clothing|India "
        "& Pakistan|Salwar Kameez",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Suits & Suit Separates",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Women's Vintage Clothing|Sweaters",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Men's Vintage Clothing|Sweaters",
    ],
)
def test_get_category_broad_ebay_clothing_from_category_raw(raw_category):
    category_broad = get_ebay_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.CLOTHING.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Clothing, Shoes & Accessories|Specialty|Dancewear|Kids' Dancewear|Pants & Shorts",
    ],
)
def test_get_category_broad_ebay_pants_from_category_raw(raw_category):
    category_broad = get_ebay_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.PANTS.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Clothing, Shoes & Accessories|Specialty|Vintage|Vintage Accessories|Women's Hats",
        "Clothing, Shoes & Accessories|Specialty|Uniforms & Work Clothing|Hats",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Vintage Accessories|Bags, Handbags"
        " & Cases",
        "Clothing, Shoes & Accessories|Women|Women's Accessories|Belts",
        "Clothing, Shoes & Accessories|Women|Women's Accessories|Hats",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal Occasion|Bridal "
        "Accessories|Belts & Sashes",
        "Clothing, Shoes & Accessories|Women|Women's Accessories|Sunglasses & Sunglasses "
        "Accessories|Sunglasses",
        "Clothing, Shoes & Accessories|Men|Men's Accessories|Sunglasses & Sunglasses "
        "Accessories|Sunglasses",
        "Clothing, Shoes & Accessories|Specialty|Dancewear|Adult Dancewear|Totes, "
        "Duffel Bags",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Vintage Accessories|Sunglasses",
        "Clothing, Shoes & Accessories|Specialty|Costumes, Reenactment, "
        "Theater|Accessories|Bags & Pouches",
        "Clothing, Shoes & Accessories|Women|Women's Accessories|Sunglasses & "
        "Sunglasses Accessories|Replacement Lenses & Parts",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Vintage Accessories|Eyeglasses",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal Occasion|Bridal "
        "Accessories|Handbags & Bags",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Vintage Accessories|Men's Hats",
        "Clothing, Shoes & Accessories|Specialty|Dancewear|Kids' Dancewear|Totes, Duffel Bags",
        "Clothing, Shoes & Accessories|Specialty|Clothing & Shoe Care|Garment Bags",
        "Clothing, Shoes & Accessories|Women|Women's Bags & Handbags",
        "Clothing, Shoes & Accessories|Men|Men's Accessories|Wallets",
        "Clothing, Shoes & Accessories|Men|Men's Accessories|Hats",
        "Clothing, Shoes & Accessories|Specialty|Costumes, Reenactment, "
        "Theater|Accessories|Glasses",
        "Clothing, Shoes & Accessories|Men|Men's Accessories|Belts",
        "Clothing, Shoes & Accessories|Men|Men's Accessories|Sunglasses & Sunglasses "
        "Accessories|Accessories",
        "Clothing, Shoes & Accessories|Women|Women's Accessories|Wallets",
        "Clothing, Shoes & Accessories|Men|Men's Accessories|Sunglasses & Sunglasses"
        " Accessories|Replacement Lenses & Parts",
        "Clothing, Shoes & Accessories|Specialty|Wedding & Formal Occasion|Women's Formal Hats",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Vintage Accessories|Belts",
        "Clothing, Shoes & Accessories|Men|Men's Accessories|Bags",
        "Clothing, Shoes & Accessories|Specialty|Vintage|Vintage Accessories|Children's Hats",
        "Clothing, Shoes & Accessories|Women|Women's Accessories|Sunglasses & "
        "Sunglasses Accessories|Accessories",
    ],
)
def test_get_category_broad_ebay_accessories_from_category_raw(raw_category):
    category_broad = get_ebay_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.ACCESSORIES.value
