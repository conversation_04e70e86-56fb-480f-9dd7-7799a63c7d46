import pytest

from curators.size_curator import SizeCurator, extract_size_and_system
from models.product import CleanProductSchema
from tests.mock_data.sample_size_mapper import SAMPLE_SIZE_MAPPER
from tests.utils import get_minimal_mock_clean_product


@pytest.fixture
def product():
    product = get_minimal_mock_clean_product()
    return CleanProductSchema().load(product)


@pytest.mark.parametrize(
    "raw_size, size_system, category, gender, result",
    [
        ("10", None, "shoe", "men", "10"),
        ("37", "EU", "shoe", "men", "4"),
        ("41", "EU", "shoe", "men", "8"),
        ("41", "EU", "shoe", "men", "8"),
        ("7", "UK", "shoe", "men", "8"),
        ("41", "IT", "shoe", "men", "8"),
        ("10", None, "shoe", "women", "10"),
        ("37", "EU", "shoe", "women", "6"),
        ("41", "EU", "shoe", "women", "10"),
        ("7", "UK", "shoe", "women", "9"),
        ("41", "IT", "shoe", "women", "11"),
    ],
)
def test_curate_shoe_size(raw_size, size_system, category, gender, result, product):
    size_id_mapper = {}
    curator = SizeCurator(product, size_id_mapper)
    assert curator.get_us_size_value(raw_size, size_system, category, gender) == result


@pytest.mark.parametrize(
    "raw_size, size_system, category, gender, result",
    [
        ("22", None, "pants", "men", "22"),
        ("32", "FR", "pants", "men", "22"),
        ("32", "FR", "pants", "women", "22"),
        ("47", "FR", "pants", "men", "37"),
        ("47", "FR", "pants", "women", "37"),
    ],
)
def test_curate_pant_size(raw_size, size_system, category, gender, result, product):
    size_id_mapper = {}
    curator = SizeCurator(product, size_id_mapper)
    assert curator.get_us_size_value(raw_size, size_system, category, gender) == result


@pytest.mark.parametrize(
    "raw_size, size_system, category, gender, result",
    [
        ("34", None, "clothing", "men", "34"),
        ("44", "FR", "clothing", "men", "34"),
        ("50", "IT", "clothing", "men", "40"),
    ],
)
def test_curate_men_clothing_size(
    raw_size, size_system, category, gender, result, product
):
    size_id_mapper = {}
    curator = SizeCurator(product, size_id_mapper)
    assert curator.get_us_size_value(raw_size, size_system, category, gender) == result


@pytest.mark.parametrize(
    "raw_size, size_system, category, gender, result",
    [
        ("00", None, "clothing", "women", "00"),
        ("2", "UK", "clothing", "women", "00"),
        ("28", "FR", "clothing", "women", "00"),
        ("14", "UK", "clothing", "women", "10"),
    ],
)
def test_curate_women_clothing_size(
    raw_size, size_system, category, gender, result, product
):
    size_id_mapper = {}
    curator = SizeCurator(product, size_id_mapper)
    assert curator.get_us_size_value(raw_size, size_system, category, gender) == result


@pytest.mark.parametrize(
    "raw_size, size_system, category, gender, result",
    [
        ("12", "EU", "clothing", "men", None),
    ],
)
def test_curate_clothing_size_not_mapped(
    raw_size, size_system, category, gender, result, product
):
    size_id_mapper = {}
    curator = SizeCurator(product, size_id_mapper)
    assert curator.get_us_size_value(raw_size, size_system, category, gender) == result


@pytest.mark.parametrize(
    "raw_size, result",
    [
        ("UK 6/ US 7.5/ FR 39.5", ("US", "7.5")),
        ("US4.5=UK2.5=AU3=EUR", ("US", "4.5")),
        ("EU 38.5 UK 5.5", ("EU", "38.5")),
        ("US6, IT36 | 6", ("US", "6")),
        ("FR 36", ("FR", "36")),
        ("36 FR", ("FR", "36")),
        ("8", (None, "8")),
        ("Small", ("US", "36")),
        ("S", ("US", "36")),
        ("M", ("US", "38")),
        ("m", ("US", "38")),
        ("medium", ("US", "38")),
        ("l", ("US", "40")),
        ("large", ("US", "40")),
        ("xl", ("US", "42")),
        ("xxl", ("US", "44")),
        ("xxxl", ("US", "46")),
        ("4xl", ("US", "48")),
        # We don't map over 3xl
        ("5xl", ("US", "5xl")),
        ("5xl", ("US", "5xl")),
    ],
)
def test_extract_size_and_system(raw_size, result):
    assert extract_size_and_system(raw_size, None, "men", "clothing") == result


@pytest.mark.parametrize(
    "raw_size, result",
    [
        ("32", ("US", "32")),
    ],
)
def test_extract_size_and_use_provided_system(raw_size, result):
    assert extract_size_and_system(raw_size, "US", "men", "clothing") == result


@pytest.mark.parametrize(
    "raw_size, size_system, category, gender, result",
    [
        ("17.5", None, "ring", "women", "7.5"),
        ("55.1", None, "ring", "women", "7.5"),
        ("66.1", None, "ring", "women", "12"),
        # If raw_size value doesn't map to US then we return None
        ("6.5-7.0 mm", None, "ring", "women", None),
    ],
)
def test_curate_ring_size(raw_size, size_system, category, gender, result, product):
    size_id_mapper = {}
    curator = SizeCurator(product, size_id_mapper)
    assert curator.get_us_size_value(raw_size, size_system, category, gender) == result


@pytest.mark.parametrize(
    "raw_size, size_system, category, gender, result",
    [
        (
            "XL",
            None,
            "clothing",
            "MEN",
            {
                "id": "clx0p5jrf002omxms3vzy2g43",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "XL",
                "us_size_value": "42",
            },
        ),
        (
            "XL",
            None,
            "pants",
            "MEN",
            {
                "id": "clx0p5fdy001jmxmspts78pxs",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "XL",
                "us_size_value": "36",
            },
        ),
        (
            "2XL",
            None,
            "clothing",
            "MEN",
            {
                "id": "clx0p5j97002imxmsj4u2plcg",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "2XL",
                "us_size_value": "44",
            },
        ),
        (
            "2XL",
            None,
            "pants",
            "MEN",
            {
                "id": "clx0p5hib001ymxms2qi1br2j",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "2XL",
                "us_size_value": "38",
            },
        ),
        (
            "3XL",
            None,
            "clothing",
            "MEN",
            {
                "id": "clx0p5jc8002jmxmsru8av53r",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "3XL",
                "us_size_value": "46",
            },
        ),
        (
            "XL",
            None,
            "clothing",
            "WOMEN",
            {
                "id": "clx0p5k32002smxmsug71a24e",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "XL",
                "us_size_value": "10",
            },
        ),
        (
            "XL",
            None,
            "pants",
            "WOMEN",
            {
                "id": "cm02os4i10039514ek7r8y3oc",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "XL",
                "us_size_value": "16",
            },
        ),
        (
            "2XL",
            None,
            "clothing",
            "WOMEN",
            {
                "id": "clx0p5l8j0034mxmsaxkfcc00",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "2XL",
                "us_size_value": "12",
            },
        ),
        (
            "2XL",
            None,
            "pants",
            "WOMEN",
            {
                "id": "cm02os4i10039514ek7r8y3oc",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "2XL",
                "us_size_value": "16",
            },
        ),
        (
            "3XL",
            None,
            "clothing",
            "WOMEN",
            {
                "id": "clx0p5l3p0033mxmsqtus8suf",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "3XL",
                "us_size_value": "14",
            },
        ),
        (
            "One Size",
            None,
            "clothing",
            "WOMEN",
            {
                "id": "clzhj3qwo000cowyfxazjrrql",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "One Size",
                "us_size_value": "one size",
            },
        ),
        (
            "One Size",
            None,
            "clothing",
            "MEN",
            {
                "id": "clzhj3qu0000bowyfw0eqmbrc",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "One Size",
                "us_size_value": "one size",
            },
        ),
        (
            "S",
            None,
            "pants",
            "women",
            {
                "id": "cm02os4o8003b514e40zqnhu0",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "S",
                "us_size_value": "4",
            },
        ),
        (
            "small",
            None,
            "pants",
            "women",
            {
                "id": "cm02os4o8003b514e40zqnhu0",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "small",
                "us_size_value": "4",
            },
        ),
        (
            "41",
            None,
            "shoe",
            "men",
            {
                "id": "clx0p5cfw000nmxmsv1m5j6v9",
                "mapped_size_system": "",
                "raw_size_system": None,
                "raw_size_value": "41",
                "us_size_value": "8",
            },
        ),
    ],
)
def test_curate_size_get_size(raw_size, size_system, category, gender, result, product):
    product["raw_size"] = raw_size
    product["raw_size_system"] = size_system
    curator = SizeCurator(product, SAMPLE_SIZE_MAPPER)
    assert curator.get_size(category, gender) == result


@pytest.mark.parametrize(
    "raw_size, size_system, category, gender, result",
    [
        (
            "M",
            "EU",
            "clothing",
            "WOMEN",
            {
                "id": "clx0p5jx5002qmxms82rq1ve4",
                "mapped_size_system": "EU",
                "raw_size_value": "M",
                "us_size_value": "6",
                "raw_size_system": "EU",
            },
        ),
    ],
)
def test_curate_size_get_size_raw_size_system(
    raw_size, size_system, category, gender, result, product
):
    product["raw_size"] = raw_size
    product["raw_size_system"] = size_system
    curator = SizeCurator(product, SAMPLE_SIZE_MAPPER)
    assert curator.get_size(category, gender) == result
