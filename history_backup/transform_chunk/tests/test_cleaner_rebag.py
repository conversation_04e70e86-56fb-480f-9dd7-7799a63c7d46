import pytest
from cleaners.base_cleaner import get_category_broad_from_category_raw
from cleaners.rebag_cleaner import get_condition_from_description_str
from cleaners.rebag_cleaner import (
    RebagCleaner,
)
from models.product import CategoryBroad, Condition
from enums import Partners


# REBAG Data
def get_mock_item():
    return """
<entry>
<program_name>Rebag</program_name>
<program_url>https://rebag.com</program_url>
<catalog_name>Google Product Feed</catalog_name>
<last_updated>2024-01-30T15:42:19.796-08:00</last_updated>
<id>7419079917745</id>
<title><PERSON> Vuitton Men&#39;s Hybrid T-Shirt Tailoring Jacket Printed Wool</title>
<description>These are professional pictures of the actual item offered by <PERSON><PERSON>.

Condition: Great.  Light odor and wear throughout. 
Accessories: Hangers, Garment Bag 
Measurements: Length 31.5&#34;, Bust 37.0&#34;
Designer: <PERSON>
Model: Men&#39;s Hybrid T-Shirt Tailoring Collarless Jacket Printed Wool
Exterior Material: Wool, Cotton 
Exterior Color: Black, Print 
Interior Material: Silk 
Interior Color: Black 
Hardware Color: Silver, Black, <PERSON><PERSON> 
Brand Code: RM222 EY2 HNJ70E 
Item Number: 189914/362</description>
<link>https://www.anrdoezrs.net/click-100832946-14537862?url=https%3A%2F%2Fshop.rebag.com%2Fproducts%2Fapparel-louis-vuitton-mens-hybrid-t-shirt-tailoring-jacket-printed-wool-189914362</link>
<impression_url>https://www.lduhtrp.net/image-100832946-14537862</impression_url>
<image_link>https://cdn.shopify.com/s/files/1/0384/0161/products/189914-362_20Louis_20Vuitton_20Men_s_20Hybrid_20T-Shirt_20Tailoring_20Jacket_20Printed_20Wool_2D_0002.jpg?v=1674244681</image_link>
<additional_image_link>https://cdn.shopify.com/s/files/1/0384/0161/products/189914-362_20Louis_20Vuitton_20Men_s_20Hybrid_20T-Shirt_20Tailoring_20Jacket_20Printed_20Wool_2D_0003.jpg?v=1674244681</additional_image_link>
<additional_image_link>https://cdn.shopify.com/s/files/1/0384/0161/products/189914-362_20Louis_20Vuitton_20Men_s_20Hybrid_20T-Shirt_20Tailoring_20Jacket_20Printed_20Wool_2D_0004.jpg?v=1674244681</additional_image_link>
<additional_image_link>https://cdn.shopify.com/s/files/1/0384/0161/products/189914-362_20Louis_20Vuitton_20Men_s_20Hybrid_20T-Shirt_20Tailoring_20Jacket_20Printed_20Wool_2D_0005.jpg?v=1674244681</additional_image_link>
<additional_image_link>https://cdn.shopify.com/s/files/1/0384/0161/products/189914-362_20Louis_20Vuitton_20Men_s_20Hybrid_20T-Shirt_20Tailoring_20Jacket_20Printed_20Wool_2D_0006.jpg?v=1674244681</additional_image_link>
<availability>in stock</availability>
<price>2660.00 USD</price>
<sale_price>1100.00 USD</sale_price>
<google_product_category>5598</google_product_category>
<google_product_category_name>Apparel &amp; Accessories &gt; Clothing &gt; Outerwear &gt; Coats &amp; Jackets</google_product_category_name>
<product_type>Home &gt; Men &gt; Clothing &gt; Jackets</product_type>
<brand>Louis Vuitton</brand>
<mpn>189914/362</mpn>
<identifier_exists>yes</identifier_exists>
<condition>used</condition>
<age_group>adult</age_group>
<color>Black</color>
<gender>male</gender>
<material>Wool</material>
<size>L</size>
<promotion_id>non-new-arrivals</promotion_id>
<product_highlight>[]</product_highlight>
<transit_time_label>True</transit_time_label>
</entry>
"""


@pytest.fixture
def cleaner():
    return RebagCleaner(Partners.REBAG.value, get_mock_item(), "test_file.xml")


@pytest.mark.parametrize(
    "raw_category",
    [
        "Apparel & Accessories > Clothing > Outerwear",
        "Apparel & Accessories > Clothing > Outerwear > Coats & Jackets",
        "Apparel & Accessories > Clothing > Outerwear > Vests",
    ],
)
def test_get_category_broad_rebag_clothing_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.CLOTHING.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Apparel & Accessories > Jewelry > Rings",
    ],
)
def test_get_category_broad_rebag_rings_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.RINGS.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Luggage & Bags > Duffel Bags",
        "Home > Men > Handbags > Cross Body Bags",
        "Home > Women > Handbags > Cross Body Bags",
        "Luggage & Bags > Cosmetic & Toiletry Bags",
        "Home > Men > Handbags > Clutches",
        "Luggage & Bags > Briefcases",
        "Home > Men > Handbags > Shoulder Bags",
        "Home > Women > Handbags > Belt Bag",
        "Apparel & Accessories > Clothing Accessories > Belts",
        "Luggage & Bags > Backpacks",
        "Apparel & Accessories > Jewelry > Watches",
        "Home > Men > Handbags > Belt Bag",
        "Home > Women > Handbags > Clutches",
        "Luggage & Bags > Shopping Totes",
        "Luggage & Bags > Luggage Accessories > Travel Pouches",
        "Luggage & Bags > Messenger Bags",
        "Luggage & Bags > Fanny Packs",
        "Luggage & Bags > Suitcases",
        "Home > Women > Handbags > Wallets",
        "Home > Women > Handbags > Shoulder Bags",
        "Apparel & Accessories > Handbags, Wallets & Cases",
    ],
)
def test_get_category_broad_rebag_accessories_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.ACCESSORIES.value


def test_condition():
    desc = (
        "These are professional pictures of the actual item offered by"
        " Rebag.This item can only be shipped within the United States. \n Condition: Very good.  "
        "Odor in interior. Lifting of scales on exterior and handles, loss of shape and"
        " darkening on handles. Wear, scuffs, marks and indentations in interior, "
        "scratches on hardware. \n Accessories: No Accessories \n Measurements:"
        " Handle Drop 5' Height 10', Width 10'"
    )
    assert "Very good" == get_condition_from_description_str(desc)


def test_get_authenticity(cleaner):
    assert cleaner.is_authenticated() is True
