import pytest

from cleaners.base_cleaner import get_category_broad_from_category_raw
from cleaners.cj_cleaner import (
    CJCleaner,
    Availability,
)
from enums import Partners
from datetime import datetime

from models.product import CategoryBroad
from tests.utils import compare_dict_values
from decimal import Decimal


class MockElement:
    def __init__(self, text, availability="in stock"):
        self.text = text
        self.availability = availability

    def find(self, key):
        return MockElement(
            {
                "availability": self.availability,
                "title": "Test Product",
                "sale_price": "$99.99",
                "price": "$199.99",
                "link": "http://example.com/product",
                "image_link": "http://example.com/image.jpg",
                "additional_image_link": [
                    "http://example.com/image2.jpg",
                    "http://example.com/image3.jpg",
                ],
                "description": "Test Description",
                "material": "Test Material",
                "id": "12345",
                "color": "White",
                "product_type": "Women &gt; shoes &gt; espadrilles",
            }.get(key, None),
            self.availability,
        )


@pytest.fixture
def mock_item():
    return MockElement(None)


@pytest.fixture
def cj_cleaner(mock_item):
    return CJCleaner(
        Partners.VESTIAIRE_COLLECTIVE.value, mock_item, "test_1712808874_0.csv"
    )


@pytest.fixture
def cj_cleaner_in_stock():
    return CJCleaner(
        Partners.VESTIAIRE_COLLECTIVE.value,
        MockElement(None, availability=Availability.IN_STOCK.value),
        "test_1712808874_0.csv",
    )


@pytest.fixture
def cj_cleaner_out_of_stock():
    return CJCleaner(
        Partners.VESTIAIRE_COLLECTIVE.value,
        MockElement(None, availability=Availability.OUT_OF_STOCK.value),
        "test_1712808874_0.csv",
    )


def test_create_item_available(cj_cleaner_in_stock):
    expected = {
        "id": f"{Partners.VESTIAIRE_COLLECTIVE.value}-12345",
        "secondhand_retailer": Partners.VESTIAIRE_COLLECTIVE.value,
        "name": "Test Product",
        "product_url": "http://example.com/product",
        "img_url": "http://example.com/image.jpg",
        "additional_img_urls": [
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg",
        ],
        "description": "Test Description",
        "raw_id": "12345",
        "material": "Test Material",
        "price_usd": Decimal(99.99),
        "created_at": datetime.now(),
        "last_seen_at": datetime.now(),
        "updated_at": datetime.now(),
        "color": "White",
        "is_available": True,
        "is_authenticated": False,
        "is_returnable": False,
        "category": "Women &gt; shoes &gt; espadrilles",
        "category_broad": "shoe",
        "is_approved_seller": True,
    }
    actual = cj_cleaner_in_stock.create_item()
    assert compare_dict_values(expected=expected, actual=actual)


def test_create_item_not_available(cj_cleaner_out_of_stock):
    assert cj_cleaner_out_of_stock.create_item() is not None


def test_is_available(cj_cleaner):
    assert cj_cleaner.is_available()


def test_get_authenticity(cj_cleaner):
    assert cj_cleaner.is_authenticated() is False


def test_get_name(cj_cleaner):
    assert cj_cleaner.get_name() == "Test Product"


def test_get_price(cj_cleaner):
    assert cj_cleaner.get_price() == 99.99


def test_get_product_url(cj_cleaner):
    assert cj_cleaner.get_product_url() == "http://example.com/product"


def test_get_img_url(cj_cleaner):
    assert cj_cleaner.get_img_url() == "http://example.com/image.jpg"


def test_get_additional_img_urls(cj_cleaner):
    assert cj_cleaner.get_additional_img_urls() == [
        "http://example.com/image2.jpg",
        "http://example.com/image3.jpg",
    ]


def test_get_description(cj_cleaner):
    assert cj_cleaner.get_description() == "Test Description"


def test_get_material(cj_cleaner):
    assert cj_cleaner.get_material() == "Test Material"


def test_get_raw_id(cj_cleaner):
    assert cj_cleaner.get_raw_id() == "12345"


#  Vest Collective Categories
@pytest.mark.parametrize(
    "raw_category",
    [
        "Women > accessories > wallets",
        "Men > bags > belt bags",
        "Women > bags > clutch bags",
        "Men > accessories > belts",
        "Women > bags > travel bags",
        "Men > accessories > sunglasses",
        "Men > accessories > watches",
        "Women > bags > belt bags",
        "Men > accessories > hats & pull on hats",
        "Women > accessories > hats",
        "Women > bags > handbags",
        "Men > bags > bags",
        "Women > accessories > belts",
        "Women > accessories > sunglasses",
        "Women > accessories > watches",
    ],
)
def test_get_category_broad_cj_accessories_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.ACCESSORIES.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Apparel & Accessories > Clothing > Pants",
        "Clothing, Shoes & Accessories|Specialty|Dancewear|Kids' Dancewear|Pants & Shorts",
        "Clothing, Shoes & Accessories|Men|Men's Clothing|Pants",
        "Men > clothing > jeans",
    ],
)
def test_get_category_broad_cj_pants_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.PANTS.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Women > clothing > tops",
        "Men > clothing > knitwear & sweatshirts",
        "Men > clothing > coats",
        "Women > clothing > knitwear",
        "Women > clothing > coats",
        "Men > clothing > polo shirts",
        "Women > clothing > shorts",
        "Women > clothing > dresses",
        "Women > clothing > trench coats",
        "Women > clothing > skirts",
        "Men > clothing > jackets",
        "Women > clothing > leather jackets",
        "Men > clothing > shirts",
        "Women > clothing > jackets",
        "Men > clothing > t-shirts",
    ],
)
def test_get_category_broad_cj_clothing_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.CLOTHING.value


@pytest.mark.parametrize(
    "raw_category",
    [
        "Women > jewellery > rings",
    ],
)
def test_get_category_broad_cj_jewelry_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.RINGS.value


# Rebag
@pytest.mark.parametrize(
    "raw_category",
    [
        "Home > Women > Shoes",
        "Home > Men > Shoes",
    ],
)
def test_get_category_broad_cj_shoes_from_category_raw(raw_category):
    category_broad = get_category_broad_from_category_raw(raw_category)
    assert category_broad == CategoryBroad.SHOES.value
