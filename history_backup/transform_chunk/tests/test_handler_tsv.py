import json
import os
from unittest.mock import Mock

import pytest

from cleaners.ebay_cleaner import (
    EbayCleaner,
    ebay_product_url_affiliate_like_substitution,
)
from field_id_mappers.field_id_mapper import FieldIdMapper
from file_handlers.tsv_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
)
from enums import Partners
from tests.mock_data.sample_size_mapper import SAMPLE_SIZE_MAPPER


@pytest.fixture
def tsv_file_path():
    return os.getcwd() + "/tests/mock_data/ebay_example.tsv"


@pytest.fixture
def tsv_file_path_ebay_localized_aspect_size():
    return os.getcwd() + "/tests/mock_data/ebay_example_size_localized_aspect.tsv"


@pytest.fixture
def jsonl_file_path():
    return os.getcwd() + "test_output.jsonl"


def test_parse_and_process(tsv_file_path, jsonl_file_path):
    field_id_mappers = FieldIdMapper({}, {}, {})
    handler = TSVHandler(
        Partners.EBAY.value, EbayCleaner, "ebay_1712808874_0.tsv", field_id_mappers
    )

    # Call the parse_and_process method
    handler.parse_and_process(tsv_file_path, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 1
        first_item = json.loads(lines[0].strip())
        assert first_item["name"] == "Merona Women Black Blazer S"
        assert first_item["description"].startswith(
            "The Largest Online Consignment &amp; Thrift Store"
        )
        assert first_item["product_url"] == (
            ebay_product_url_affiliate_like_substitution(
                "https://www.ebay.com/itm/126461563210?"
                "var=0&mkevt=1&mkcid=1&mkrid=711-53200-"
                "19255-0&campid=CAMPAIGNID&customid="
                "CUSTOMID&toolid=20006"
            )
        )
        assert (
            first_item["img_url"]
            == "https://i.ebayimg.com/images/g/iScAAOSwW51mM4D6/s-l1600.jpg"
        )
        assert first_item["price_usd"] == 19.74

    # Clean up the test JSONL file after the test
    os.remove(jsonl_file_path)


def test_parse_and_process(tsv_file_path_ebay_localized_aspect_size, jsonl_file_path):
    field_id_mappers = FieldIdMapper(SAMPLE_SIZE_MAPPER, {}, [], {})
    handler = TSVHandler(
        Partners.EBAY.value, EbayCleaner, "ebay_1712808874_0.tsv", field_id_mappers
    )

    # Call the parse_and_process method
    handler.parse_and_process(tsv_file_path_ebay_localized_aspect_size, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 1
        first_item = json.loads(lines[0].strip())
        assert (
            first_item["name"]
            == "vtg 80s RENO NEVADA SILVER STATE LICENSE TO GAMBLE CASINO RESORT 1987 t-shirt S"
        )
        assert first_item["gender"] == "MEN"
        assert first_item["category_broad"] == "clothing"
        assert first_item["size"] == {
            "id": "clx0p5j6f002hmxmsq54hn42w",
            "mapped_size_system": "",
            "raw_size_system": None,
            "raw_size_value": "S",
            "us_size_value": "36",
        }

    # Clean up the test JSONL file after the test
    os.remove(jsonl_file_path)
