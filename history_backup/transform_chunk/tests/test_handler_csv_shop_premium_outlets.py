import json
import os

import pytest

from cleaners.shop_premium_outlets_cleaner import ShopPremiumOutletsCleaner
from field_id_mappers.field_id_mapper import FieldIdMapper
from file_handlers.csv_handler import (
    CSVHandler,
)
from enums import Partners
from tests.mock_data.sample_attribute_id_mapper import SAMPLE_ATTRIBUTES_MAPPING
from tests.mock_data.sample_size_mapper import SAMPLE_SIZE_MAPPER


@pytest.fixture
def csv_file_path_sop():
    return os.getcwd() + "/tests/mock_data/sop_example.csv"


@pytest.fixture
def jsonl_file_path():
    return os.getcwd() + "test_output.jsonl"


def test_parse_and_process(csv_file_path_sop, jsonl_file_path):
    field_id_mappers = FieldIdMapper(
        SAMPLE_SIZE_MAPPER, {}, [], SAMPLE_ATTRIBUTES_MAPPING
    )
    handler = CSVHandler(
        Partners.SHOP_PREMIUM_OUTLETS.value,
        ShopPremiumOutletsCleaner,
        "test_1712808874_0.csv",
        field_id_mappers,
    )

    # Call the parse_and_process method
    handler.parse_and_process(csv_file_path_sop, jsonl_file_path)

    # Read the generated JSONL file and verify its contents
    with open(jsonl_file_path, "r") as file:
        lines = file.readlines()
        assert len(lines) == 2
        first_item = json.loads(lines[0].strip())
        assert first_item["name"] == "Elina Chambray Belted Dress"
        assert first_item["description"].startswith("Elina Chambray Belted Dress")
        assert first_item["product_url"].startswith(
            "https://click.linksynergy.com/link?id=jbr6rn*Xy2I&offe"
        )
        assert (
            first_item["img_url"]
            == "https://cdn.shopify.com/s/files/1/0291/4536/6588/files/e0f18af50aeb4dda8486383ac8d9b763_fbbb861e-e988-4bf6-b1ee-8eb107c79387.jpg?v=1724720482"
        )
        assert first_item["price_usd"] == 69.99
        assert first_item["meta"] == {"filepath": "test_1712808874_0.csv"}
        assert first_item["gender"] == "WOMEN"
        assert first_item["size"] == {
            "id": "clx0p5k32002smxmsug71a24e",
            "mapped_size_system": "",
            "raw_size_system": None,
            "raw_size_value": "XLarge",
            "us_size_value": "10",
        }
        assert first_item["attribute_list"] == [
            {
                "attribute_display_name": "dresses",
                "attribute_id": "clzd3pk1700c0wfrnkmuzsvpi",
            },
            {
                "attribute_display_name": "women",
                "attribute_id": "clzd3ps3x00eiwfrngj0361n6",
            },
        ]

    # Clean up the test JSONL file after the test
    os.remove(jsonl_file_path)
