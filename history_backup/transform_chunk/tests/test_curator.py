import pytest
from curators.curator import Curator
from field_id_mappers.field_id_mapper import FieldIdMapper
from models.product import CleanProductSchema
from tests.utils import get_minimal_mock_clean_product


@pytest.fixture
def product():
    product = get_minimal_mock_clean_product()
    return CleanProductSchema().load(product)


@pytest.mark.parametrize(
    "product_name, has_profanity",
    [
        ("This is a shit brand", True),
        ("This is a sexy brand", True),
        ("This is a dildo brand", True),
        ("This is a good brand", False),
        ("This is a brand", False),
    ],
)
def test_curator_should_filter_profanity_in_name(product_name, has_profanity, product):
    product["name"] = product_name
    curator = Curator(product, FieldIdMapper({}, {}, [], {}))
    assert curator.has_profanity() == has_profanity


@pytest.mark.parametrize(
    "product_description, has_profanity",
    [
        ("This is a sexual brand", True),
        ("This is a horny brand", True),
        ("This is a bondage brand", True),
        ("This is a good brand", False),
        ("This is a brand", False),
    ],
)
def test_curator_should_filter_profanity_in_description(
    product_description, has_profanity, product
):
    product["description"] = product_description
    curator = Curator(product, FieldIdMapper({}, {}, [], {}))
    assert curator.has_profanity() == has_profanity


@pytest.mark.parametrize(
    "price, should_filter",
    [
        (44, False),
        (0, True),
        (999999.99, False),
        (10000000.32, True),
        (-144, True),
    ],
)
def test_curator_should_filter_by_price(price, should_filter, product):
    product["price_usd"] = price
    curator = Curator(product, FieldIdMapper({}, {}, [], {}))
    assert curator.is_price_outside_valid_range() == should_filter
