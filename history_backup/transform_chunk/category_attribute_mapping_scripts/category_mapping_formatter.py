import csv
import json


def parse_single_csv_to_dict(primary_csv_path):
    retailer_dict = {}

    # Parse the primary CSV file
    with open(primary_csv_path, mode="r", newline="", encoding="utf-8") as primary_file:
        primary_reader = csv.reader(primary_file)
        next(primary_reader)  # Skip the header row

        for row in primary_reader:
            raw_category = row[0]
            retailer = row[1]
            raw_clean_category = row[2]

            if retailer not in retailer_dict:
                retailer_dict[retailer] = {}
            if "," in raw_clean_category:
                attribute_array = [
                    c.strip().lower() for c in raw_clean_category.split(",")
                ]
            else:
                attribute_array = [raw_clean_category.strip().lower()]

            clean_attribute_array = []
            for a in attribute_array:
                if "womens" in a:
                    b = a.replace("womens", "")
                    clean_attribute_array.append(b.strip())
                    clean_attribute_array.append("women")
                elif "women's" in a:
                    b = a.replace("women's", "")
                    clean_attribute_array.append(b.strip())
                    clean_attribute_array.append("women")
                elif "women" in a:
                    b = a.replace("women", "")
                    clean_attribute_array.append(b.strip())
                    clean_attribute_array.append("women")
                elif "mens" in a:
                    b = a.replace("mens", "")
                    clean_attribute_array.append(b.strip())
                    clean_attribute_array.append("men")
                elif "men's" in a:
                    b = a.replace("men's", "")
                    clean_attribute_array.append(b.strip())
                    clean_attribute_array.append("men")
                elif "men" in a:
                    b = a.replace("men", "")
                    clean_attribute_array.append(b.strip())
                    clean_attribute_array.append("men")
                elif "kids" in a:
                    b = a.replace("kids", "")
                    clean_attribute_array.append(b.strip())
                    clean_attribute_array.append("kids")
                elif "kid's" in a:
                    b = a.replace("kid's", "")
                    clean_attribute_array.append(b.strip())
                    clean_attribute_array.append("kids")
                else:
                    clean_attribute_array.append(a)

            retailer_dict[retailer][raw_category] = [
                item.replace("&", "and") for item in clean_attribute_array if item != ""
            ]

            if "vintage" in raw_category.lower():
                retailer_dict[retailer][raw_category].append("vintage")
    return retailer_dict


def parse_single_csv_get_unique_attributes(formatted_json):
    clean_category_set = set()
    clean_category_set.add("vintage")

    # Parse the primary CSV file
    for partner, category_mappings in formatted_json.items():
        for raw_category, clean_attribute_list in category_mappings.items():
            for attribute in clean_attribute_list:
                clean_category_set.add(attribute.strip().lower())

    return list(clean_category_set)


def write_dict_to_json(output_dict, output_file_path):
    with open(output_file_path, mode="w", encoding="utf-8") as json_file:
        json.dump(output_dict, json_file, indent=4)


# Usage example
main_category_mapping_path = "category_mappings.csv"
blacklist_file_path = "category_mappings_blacklist.csv"

single_result = parse_single_csv_to_dict(main_category_mapping_path)
blacklist_result = parse_single_csv_to_dict(blacklist_file_path)

attributes = parse_single_csv_get_unique_attributes(single_result)

write_dict_to_json(single_result, "../local/single_category_mappings.json")
write_dict_to_json(sorted(attributes), "../local/unique_attributes.json")

# write_dict_to_json(result, "category_mappings.json")


print(attributes)
