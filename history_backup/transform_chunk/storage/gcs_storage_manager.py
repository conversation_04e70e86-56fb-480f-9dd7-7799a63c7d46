from google.cloud import storage
import os
import logging

from util.gcs_task_manager import GCSTaskManager


class GCSStorageManager:
    def __init__(self):
        self.bucket_name = os.getenv("UPLOAD_BUCKET_NAME")
        self.storage_client = storage.Client()
        self.bucket = self.storage_client.bucket(self.bucket_name)

    def upload_jsonl_to_gcs(
        self, source_file_path: str, file_name: str, partner_name: str, metadata=None
    ):
        dest_file_path = f"{partner_name}/{file_name}.jsonl"
        blob = self.bucket.blob(dest_file_path)
        is_ebay_bootstrap = False
        if metadata is not None:
            blob.metadata = metadata
            is_ebay_bootstrap = bool(metadata.get("is_ebay_bootstrap", False))
        blob.upload_from_filename(source_file_path, content_type="application/jsonl")
        logging.info(f"Uploaded {file_name} to GCS")
        GCSTaskManager().create_task(dest_file_path, partner_name, is_ebay_bootstrap)
