import csv
from file_handlers.base_handler import FileHandler
import simplejson as json

from util.stat_logger import StatLogger


class CSVHandler(FileHandler):
    def parse_and_process(self, csv_file_path, jsonl_file_path):
        with open(csv_file_path, "r") as csv_file:
            with open(jsonl_file_path, "w") as jsonl_file:
                # Reading with <PERSON>ct<PERSON><PERSON><PERSON> as we have headers
                csv_reader = csv.DictReader(csv_file)
                for row in csv_reader:
                    processed_item = self._process_entry(row)
                    if processed_item is not None:
                        StatLogger.add_item_count()
                        jsonl_file.write(json.dumps(processed_item, default=str) + "\n")
