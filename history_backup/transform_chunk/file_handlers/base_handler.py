from abc import ABC, abstractmethod
from cleaners.base_cleaner import BaseCleaner
from curators.curator import Curator
from field_id_mappers.field_id_mapper import FieldIdMapper


class FileHandler(ABC):
    def __init__(
        self,
        partner_name: str,
        cleaner: BaseCleaner,
        file_name: str,
        field_id_mapper: FieldIdMapper,
    ):
        self.partner_name = partner_name
        self.cleaner = cleaner
        self.file_name = file_name
        self.field_id_mapper = field_id_mapper

    def __entry_is_empty(self, entry):
        """
        Check if the given entry (lxml.etree._Element) is None or equivalent.
        Returns:
            True if the entry is empty, False otherwise.
        """
        if entry is None:
            return True
        if (
            len(entry) == 0
            and not (entry.text and entry.text.strip())
            and not entry.attrib
        ):
            return True
        return False

    def _process_entry(self, entry):
        if self.__entry_is_empty(entry):
            return None

        item_cleaner = self.cleaner(self.partner_name, entry, self.file_name)
        cleaned_item = item_cleaner.create_item()
        if cleaned_item is None:
            return None

        item_curator = Curator(cleaned_item, self.field_id_mapper, self.file_name)
        curated_item = item_curator.curate()
        return curated_item

    @abstractmethod
    def parse_and_process(self, input_filepath: str, output_filepath: str):
        pass
