import csv
import <PERSON><PERSON><PERSON> as json

from file_handlers.base_handler import <PERSON>Handler
from util.stat_logger import StatLogger


class TSVHandler(FileHandler):
    def parse_and_process(self, tsv_file_path, jsonl_file_path):
        with open(tsv_file_path, "r") as csv_file:
            with open(jsonl_file_path, "w") as jsonl_file:
                # Reading with <PERSON>ct<PERSON><PERSON><PERSON> as we have headers
                tsv_reader = csv.DictReader(csv_file, delimiter="\t")
                for row in tsv_reader:
                    processed_item = self._process_entry(row)
                    if processed_item is not None:
                        StatLogger.add_item_count()
                        jsonl_file.write(json.dumps(processed_item, default=str) + "\n")
