from lxml import etree
from file_handlers.base_handler import <PERSON><PERSON><PERSON><PERSON>
from enums import Partners
import simplejson as json

from util.stat_logger import StatLogger

XML_TAG_MAPPINGS = {
    Partners.THE_LUXURY_CLOSET.value: "entry",
    Partners.FARFETCH.value: "item",
    Partners.VESTIAIRE_COLLECTIVE.value: "entry",
}


class XMLHandler(FileHandler):
    def parse_and_process(self, xml_filepath, jsonl_filepath):
        # Parse the XML file
        with open(xml_filepath, "rb") as xml_file:
            # Use lxml etree.iterparse for efficient streaming parsing
            product_tag = XML_TAG_MAPPINGS.get(self.partner_name, "entry")
            context = etree.iterparse(xml_file, events=("end",), tag=product_tag)

            # Open the JSONL file for writing
            with open(jsonl_filepath, "w") as jsonl_file:
                for _, entry in context:
                    # Process the entry
                    processed_item = self._process_entry(entry)

                    # Write the processed item to the JSONL file
                    if processed_item is not None:
                        StatLogger.add_item_count()
                        jsonl_file.write(json.dumps(processed_item, default=str) + "\n")

                    # Clear the entry to free memory
                    entry.clear()
