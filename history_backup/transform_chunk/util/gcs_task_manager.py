import json
import logging
import os

from google.cloud import tasks_v2


class GCSTaskManager:
    def __init__(self):
        self.storage_client = tasks_v2.CloudTasksClient()
        self.project_id = os.getenv("PROJECT_ID")
        self.location = os.getenv("LOCATION")
        self.queue = os.getenv("DOWNSTREAM_TASK_QUEUE")
        self.long_running_queue = os.getenv("DOWNSTREAM_LONG_RUNNING_TASK_QUEUE")
        self.task_url = os.getenv("DOWNSTREAM_TASK_URI")
        self.service_account_email = os.getenv("SERVICE_ACCOUNT_EMAIL")
        self.upload_bucket_name = os.getenv("UPLOAD_BUCKET_NAME")

    def create_task(self, file_path: str, partner_name: str, is_ebay_bootstrap: bool):
        try:
            payload = {
                "message": {
                    "attributes": {
                        "objectId": file_path,  # "rebag/rebag_1718919066_0.xml",
                        "bucketId": self.upload_bucket_name,
                    }
                }
            }

            client = tasks_v2.CloudTasksClient()
            queue = (
                self.long_running_queue
                if (partner_name == "poshmark" or is_ebay_bootstrap)
                else self.queue
            )
            parent = client.queue_path(self.project_id, self.location, queue)

            task = tasks_v2.Task(
                http_request={
                    "http_method": "POST",
                    "url": self.task_url,
                    "headers": {"Content-Type": "application/json"},
                    "oidc_token": {
                        "service_account_email": self.service_account_email,
                    },
                    "body": json.dumps(payload).encode(),
                },
                dispatch_deadline={"seconds": 1800},
            )

            resp = client.create_task(
                tasks_v2.CreateTaskRequest(
                    {
                        "parent": parent,
                        "task": task,
                    }
                )
            )
            logging.info(f"Task sent to {self.queue}: {resp}")
        except Exception as e:
            logging.error(f"Failed to create task: {e}")
            raise e
