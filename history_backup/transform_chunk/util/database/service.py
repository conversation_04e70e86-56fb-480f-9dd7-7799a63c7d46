from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from util.database.models import PartnerProductSync, PartnerProductFileSync, Status


def create_partner_product_sync(session: Session, partner_name: str):
    try:
        status = Status.PENDING.value
        new_partner_product_sync = PartnerProductSync(
            partner=partner_name, status=status
        )
        session.add(new_partner_product_sync)
        session.commit()
        return new_partner_product_sync.id
    except SQLAlchemyError as e:
        print(f"Error creating partner product sync: {e}")
        session.rollback()


def create_partner_product_file_sync(
    session: Session, partner_product_sync_id, file_name, partner_name: str
):
    try:
        new_partner_product_file_sync = PartnerProductFileSync(
            partner_product_sync_id=partner_product_sync_id,
            file_name=file_name,
            partner=partner_name,
            status=Status.PENDING.value,
        )
        session.add(new_partner_product_file_sync)
        session.commit()
        return new_partner_product_file_sync.id
    except SQLAlchemyError as e:
        session.rollback()
        print(f"Error creating partner product file sync: {e}")


def update_partner_product_syncs_status(session, partner_product_sync_id, status):
    try:
        # Fetch the record
        file_sync = (
            session.query(PartnerProductSync)
            .filter(PartnerProductSync.id == partner_product_sync_id)
            .first()
        )

        if file_sync:
            file_sync.status = status
            session.commit()
            print(
                f"Updated PartnerProductSync ID {partner_product_sync_id} to status {status}"
            )
        else:
            print(f"PartnerProductSync ID {partner_product_sync_id} not found.")

    except SQLAlchemyError as e:
        # Rollback the transaction in case of an error
        session.rollback()
        print(f"Error updating status: {e}")


def get_partner_product_file_syncs_status(session, partner_product_file_sync_id):
    try:
        # Fetch the record
        file_sync = (
            session.query(PartnerProductFileSync)
            .filter(PartnerProductFileSync.id == partner_product_file_sync_id)
            .first()
        )
        if file_sync:
            return file_sync.status
    except SQLAlchemyError as e:
        print(f"Error updating status: {e}")
        session.rollback()


def update_partner_product_file_syncs_status(
    session, partner_product_file_sync_id, status
):
    try:
        # Fetch the record
        file_sync = (
            session.query(PartnerProductFileSync)
            .filter(PartnerProductFileSync.id == partner_product_file_sync_id)
            .first()
        )

        if file_sync:
            # Update the status
            file_sync.status = status
            # Commit the transaction
            session.commit()
            print(
                f"Updated PartnerProductFileSync ID {partner_product_file_sync_id} to status {status}"
            )
        else:
            print(
                f"PartnerProductFileSync ID {partner_product_file_sync_id} not found."
            )

    except SQLAlchemyError as e:
        # Rollback the transaction in case of an error
        session.rollback()
        print(f"Error updating status: {e}")
