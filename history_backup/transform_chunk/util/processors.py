import re


def extract_numeric_price(price_string):
    if not price_string:
        return None
    # Remove non-digit characters and convert to float
    numeric_part = re.sub(r"[^\d.]", "", price_string)
    try:
        return float(numeric_part)
    except ValueError:
        return None


def drop_null_values_from_dict(dct: dict) -> dict:
    output = {}
    for key, value in dct.items():
        if value is not None:
            output[key] = value
    return output
