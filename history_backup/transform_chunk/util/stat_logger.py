import json
import logging

NON_UNIFIED_BRAND_COUNT_KEY = "brand"
NON_UNIFIED_SIZE_COUNT_KEY = "size"
NON_UNIFIED_COLOR_COUNT_KEY = "color"
NON_UNIFIED_GENDER_COUNT_KEY = "gender"


class StatLogger:
    item_count = 0
    non_unified_count_dict = {}
    file_name = None
    partner_name = None

    def __init__(self, partner_name, file_name):
        StatLogger.item_count = 0
        StatLogger.file_name = file_name
        StatLogger.partner_name = partner_name
        StatLogger.non_unified_count_dict = {
            NON_UNIFIED_BRAND_COUNT_KEY: 0,
            NON_UNIFIED_SIZE_COUNT_KEY: 0,
            NON_UNIFIED_COLOR_COUNT_KEY: 0,
            NON_UNIFIED_GENDER_COUNT_KEY: 0,
        }

    @classmethod
    def add_non_unified_count(self, key):
        if key in StatLogger.non_unified_count_dict:
            StatLogger.non_unified_count_dict[key] += 1
        else:
            StatLogger.non_unified_count_dict[key] = 1

    @classmethod
    def add_item_count(cls):
        cls.item_count += 1

    @classmethod
    def log_stats(cls):
        logging.info(
            json.dumps(
                {
                    "type": "metric",
                    "non_unified_counts": StatLogger.non_unified_count_dict,
                    "item_count": cls.item_count,
                    "file_name": cls.file_name,
                    "partner_name": cls.partner_name,
                }
            )
        )
