import logging
import os
import sys

import functions_framework
import google.cloud.logging
from google.cloud import storage
from sqlalchemy.orm import sessionmaker

from field_id_mappers.BrandService import BrandService
from field_id_mappers.attribute_id_mapper import AttributeIdMapper
from field_id_mappers.field_id_mapper import FieldIdMapper
from field_id_mappers.size_id_mapper import SizeIdMapper
from mappings import FILE_HANDLER_MAPPINGS, CLEANER_MAPPINGS
from storage.gcs_storage_manager import GCSStorageManager
from util.database.connector import connect_with_connector
from util.database.models import Status
from util.database.service import (
    update_partner_product_file_syncs_status,
    get_partner_product_file_syncs_status,
)
from util.stat_logger import StatLogger


@functions_framework.http
def transform_partner_chunk(request):
    """HTTP Cloud Function to fetch data for a given partner."""
    setup_logging()
    request_json = request.get_json()
    logging.info("Received request to transform partner chunk")
    if (
        request_json
        and "message" in request_json
        and "attributes" in request_json["message"]
    ):
        message = request_json["message"]["attributes"]
        logging.info(f"Received message: {message}")
    else:
        logging.error(f"Missing message or attributes in the request {request_json}")
        logging.error(f"Raw request {request}")
        return "Bad Request", 400

    if message and "objectId" in message and "bucketId" in message:
        file_name = message["objectId"]
        bucket_name = message["bucketId"]
        logging.info(f"Received file: {file_name} from bucket: {bucket_name}")
    else:
        logging.error("Missing file name or bucket name in the request")
        return "Bad Request", 400

    print(f"Processing file: {file_name} from bucket: {bucket_name}")

    # Extract the folder path from the file name
    folder_path = "/".join(file_name.split("/")[:-1])
    if folder_path:
        logging.info(f"Folder path: {folder_path}")

    # Placeholder for additional processing logic
    return process_file(file_name, bucket_name, folder_path)


def setup_logging():
    if os.getenv("ENVIRONMENT") != "local":
        logging.getLogger().setLevel(logging.INFO)
        client = google.cloud.logging.Client()
        client.setup_logging()
    else:
        logging.basicConfig(stream=sys.stdout, level=logging.INFO)


def process_file(file_path, bucket_name, partner_name):
    partner_product_file_sync_id = None
    engine = connect_with_connector()
    Session = sessionmaker(bind=engine)
    try:
        StatLogger(partner_name=partner_name, file_name=file_path)
        file_name = os.path.basename(file_path)
        logging.info(
            f"Processing file: {file_name} from bucket: {bucket_name} for partner: {partner_name}"
        )
        # Initialize a storage client
        storage_client = storage.Client()

        # Get the bucket and blob (file) objects
        bucket = storage_client.bucket(bucket_name)
        logging.info(f"Bucket: {bucket.name}")
        blob = bucket.blob(file_path)
        blob.reload()
        logging.info(f"Got blob at file path: {file_path}")

        partner_product_file_sync_id = blob.metadata.get("partner_product_file_sync_id")
        is_ebay_bootstrap = bool(blob.metadata.get("is_ebay_bootstrap", False))
        with Session() as session:
            status = get_partner_product_file_syncs_status(
                session, partner_product_file_sync_id
            )
            if status in [
                Status.TRANSFORMING.value,
                Status.TRANSFORMED.value,
                Status.INGESTING.value,
                Status.INGESTED.value,
            ]:
                logging.warning(
                    f"{file_path} with id {partner_product_file_sync_id} is already in {status} "
                    f"state. Will skip processing."
                )
                return (
                    f"Transform and chunk processed successfully for {partner_name}!",
                    200,
                )
            update_partner_product_file_syncs_status(
                session, partner_product_file_sync_id, Status.TRANSFORMING.value
            )

        # Download the file to a temporary location
        temp_file_path = f"/tmp/{file_path}"
        os.makedirs(os.path.dirname(temp_file_path), exist_ok=True)
        blob.download_to_filename(temp_file_path)
        logging.info(f"Downloaded file to: {temp_file_path}")

        # Define the output JSONL file path
        jsonl_file_path = f"{temp_file_path}.jsonl"

        # Create an instance of the handler based on the partner name and process it
        handler = FILE_HANDLER_MAPPINGS.get(partner_name)
        if not handler:
            logging.error(f"No handler found for partner: {partner_name}")
            return f"No handler found for partner: {partner_name}", 400

        cleaner = CLEANER_MAPPINGS.get(partner_name)
        if not cleaner:
            logging.error(f"No cleaner found for partner: {partner_name}")
            return f"No cleaner found for partner: {partner_name}", 400

        brand_service = BrandService()
        field_id_mapper = FieldIdMapper(
            SizeIdMapper().size_id_mapper,
            brand_service.get_brand_id_mapper(),
            brand_service.get_brand_list(),
            AttributeIdMapper().attribute_id_mapper,
        )
        handler_instance = handler(
            partner_name=partner_name,
            cleaner=cleaner,
            file_name=file_path,
            field_id_mapper=field_id_mapper,
        )
        handler_instance.parse_and_process(temp_file_path, jsonl_file_path)
        StatLogger.log_stats()
        GCSStorageManager().upload_jsonl_to_gcs(
            jsonl_file_path,
            file_name,
            partner_name,
            {
                "partner_product_file_sync_id": partner_product_file_sync_id,
                "is_ebay_bootstrap": is_ebay_bootstrap,
            },
        )
        os.remove(temp_file_path)
        os.remove(jsonl_file_path)
        logging.info(f"Transform and chunk processed successfully for {partner_name}!")
        with Session() as session:
            update_partner_product_file_syncs_status(
                session,
                partner_product_file_sync_id,
                Status.TRANSFORMED.value,
            )
        return (
            f"Transform and chunk processed successfully for {partner_name}!",
            200,
        )
    except Exception as e:
        logging.exception(e)
        if partner_product_file_sync_id is not None:
            with Session() as session:
                update_partner_product_file_syncs_status(
                    session,
                    partner_product_file_sync_id,
                    Status.FAILED_TRANSFORM.value,
                )
        return f"Error processing data for {partner_name}: {e}", 500
