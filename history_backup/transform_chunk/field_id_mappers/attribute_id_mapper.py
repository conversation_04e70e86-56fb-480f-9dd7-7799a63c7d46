import os

from python_graphql_client import GraphqlClient
import google.auth.transport.requests
import google.oauth2.id_token

from assets.category_mappings import RAW_CATEGORY_TO_ATTRIBUTE_MAPPING


def get_query():
    return """
            query Attributes($attribute: AttributeQueryInput) {
              getAttributes(attribute: $attribute) {
                id
                displayName
              }
            }
            """


class AttributeIdMapper:

    def __init__(self):
        self.attribute_id_mapper = self.get_mapper()

    def get_mapper(self):
        token = os.getenv("X_PHIA_TOKEN")
        """
            Note, the below auth will not work locally unless you manually
            generate an identity token using 'gcloud auth print-identity-token'

            TODO - Fix this to work locally
        """
        auth_req = google.auth.transport.requests.Request()
        id_token = google.oauth2.id_token.fetch_id_token(
            auth_req, "https://phia-api-data-pipeline-prod-4wdelowz4a-uc.a.run.app"
        )
        client = GraphqlClient(
            endpoint="https://phia-api-data-pipeline-prod-4wdelowz4a-uc.a.run.app/internal/graphql",
            headers={"x-phia-token": token, "Authorization": f"Bearer {id_token}"},
        )
        resp = client.execute(query=get_query(), variables={"attribute": {}})
        simple_category_mapping = {}
        for attribute in resp["data"]["getAttributes"]:
            simple_category_mapping[attribute["displayName"]] = attribute["id"]

        brand_raw_category_to_clean_attribute_id = {}
        for partner, categories in RAW_CATEGORY_TO_ATTRIBUTE_MAPPING.items():
            brand_raw_category_to_clean_attribute_id[partner] = {}
            for raw_category, clean_category_list in categories.items():
                attribute_id_list = []
                for clean_category in clean_category_list:
                    if clean_category in simple_category_mapping:
                        attribute_id_list.append(
                            {
                                "attribute_id": simple_category_mapping[clean_category],
                                "attribute_display_name": clean_category,
                            }
                        )
                    brand_raw_category_to_clean_attribute_id[partner][
                        raw_category
                    ] = attribute_id_list

        return brand_raw_category_to_clean_attribute_id
