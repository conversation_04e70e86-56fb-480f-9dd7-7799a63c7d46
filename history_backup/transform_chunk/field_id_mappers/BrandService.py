import os

import requests
from python_graphql_client import GraphqlClient
import google.auth.transport.requests
import google.oauth2.id_token


def get_query():
    return """
        query Brands {
          brands {
            id
            displayName
            deletedAt
          }
        }
            """


class BrandService:

    def __init__(self):
        token = os.getenv("X_PHIA_TOKEN")
        """
            Note, the below auth will not work locally unless you manually
            generate an identity token using 'gcloud auth print-identity-token'

            TODO - Fix this to work locally
        """
        auth_req = google.auth.transport.requests.Request()
        id_token = google.oauth2.id_token.fetch_id_token(
            auth_req, "https://phia-api-data-pipeline-prod-4wdelowz4a-uc.a.run.app"
        )
        client = GraphqlClient(
            endpoint="https://phia-api-data-pipeline-prod-4wdelowz4a-uc.a.run.app/internal/graphql",
            headers={"x-phia-token": token, "Authorization": f"Bearer {id_token}"},
        )
        resp = client.execute(query=get_query())
        brand_list = []
        brand_mapper = {}
        for brand in resp["data"]["brands"]:
            if brand["deletedAt"] is None:
                brand_list.append(brand["displayName"].strip())
                brand_mapper[brand["displayName"].lower()] = brand["id"]

        self.brand_list = brand_list
        self.brand_mapper = brand_mapper

    def get_brand_id_mapper(self):
        return self.brand_mapper

    def get_brand_list(self):
        return self.brand_list
