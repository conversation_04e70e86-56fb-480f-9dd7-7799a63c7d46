import os

from python_graphql_client import GraphqlClient
import google.auth.transport.requests
import google.oauth2.id_token


def get_query():
    return """
        query Query {
          sizes {
            id,
            gender
            category
            value
          }
        }
            """


class SizeIdMapper:

    def __init__(self):
        self.size_id_mapper = self.get_mapper()

    def get_mapper(self):
        token = os.getenv("X_PHIA_TOKEN")
        """
            Note, the below auth will not work locally unless you manually
            generate an identity token using 'gcloud auth print-identity-token'

            TODO - Fix this to work locally
        """
        auth_req = google.auth.transport.requests.Request()
        id_token = google.oauth2.id_token.fetch_id_token(
            auth_req, "https://phia-api-data-pipeline-prod-4wdelowz4a-uc.a.run.app"
        )
        client = GraphqlClient(
            endpoint="https://phia-api-data-pipeline-prod-4wdelowz4a-uc.a.run.app/internal/graphql",
            headers={"x-phia-token": token, "Authorization": f"Bearer {id_token}"},
        )
        resp = client.execute(query=get_query())
        mapper = {}
        for size in resp["data"]["sizes"]:
            size_key = f"{size['gender'].lower()}_{size['category'].lower()}_{size['value'].lower()}"
            mapper[size_key] = size["id"]
        return mapper
