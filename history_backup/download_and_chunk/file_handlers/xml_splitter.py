from zipfile import ZipFile
import lxml.etree as ET
import time
import logging
import datetime
import tracemalloc

from sqlalchemy.orm import sessionmaker, Session

from file_handlers.clean_xml_file import CleanXMLFile
from storage.gcs_storage_manager import GCSStorageManager
from utils.database.connector import connect_with_connector
from utils.helper import get_tracemem_use
from file_handlers.base_splitter import BaseSplitter


class XMLSplitter(BaseSplitter):
    def __init__(self, partner_name: str, batch_size: int, tag: str):
        super().__init__(partner_name, batch_size)
        self.tag = tag

    def split_data_into_batches(self, input_filepath: str):
        """
        Split a compressed XML file (zip) or plain XML file into smaller batches of records.

        Args:
            input_filepath: The path to the input zip file containing the XML file.
        """
        tracemalloc.start()
        if input_filepath.endswith(".xml"):
            with open(input_filepath, "rb") as file:
                """
                We want to use a last_modified_timestamp
                set by the Downloader implementation or fallback to now
                """
                file_modified_time = self.last_modified_timestamp or int(
                    datetime.datetime.now().timestamp()
                )
                self._process_xml_file(
                    file=file,
                    batch_size=self.batch_size,
                    file_timestamp=file_modified_time,
                )
        else:
            with ZipFile(input_filepath) as zf:
                for file in zf.namelist():
                    if file.endswith(".xml") or (
                        file.endswith(".tmp") and ".xml" in file
                    ):
                        logging.info("Splitting file into batches...")
                        file_info = zf.getinfo(file)
                        """
                            We want to use a last_modified_timestamp
                            set by the Downloader implementation or fallback to the
                            ZipFile's datetime info
                        """
                        file_timestamp = self.last_modified_timestamp or int(
                            datetime.datetime(*file_info.date_time).timestamp()
                        )
                        with zf.open(file) as f:
                            self._process_xml_file(
                                file=f,
                                batch_size=self.batch_size,
                                file_timestamp=file_timestamp,
                            )

    def _process_xml_file(self, file, batch_size, file_timestamp):
        """
        Process the XML file, splitting it into batches.

        Args:
            file: The file object representing the XML file.
            batch_size: The number of records in each batch.
            file_timestamp: The timestamp of the partner inventory file.
        """
        engine = connect_with_connector()
        Session = sessionmaker(bind=engine)
        start = time.time()
        clean_file = CleanXMLFile(file)
        logging.debug(f"Memory after file clean: {get_tracemem_use()}")
        context = ET.iterparse(clean_file, events=("end",))
        _, root = next(context)  # Get the root element
        batch_count, record_count = 0, 0
        batch_data = []

        with Session() as session:
            for _, element in context:
                if element.tag == self.tag:
                    batch_data.append(
                        ET.tostring(element, encoding="unicode", method="xml")
                    )
                    record_count += 1

                    if record_count >= batch_size:
                        self._upload_to_cloud_storage(
                            batch_data, batch_count, file_timestamp, session
                        )
                        batch_data = []
                        logging.debug(
                            f"Memory after batch {batch_count} clean: {get_tracemem_use()}"
                        )
                        batch_count += 1
                        record_count = 0
                        logging.info(
                            f"Time splitting {batch_size} records: {time.time() - start}"
                        )
                        start = time.time()
                    element.clear()
            # Write any remaining records to a final batch file
            if record_count > 0:
                self._upload_to_cloud_storage(
                    batch_data, batch_count, file_timestamp, session
                )
                batch_data = []

    def _upload_to_cloud_storage(
        self,
        batch_data: list[str],
        batch_count: int,
        file_timestamp: int,
        session: Session,
    ):
        """
        Write the current batch to an XML file to GCS.

        Args:
            batch_data: The list of serialized elements of the current batch.
            batch_count: The current batch number.
            file_timestamp: The timestamp of the partner inventory file.
        """
        xml_str = "<feed>\n" + "".join(batch_data) + "</feed>\n"
        batch_filename = f"{self.partner_name}_{file_timestamp}_{batch_count}.xml"
        partner_product_file_sync_id = self.create_partner_product_file_sync(
            session, batch_filename
        )
        GCSStorageManager().upload_xml_to_gcs(
            xml_str,
            batch_filename,
            self.partner_name,
            metadata={"partner_product_file_sync_id": partner_product_file_sync_id},
        )
        logging.info(f"Batch {batch_count} written with {len(batch_data)} records.")
        logging.debug(f"Memory after batch  {batch_count} write: {get_tracemem_use()}")
