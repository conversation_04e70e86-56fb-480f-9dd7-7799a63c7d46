from abc import ABC, abstractmethod

from sqlalchemy.orm import sessionmaker, Session

from utils.database.connector import connect_with_connector
from utils.database.service import create_partner_product_file_sync


class BaseSplitter(ABC):
    def __init__(self, partner_name: str, batch_size: int):
        self.partner_name = partner_name
        self.batch_size = batch_size
        self.last_modified_timestamp = None
        self.partner_product_sync_id = None

    @abstractmethod
    def split_data_into_batches(self, input_file_path: str):
        """Splits the partner feed data into batches and uploads to cloud storage"""
        pass

    @abstractmethod
    def _upload_to_cloud_storage(
        self, batch_data: str, batch_count: str, file_timestamp: str
    ):
        """Uploads the batched files to google cloud storage"""
        pass

    def set_last_modified_timestamp(self, last_modified_timestamp: int):
        """Return the last modified timestamp in seconds"""
        self.last_modified_timestamp = last_modified_timestamp

    def create_partner_product_file_sync(self, session: Session, file_name: str) -> str:
        partner_product_file_sync_id = create_partner_product_file_sync(
            session, self.partner_product_sync_id, file_name, self.partner_name
        )
        return partner_product_file_sync_id
