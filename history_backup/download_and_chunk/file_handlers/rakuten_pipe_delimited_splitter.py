import os
import io
import time
import logging
import gzip
import csv
import tracemalloc
from datetime import datetime

from sqlalchemy.orm import Session, sessionmaker

from storage.gcs_storage_manager import GCSStorageManager
from utils.database.connector import connect_with_connector
from utils.helper import get_tracemem_use
from file_handlers.base_splitter import BaseSplitter


class PipeDelimitedSplitter(BaseSplitter):

    def __init__(self, partner_name, batch_size):
        super().__init__(partner_name, batch_size)

    def split_data_into_batches(self, input_filepath: str):
        """
        Split a compressed | delimited TXT file (gzip) into smaller batches of csv records.

        Args:
            input_filepath: The path to the input gzip file containing the TXT file.
        """
        tracemalloc.start()
        with gzip.open(filename=input_filepath, mode="rt") as zf:
            logging.info("Splitting file into batches...")
            """
                We want to use a last_modified_timestamp
                set by the Downloader implementation or fallback to the file
                path's last modified time
            """
            file_modified_time = self.last_modified_timestamp or os.path.getmtime(
                input_filepath
            )

            file_timestamp = int(datetime.fromtimestamp(file_modified_time).timestamp())
            self._process_file(
                file=zf,
                batch_size=self.batch_size,
                file_timestamp=file_timestamp,
            )

    def _process_file(self, file, batch_size: int, file_timestamp: int):
        """
        Process the TXT file, splitting it into batches.

        Args:
            file: The file object representing the | delimited TXT file.
            batch_size: The number of records in each batch.
            file_timestamp: The timestamp of the partner inventory file.
        """
        engine = connect_with_connector()
        Session = sessionmaker(bind=engine)
        start = time.time()
        logging.debug(f"Memory before batching data: {get_tracemem_use()}")

        reader = csv.reader(file, delimiter="|")

        """
        Rakuten doesn't include file names in the | delimited txt files
        Instead, they are defined here: https://pubhelp.rakutenadvertising.com/hc/en-us
        /articles/8191594256013-Product-Catalog-Appendix-A-File-Field-Definitions
        """
        header = [
            "product_id",
            "product_name",
            "sku_number",
            "primary_category",
            "secondary_category",
            "product_url",
            "product_image_url",
            "buy_url",
            "short_product_description",
            "long_product_description",
            "discount",
            "discount_type",
            "sale_price",
            "retail_price",
            "begin_date",
            "end_date",
            "brand",
            "shipping",
            "keywords",
            "manufacturer_part_number",
            "manufacturer_name",
            "shipping_information",
            "availability",
            "universal_product_code",
            "class_id",
            "currency",
            "m1",
            "pixel",
            "attribute_1",
            "attribute_2",
            "attribute_3",
            "attribute_4",
            "attribute_5",
            "attribute_6",
            "attribute_7",
            "attribute_8",
            "attribute_9",
            "attribute_10",
        ]
        meta = next(reader)
        batch_count, record_count = 0, 0
        output_string_io = io.StringIO()
        writer = csv.writer(output_string_io, delimiter=",")

        writer.writerow(header)

        with Session() as session:
            for row in reader:
                writer.writerow(row)
                record_count += 1

                if record_count >= batch_size:
                    output_string_io.seek(0)
                    csv_str = output_string_io.getvalue()

                    self._upload_to_cloud_storage(
                        csv_str, record_count, batch_count, file_timestamp, session
                    )
                    logging.debug(
                        f"Memory after batch {batch_count} clean: {get_tracemem_use()}"
                    )
                    batch_count += 1
                    record_count = 0
                    logging.info(
                        f"Time splitting {batch_size} records: {time.time() - start}"
                    )
                    start = time.time()
                    output_string_io = io.StringIO()
                    writer = csv.writer(output_string_io, delimiter=",")
                    writer.writerow(header)

            # Write any remaining records to a final batch file
            if record_count > 0:
                output_string_io.seek(0)
                csv_str = output_string_io.getvalue()
                self._upload_to_cloud_storage(
                    csv_str, record_count, batch_count, file_timestamp, session
                )
                output_string_io = io.StringIO()

    def _upload_to_cloud_storage(
        self,
        batch_data: str,
        record_count: int,
        batch_count: int,
        file_timestamp: int,
        session: Session,
    ):
        """
        Write the current batch to an CSV file to GCS.

        Args:
            batch_data: The string text stream of serialized elements of the current batch.
            record_count: The number of records in the current batch.
            batch_count: The current batch number.
            file_timestamp: The timestamp of the partner inventory file.
        """
        batch_filename = f"{self.partner_name}_{file_timestamp}_{batch_count}.csv"
        partner_product_file_sync_id = self.create_partner_product_file_sync(
            session, batch_filename
        )
        GCSStorageManager().upload_csv_to_gcs(
            batch_data,
            batch_filename,
            self.partner_name,
            metadata={
                "partner_product_file_sync_id": partner_product_file_sync_id,
            },
        )
        logging.info(f"Batch {batch_count} written with {record_count} records.")
        logging.debug(f"Memory after batch {batch_count} write: {get_tracemem_use()}")
