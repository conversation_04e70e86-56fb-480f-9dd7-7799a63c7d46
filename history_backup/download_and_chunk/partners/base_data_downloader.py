from abc import ABC, abstractmethod


class BaseDataDownloader(ABC):

    def __init__(self, partner_name: str):
        self.partner_name = partner_name

    @abstractmethod
    def fetch_data(self) -> str:
        """Fetches the data for the partner"""
        pass

    def get_last_modified_timestamp(self) -> str | None:
        """Fetches the last modified timestamp of the file for the partner"""
        return None
