from ftplib import FTP
import os
from datetime import datetime
import logging

from partners.base_data_downloader import BaseDataDownloader


class ImpactDataDownloader(BaseDataDownloader):

    def __init__(self, partner_name: str, ftp_filepath: str):
        super().__init__(partner_name)
        self.ftp_filepath = ftp_filepath

    def fetch_data(self):
        """
        Fetches data for a given partner on Impact.

        Returns:
            local_downloaded_file (str): The path to the downloaded file.
        """
        local_downloaded_file = f"downloads/{self.partner_name}-feed.csv.gz"
        os.makedirs(os.path.dirname(local_downloaded_file), exist_ok=True)

        try:
            logging.info(
                "Accessing Impact FTP server to download the partner feed file for "
                + self.partner_name
            )
            self._download_from_ftp(
                self.ftp_filepath,
                local_downloaded_file,
            )
            logging.info(f"Data successfully fetched from {self.partner_name}")
            return local_downloaded_file

        except Exception as e:
            logging.error(f"Error processing data for {self.partner_name}: {e}")
            raise e

    def _download_from_ftp(
        self,
        product_catalog_remote_path: str,
        local_downloaded_file: str,
    ):
        """
        Downloads the inventory file for the given partner from Impact's FTP server.

        Args:
            product_catalog_remote_path (str): The remote path of the file to download.
            local_downloaded_file (str): The local path where the file will be saved.
        """
        ftp_host = os.getenv("IMPACT_FTP_HOST", "products.impact.com")
        ftp_username = os.getenv("IMPACT_FTP_USERNAME", "ps-ftp_4095344")
        ftp_password = os.getenv("IMPACT_FTP_PASSWORD")

        with FTP(ftp_host) as ftp:
            ftp.login(user=ftp_username, passwd=ftp_password)
            logging.info("Downloading the file from Impact FTP server...")
            with open(local_downloaded_file, "wb") as lf:
                ftp.retrbinary(
                    f"RETR {product_catalog_remote_path}", lambda chunk: lf.write(chunk)
                )
            product_catalog_file_timestamp = self._fetch_ftp_file_timestamp(
                self.ftp_filepath, ftp
            )
            # Setting timestamp of the downloaded file
            try:
                file_timestamp = datetime.fromisoformat(product_catalog_file_timestamp)
                mod_time = file_timestamp.timestamp()
                os.utime(local_downloaded_file, (mod_time, mod_time))
            except Exception as e:
                logging.error(
                    f"Error while setting file timestamp for {local_downloaded_file}: {e}"
                )
                raise e

            logging.info(f"Successfully downloaded the file to {local_downloaded_file}")

    @staticmethod
    def _fetch_ftp_file_timestamp(
        product_catalog_remote_path: str, ftp_connection: FTP
    ):
        """
        Fetches the last modified timestamp for the partner's feed file from the Impact FTP server.

        Args:
            product_catalog_remote_path (str): The remote path of the file.

        Returns:
            timestamp (datetime.datetime): The last modified timestamp for the partner's feed file
        """
        try:
            logging.info("Fetching the file timestamp for the partner's catalog file")
            response = ftp_connection.sendcmd(f"MDTM {product_catalog_remote_path}")
            if response.startswith("213 "):
                # Extract the timestamp and convert it to a datetime object
                timestamp_str = response[4:].split(".")[0]
                file_timestamp = datetime.strptime(timestamp_str, "%Y%m%d%H%M%S")
                logging.info(
                    f"Last modified time for file on the FTP server: {file_timestamp}"
                )
                return file_timestamp.isoformat()
            else:
                logging.info(
                    "Could not retrieve the file timestamp for the partner from Impact FTP server"
                )
        except Exception as e:
            logging.error(
                f"Error while fetching file timestamp for {product_catalog_remote_path}: {e}"
            )
            raise e
