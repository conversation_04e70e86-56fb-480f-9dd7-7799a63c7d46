import os
import logging
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


from partners.base_data_downloader import BaseDataDownloader


class EndpointDataDownloader(BaseDataDownloader):

    def __init__(self, partner_name: str, api_endpoint: str, file_type: str):
        super().__init__(partner_name)
        self.api_endpoint = api_endpoint
        self.file_type = file_type

    def fetch_data(self):
        """
        Fetches data for the retailer from the given endpoint.

        Returns:
            local_downloaded_file (str): The path to the downloaded file.
        """
        local_downloaded_file = f"downloads/{self.partner_name}-feed.{self.file_type}"
        os.makedirs(os.path.dirname(local_downloaded_file), exist_ok=True)

        try:
            logging.info(
                f"Accessing {self.partner_name} API Endpoint to download the feed file"
            )
            self._download_from_url(
                fetch_endpoint=self.api_endpoint,
                local_downloaded_file=local_downloaded_file,
            )
            logging.info(f"Data fetched successfully from {self.partner_name}")
            return local_downloaded_file

        except Exception as e:
            logging.error(f"Error processing data for {self.partner_name}: {e}")
            raise e

    def _download_from_url(self, fetch_endpoint: str, local_downloaded_file: str):
        """
        Downloads the inventory file for the retailer from the fetch endpoint.

        Args:
            fetch_endpoint (str): The endpoint to fetch the inventory file from.
            local_downloaded_file (str): The local path where the file will be saved.
        """
        try:
            response = self._download_with_retry(fetch_endpoint)
            response.raise_for_status()
            with open(local_downloaded_file, "wb") as file:
                file.write(response.content)
            logging.info(f"Successfully downloaded the file to {local_downloaded_file}")
        except Exception as e:
            logging.error(f"Error downloading data from the partner API endpoint: {e}")

    @staticmethod
    def _download_with_retry(endpoint, retries=3, backoff_factor=0.3):
        """
        Downloads the inventory file from the api endpoint with retries.

        Args:
            endpoint (str): The endpoint to fetch the inventory file from.
            retries (int): The number of times to retry the request. Defaults to 3.
            backoff_factor (float): The time delay between retries. Defaults to 0.3s

        Returns:
            response: The response object received from the api endpoint.
        """
        session = requests.Session()
        retries = Retry(
            total=retries,
            read=retries,
            connect=retries,
            backoff_factor=backoff_factor,
            status_forcelist=[408, 429, 500, 502, 503, 504],
        )
        session.mount("http://", HTTPAdapter(max_retries=retries))
        session.mount("https://", HTTPAdapter(max_retries=retries))

        response = session.request("GET", endpoint)
        return response
