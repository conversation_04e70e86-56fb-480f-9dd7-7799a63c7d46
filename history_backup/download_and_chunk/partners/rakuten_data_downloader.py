import paramiko
import os
import logging

from partners.base_data_downloader import BaseDataDownloader


class RakutenDataDownloader(BaseDataDownloader):
    def __init__(self, partner_name: str, sftp_filepath: str):
        super().__init__(partner_name)
        self.sftp_filepath = sftp_filepath

    def fetch_data(self) -> str:
        """
        Fetches data for a given partner on Rakuten.

        Returns:
            local_downloaded_file (str): The path to the downloaded file.
        """
        product_catalog_remote_path = self.sftp_filepath
        local_downloaded_file = f"downloads/{self.partner_name}-feed.txt.zip"
        os.makedirs(os.path.dirname(local_downloaded_file), exist_ok=True)

        try:
            logging.info(
                f"Accessing Rakuten SFTP server to download the feed file for {self.partner_name}"
            )
            self._download_from_sftp(product_catalog_remote_path, local_downloaded_file)
            logging.info(
                f"Data successfully fetched from {self.partner_name}: {local_downloaded_file}"
            )
            return local_downloaded_file

        except Exception as e:
            logging.error(f"Error processing data for {self.partner_name}: {e}")
            raise e

    @staticmethod
    def _download_from_sftp(
        product_catalog_remote_path: str, local_downloaded_file: str
    ):
        """
        Downloads the inventory file for the given partner from Rakuten's SFTP server.

        Args:
            product_catalog_remote_path (str): The remote path of the file to download.
            local_downloaded_file (str): The local path where the file will be saved.
        """
        sftp_host = os.getenv("RAKUTEN_HOST", "aftp.linksynergy.com")
        sftp_port = int(os.getenv("RAKUTEN_PORT", 22))
        sftp_username = os.getenv("RAKUTEN_USERNAME", "rkp_4152161")
        sftp_password = os.getenv("RAKUTEN_SFTP_PASSWORD")

        with paramiko.Transport((sftp_host, sftp_port)) as transport:
            transport.connect(username=sftp_username, password=sftp_password)
            with paramiko.SFTPClient.from_transport(transport) as sftp:
                logging.info("Downloading the file from Rakuten SFTP server...")
                sftp.get(product_catalog_remote_path, local_downloaded_file)
                logging.info("File downloaded successfully.")
