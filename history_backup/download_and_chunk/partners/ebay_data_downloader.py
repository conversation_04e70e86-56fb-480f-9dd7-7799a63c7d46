from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
import os
import time
import logging
from datetime import datetime

import requests
import math
import shutil

from enums import Partners
from partners.base_data_downloader import BaseDataDownloader
from utils.ebay_oauth.oauth2api import oauth2api
from utils.ebay_oauth.model.model import environment


class EbayDataDownloader(BaseDataDownloader):

    def __init__(self):
        super().__init__(Partners.EBAY.value)
        self.snapshot_api = (
            "https://api.ebay.com/buy/feed/v1_beta/item_snapshot?category_id=11450"
        )
        self.access_token = None
        self.last_refresh = 0
        """
        Note: The max_chunk_size is using the maximum number of
        bytes we can reqeust from Ebay. More details on
        retrieving a gzip file feed from Ebay API available here:
        https://developer.ebay.com/api-docs/buy/static/api-feed_beta.html#retrv-gzip
        """
        self.max_chunk_size = 104857600
        self.client_id = os.getenv("EBAY_APP_ID")
        self.client_secret = os.getenv("EBAY_CERT_ID")
        self.last_modified_timestamp = None

    def fetch_data(self):
        """
        Fetches snapshot data for ebay.

        Returns:
            local_concatenated_file (str): The path to concatenated downloaded file
        """
        local_downloads_folder = "downloads/ebay-feed"
        local_concatenated_file = "downloads/ebay-feed.tsv.gz"
        os.makedirs(local_downloads_folder, exist_ok=True)

        """
        When we ingest data from the getItemSnapshotFeed we are pulling all new items
        and items that have changed over the course of a given hour. In other words,
        making a request for 2024-05-01T08:00:00.000Z gives us the window from 8:00am to 8:59am
        on 2024-05-01. We can only request a time window 2 hours after the desired timestamp.
        So in order to get the 8:00 to 8:59am window, we must make the request after 11:00am
        """
        date = time.strftime(
            "%Y-%m-%dT%H:00:00.000Z", time.gmtime(time.time() - 2 * 60 * 60)
        )
        logging.info(f"Generating ebay snapshot API feed for date {date}")
        self.snapshot_api += f"&snapshot_date={date}"

        try:
            start_time = time.time()
            logging.info("Accessing ebay snapshot API to download the feed files")
            self._download_from_url(
                fetch_endpoint=self.snapshot_api,
                local_dir=local_downloads_folder,
                local_concatenated_file=local_concatenated_file,
            )
            logging.info(
                f"Time collecting items from {self.partner_name}: {time.time() - start_time}"
            )
            return local_concatenated_file

        except Exception as e:
            logging.error(f"Error processing data for {self.partner_name}: {e}")
            raise e

    def _download_from_url(
        self, fetch_endpoint: str, local_dir: str, local_concatenated_file: str
    ):
        """
        Downloads the inventory files from eBay's snapshot api.

        Args:
            local_dir (str): The local folder path where the files will be saved.
            local_concatenated_file (str): The local file path where the downloaded files
                                           will be saved as a single concatenated file.
        """

        self._refresh_oauth()

        start_bytes = 0
        end_bytes = self.max_chunk_size

        try:
            headers = self._get_headers(start_bytes, end_bytes)
            response = requests.get(fetch_endpoint, headers=headers)
            response.raise_for_status()
            last_modified_str = response.headers.get("Last-Modified")
            last_modified_timestamp = math.floor(
                datetime.strptime(
                    last_modified_str, "%a, %d %b %Y %H:%M:%S %Z"
                ).timestamp()
            )
            self.last_modified_timestamp = last_modified_timestamp

            if response.status_code == 200:  # Full content
                file_name = f"{local_dir}/ebay-feed.tsv.gz.0"
                os.makedirs(os.path.dirname(file_name), exist_ok=True)
                with open(file_name, "wb") as f:
                    f.write(response.content)

                self._concatenate_chunks([0], local_dir, local_concatenated_file)

            elif response.status_code == 206:  # Partial content
                max_bytes = int(response.headers["Content-Range"].split("/")[1])
                logging.info(f"Bytes to be received from ebay: {max_bytes}")

                max_calls = math.ceil(max_bytes / self.max_chunk_size)

                file_name = f"{local_dir}/ebay-feed.tsv.gz.0"
                os.makedirs(os.path.dirname(file_name), exist_ok=True)
                with open(file_name, "wb") as f:
                    f.write(response.content)

                with ThreadPoolExecutor(max_workers=10) as executor:
                    for chunk in range(1, max_calls):
                        executor.submit(
                            self._download_partial_feed,
                            chunk,
                            local_dir,
                        )
                    executor.shutdown(wait=True)

                self._concatenate_chunks(
                    [i for i in range(max_calls)], local_dir, local_concatenated_file
                )

        except Exception as e:
            logging.error(f"Error downloading snapshot data from ebay: {e}")
            raise e

    def _download_partial_feed(self, chunk_count: int, local_dir: str):
        """
        Downloads a single hundred mebibyte chunk (max_chunk_size) from the ebay snapshot api.
        """
        start_bytes = chunk_count * self.max_chunk_size + 1
        end_bytes = (chunk_count + 1) * self.max_chunk_size

        headers = self._get_headers(start_bytes, end_bytes)
        response = requests.get(self.snapshot_api, headers=headers)

        logging.info(f"Downloading chunk {chunk_count}")

        try:
            response.raise_for_status()
            filename = f"{local_dir}/ebay-feed.tsv.gz.{chunk_count}"
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            with open(filename, "ab") as f:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        f.write(chunk)
        except Exception as e:
            logging.error(
                f"Error while downloading partial "
                f"feed for chunk {chunk_count} from ebay snapshot api: {e}"
            )
            raise e

    def _refresh_oauth(self):
        """Refreshes the application token to fetch data from ebay."""

        if self.access_token is None or time.time() - self.last_refresh > 6000:

            oauth2api_inst = oauth2api(
                client_id=self.client_id, client_secret=self.client_secret
            )
            app_scopes = [
                "https://api.ebay.com/oauth/api_scope",
                "https://api.ebay.com/oauth/api_scope/buy.item.feed",
            ]
            app_token = oauth2api_inst.get_application_token(
                environment.PRODUCTION, app_scopes
            )
            self.access_token = app_token.access_token
            self.last_refresh = time.time()

    def _get_headers(self, start_bytes, end_bytes):
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
            "X-EBAY-C-MARKETPLACE-ID": "EBAY_US",
            "X-EBAY-C-ENDUSERCTX": "affiliateCampaignId=<ePNCampaignId>,"
            "affiliateReferenceId=<referenceId>",
            "Accept-Encoding": "application/json",
            "Range": f"bytes={start_bytes}-{end_bytes}",
        }

    def get_last_modified_timestamp(self) -> str | None:
        """Fetches the last modified timestamp of the file for the partner"""
        return self.last_modified_timestamp

    @staticmethod
    def _concatenate_chunks(total_chunks, local_dir, output_filename):
        """Concatenate gzip files into a single gzip file."""
        logging.info(
            f"Concatenating chunks received from ebay into a single gzip file: {output_filename}"
        )
        os.makedirs(os.path.dirname(output_filename), exist_ok=True)
        with open(output_filename, "wb") as wfd:
            for i in total_chunks:
                with open(f"{local_dir}/ebay-feed.tsv.gz.{i}", "rb") as fd:
                    shutil.copyfileobj(fd, wfd)
        logging.info("Successfully concatenated chunks received from ebay")
