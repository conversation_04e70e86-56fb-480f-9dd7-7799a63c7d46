from concurrent.futures import ThreadPoolExecutor
import paramiko
import os
import time
import logging

from partners.base_data_downloader import BaseDataDownloader
from enums import Partners


class PoshmarkDataDownloader(BaseDataDownloader):

    def __init__(self):
        super().__init__(partner_name=Partners.POSHMARK.value)

    def fetch_data(self):
        """
        Fetches data for poshmark.

        Returns:
            local_downloads_folder (str): The path to the folder where files are downloaded
        """
        local_downloads_folder = "downloads/poshmark-feed"
        os.makedirs(local_downloads_folder, exist_ok=True)

        try:
            start_time = time.time()
            logging.info("Accessing Poshmark SFTP server to download the feed files")
            self._download_from_sftp(local_downloads_folder)

            logging.info(
                f"Time collecting items from {self.partner_name}: {time.time() - start_time}"
            )
            return local_downloads_folder

        except Exception as e:
            logging.error(f"Error processing data for {self.partner_name}: {e}")
            raise e

    def _download_from_sftp(self, local_dir: str):
        """
        Downloads the inventory files from Poshmark's SFTP server.

        Args:
            local_dir (str): The local path where the files will be saved.
        """
        sftp_host = os.getenv("POSHMARK_SFTP_HOST", "************")
        sftp_port = os.getenv("POSHMARK_SFTP_PORT", 22)
        sftp_username = os.getenv("POSHMARK_SFTP_USERNAME", "phia")
        sftp_password = os.getenv("POSHMARK_SFTP_PASSWORD")
        sftp_remote_dir = "/phia/us/phia/"

        with paramiko.Transport((sftp_host, sftp_port)) as transport:
            transport.connect(username=sftp_username, password=sftp_password)
            with paramiko.SFTPClient.from_transport(transport) as sftp:
                logging.info("Downloading the file from SFTP server...")

                dir = sftp.listdir(sftp_remote_dir)
                files = [f for f in dir if f.endswith(".gz")]
                logging.info(files)
                with ThreadPoolExecutor(max_workers=10) as executor:
                    for file in files:
                        executor.submit(
                            self._download_and_process_individual_file,
                            sftp_remote_dir,
                            local_dir,
                            file,
                            (sftp_host, sftp_port, sftp_username, sftp_password),
                        )
                    executor.shutdown(wait=True)

                logging.info("File downloaded successfully.")

    @staticmethod
    def _download_and_process_individual_file(
        remote_dir: str, local_dir: str, file_name: str, sftp_config: tuple
    ):
        """
        Downloads a single file from the SFTP server and processes it.

        Args:
            remote_dir (str): The directory on the SFTP server where the file is located.
            local_dir (str): The local directory where the file will be saved.
            file_name (str): The name of the file to download.
            sftp_config (tuple): The configuration for the SFTP server.
        """
        remote_file_path = f"{remote_dir}/{file_name}"
        local_file_path = f"{local_dir}/{file_name}"
        sftp_host, sftp_port, sftp_username, sftp_password = sftp_config

        with paramiko.Transport((sftp_host, sftp_port)) as transport:
            transport.connect(username=sftp_username, password=sftp_password)
            with paramiko.SFTPClient.from_transport(transport) as sftp:
                sftp.get(remote_file_path, local_file_path)
            logging.info(f"Downloaded {file_name} from {remote_dir}")
