from dataclasses import dataclass
from typing import Type, Dict

from enums import Partners

from file_handlers.base_splitter import BaseSplitter
from file_handlers.rakuten_pipe_delimited_splitter import PipeDelimitedSplitter
from file_handlers.tsv_splitter import TSVSplitter
from file_handlers.xml_splitter import XMLSplitter
from file_handlers.csv_splitter import CSVSplitter

from partners.base_data_downloader import BaseDataDownloader
from partners.cj_data_downloader import CJDataDownloader
from partners.ebay_data_downloader import EbayDataDownloader
from partners.impact_data_downloader import ImpactDataDownloader
from partners.endpoint_data_downloader import EndpointDataDownloader
from partners.poshmark_data_downloader import PoshmarkDataDownloader
from partners.rakuten_data_downloader import RakutenDataDownloader


@dataclass
class PartnerConfig:
    data_downloader: Type[BaseDataDownloader]
    file_handler: Type[BaseSplitter]


class ConfigurationFactory:

    SUPPORTED_PARTNERS = [partner.value for partner in Partners]

    CONFIGURATIONS: Dict[str, PartnerConfig] = {}

    @classmethod
    def initialize_configurations(cls):
        cls.CONFIGURATIONS = {
            Partners.BOPF.value: cls.create_impact_config(
                Partners.BOPF.value,
                "/BOPF-Business-of-preloved-Fashion-Affiliate-Program/BOPF-Business-of-preloved-Fashion_IR.csv.gz",  # noqa
            ),
            Partners.EBAY.value: PartnerConfig(
                data_downloader=EbayDataDownloader(),
                file_handler=TSVSplitter(
                    partner_name=Partners.EBAY.value, batch_size=5000
                ),
            ),
            Partners.ETSY.value: cls.create_endpoint_config(
                Partners.ETSY.value,
                "https://productdata.awin.com/datafeed/download/apikey/32cc69065523725b5cbad1be497b3be2/fid/87273/format/csv/language/en/delimiter/%2C/compression/gzip/adultcontent/1/columns/data_feed_id%2Cmerchant_id%2Cmerchant_name%2Caw_product_id%2Caw_deep_link%2Caw_image_url%2Caw_thumb_url%2Ccategory_id%2Ccategory_name%2Cbrand_id%2Cbrand_name%2Cmerchant_product_id%2Cmpn%2Cproduct_name%2Cdescription%2Clanguage%2Cmerchant_deep_link%2Cmerchant_image_url%2Cdelivery_time%2Ccurrency%2Csearch_price%2Cdelivery_cost%2Cin_stock%2Cstock_quantity%2Ccondition%2Cproduct_type%2Cdimensions%2Ccolour%2Ckeywords%2Ccustom_1%2Ccustom_2%2Ccustom_3%2Ccustom_4%2Cdelivery_weight%2CFashion%3Asize%2CFashion%3Amaterial%2Calternate_image%2Cproduct_price_old/",  # noqa
                "csv.gz",
            ),
            Partners.FARFETCH.value: cls.create_endpoint_config(
                Partners.FARFETCH.value,
                "http://feeds.performancehorizon.com/phia/1011l270/productsup_partnerize_phia_us_en_USD.xml.zip",  # noqa
                "xml.zip",
            ),
            Partners.GRAILED.value: cls.create_impact_config(
                Partners.GRAILED.value,
                "/Grailed/Entire-Feed_IR.csv.gz",
                batch_size=5000,
            ),
            Partners.HEWI.value: cls.create_impact_config(
                Partners.HEWI.value,
                "/Hardly-Ever-Worn-It/HEWI-Main-Site-Products_IR.csv.gz",
            ),
            Partners.HURR.value: cls.create_cj_config(
                Partners.HURR.value, "Hurr-Google_Product_Feed-shopping.xml.zip"
            ),
            Partners.LUXE_COLLECTIVE.value: cls.create_impact_config(
                Partners.LUXE_COLLECTIVE.value,
                "/Luxe-Collective/Luxe-Collective_IR.csv.gz",
            ),
            Partners.POSHMARK.value: PartnerConfig(
                data_downloader=PoshmarkDataDownloader(),
                file_handler=CSVSplitter(
                    partner_name=Partners.POSHMARK.value, batch_size=5000
                ),
            ),
            Partners.REBAG.value: cls.create_cj_config(
                Partners.REBAG.value,
                "Rebag-Google_Product_Feed-shopping.xml.zip",
            ),
            Partners.STOCKX.value: cls.create_impact_config(
                Partners.STOCKX.value,
                "/StockX/United-States-All-In-Eng-USD-v2_IR.csv.gz",
            ),
            Partners.THE_LUXURY_CLOSET.value: cls.create_cj_config(
                Partners.THE_LUXURY_CLOSET.value,
                "The_Luxury_Closet-CJ_Test-shopping.xml.zip",
                5000,
            ),
            Partners.THE_REAL_REAL.value: cls.create_impact_config(
                Partners.THE_REAL_REAL.value,
                "/The-RealReal/The-RealReal-Product-Catalog-NEW_CUSTOM.csv.gz",
                batch_size=5000,
            ),
            Partners.VESTIAIRE_COLLECTIVE.value: cls.create_cj_config(
                Partners.VESTIAIRE_COLLECTIVE.value,
                "Vestiaire_Collective-VCO_Aff_EN_USD-shopping.xml.zip",
                batch_size=5000,
            ),
            Partners.SHOP_PREMIUM_OUTLETS.value: cls.create_rakuten_config(
                Partners.SHOP_PREMIUM_OUTLETS.value,
                "/50745/50745_4152161_2962_cmp.txt.gz",
            ),
        }

    @staticmethod
    def create_impact_config(partner_name, ftp_filepath, batch_size=5000):
        data_downloader = ImpactDataDownloader(
            partner_name=partner_name, ftp_filepath=ftp_filepath
        )
        file_handler = CSVSplitter(partner_name=partner_name, batch_size=batch_size)
        return PartnerConfig(data_downloader=data_downloader, file_handler=file_handler)

    @staticmethod
    def create_rakuten_config(partner_name, ftp_filepath, batch_size=5000):
        data_downloader = RakutenDataDownloader(
            partner_name=partner_name, sftp_filepath=ftp_filepath
        )
        file_handler = PipeDelimitedSplitter(
            partner_name=partner_name, batch_size=batch_size
        )
        return PartnerConfig(data_downloader=data_downloader, file_handler=file_handler)

    @staticmethod
    def create_cj_config(partner_name, sftp_filepath, batch_size=5000, tag="entry"):
        data_downloader = CJDataDownloader(
            partner_name=partner_name, sftp_filepath=sftp_filepath
        )
        file_handler = XMLSplitter(
            partner_name=partner_name, batch_size=batch_size, tag=tag
        )
        return PartnerConfig(data_downloader=data_downloader, file_handler=file_handler)

    @staticmethod
    def create_endpoint_config(
        partner_name, api_endpoint, file_type, batch_size=5000, tag="item"
    ):
        data_downloader = EndpointDataDownloader(
            partner_name=partner_name,
            api_endpoint=api_endpoint,
            file_type=file_type,
        )
        if partner_name == Partners.ETSY.value:
            file_handler = CSVSplitter(partner_name=partner_name, batch_size=batch_size)
        elif partner_name == Partners.FARFETCH.value:
            file_handler = XMLSplitter(
                partner_name=partner_name, batch_size=batch_size, tag=tag
            )
        elif partner_name == Partners.THE_LUXURY_CLOSET.value:
            file_handler = XMLSplitter(
                partner_name=partner_name, batch_size=batch_size, tag=tag
            )
        return PartnerConfig(data_downloader=data_downloader, file_handler=file_handler)

    @classmethod
    def get_partner_configuration(cls, partner_name):
        """
        Retrieves the partner specific configuration.
        Args:
            partner_name: The name of the partner

        Returns:
            downloader: The data downloader for the partner.
            splitter: The file handler (splitter) for the partner.
            batch_size: The batch size needed to split data into chunks.
        """
        cls.initialize_configurations()
        partner_configuration = ConfigurationFactory.CONFIGURATIONS.get(partner_name)
        downloader = partner_configuration.data_downloader
        splitter = partner_configuration.file_handler
        return downloader, splitter
