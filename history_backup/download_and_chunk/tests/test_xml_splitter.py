import os

import pytest
import time
from unittest.mock import patch, Mock

from file_handlers.xml_splitter import XMLSplitter
from lxml import etree as ET
from storage.gcs_storage_manager import GCSStorageManager
from enums import Partners


@pytest.fixture
def setup_and_teardown():
    # Set up any required resources before each test
    input_zip_file = os.getcwd() + "/tests/mock_data/test_data.xml.zip"
    batch_size = 10000
    batch_count = 0
    product_tag = "entry"
    file_timestamp = round(time.time())
    yield input_zip_file, batch_size, batch_count, product_tag, file_timestamp


@patch.object(GCSStorageManager, "upload_xml_to_gcs")
def test_split_data_into_batches(mock_upload, setup_and_teardown):
    # Test the split_zipped_xml_into_batches function
    input_zip_file, batch_size, batch_count, product_tag, file_timestamp = (
        setup_and_teardown
    )
    xml_splitter = XMLSplitter(
        partner_name=Partners.REBAG.value, batch_size=batch_size, tag=product_tag
    )
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    xml_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    xml_splitter.split_data_into_batches(
        input_filepath=input_zip_file,
    )
    # Check if the upload_xml_to_gcs method was called the expected number of times
    assert mock_upload.call_count == 10


@patch.object(GCSStorageManager, "upload_xml_to_gcs")
def test_upload_to_cloud_storage(mock_upload, setup_and_teardown):
    input_zip_file, batch_size, batch_count, product_tag, file_timestamp = (
        setup_and_teardown
    )
    xml_splitter = XMLSplitter(
        partner_name=Partners.REBAG.value, batch_size=batch_size, tag=product_tag
    )
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    xml_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    xml_splitter._upload_to_cloud_storage(
        batch_data=input_zip_file,
        batch_count=batch_count,
        file_timestamp=file_timestamp,
        session=Mock(),
    )
    assert mock_upload.called is True


@patch.object(GCSStorageManager, "upload_xml_to_gcs")
def test_batch_content(mock_upload, setup_and_teardown):
    # Test if the batch files contain the correct number of entries
    input_zip_file, batch_size, _, product_tag, _ = setup_and_teardown
    xml_splitter = XMLSplitter(
        partner_name=Partners.REBAG.value, batch_size=batch_size, tag=product_tag
    )
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    xml_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    xml_splitter.split_data_into_batches(input_filepath=input_zip_file)
    # Check if the uploaded XML strings contain the correct number of entries
    for call_args in mock_upload.call_args_list:
        xml_str = call_args[0][0]
        root = ET.fromstring(xml_str)
        assert len(root) == batch_size
