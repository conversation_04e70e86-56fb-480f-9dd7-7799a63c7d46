from unittest.mock import patch, MagicMock

from file_handlers.tsv_splitter import T<PERSON><PERSON><PERSON>litter
from main import download_and_chunk
from factory import ConfigurationFactory
from enums import Partners
import pytest
from file_handlers.xml_splitter import XMLSplitter
from file_handlers.csv_splitter import CSVSplitter
from partners.cj_data_downloader import CJDataDownloader
from partners.ebay_data_downloader import EbayDataDownloader
from partners.impact_data_downloader import ImpactDataDownloader
from partners.endpoint_data_downloader import EndpointDataDownloader
from partners.poshmark_data_downloader import PoshmarkDataDownloader


@pytest.mark.parametrize(
    "partner_name, sftp_filepath",
    [
        (Partners.REBAG.value, "Rebag-Google_Product_Feed-shopping.xml.zip"),
        (Partners.HURR.value, "Hurr-Google_Product_Feed-shopping.xml.zip"),
        (
            Partners.THE_LUXURY_CLOSET.value,
            "The_Luxury_Closet-CJ_Test-shopping.xml.zip",
        ),
        (
            Partners.VESTIAIRE_COLLECTIVE.value,
            "Vestiaire_Collective-VCO_Aff_EN_USD-shopping.xml.zip",
        ),
    ],
)
def test_download_and_chunk_cj_partners(partner_name, sftp_filepath):
    downloader, splitter = ConfigurationFactory.get_partner_configuration(partner_name)

    assert isinstance(
        splitter, XMLSplitter
    ), f"splitter should be an instance of XMLSplitter, got {type(splitter)} instead"
    assert isinstance(
        downloader, CJDataDownloader
    ), f"download should be an instance of CJDataDownloader, got {type(downloader)} instead"

    assert (
        splitter.tag == "entry"
    ), f"splitter.tag should be 'entry', got {splitter.tag} instead"
    assert (
        downloader.sftp_filepath == sftp_filepath
    ), f"downloader.sftp_filepath should be {sftp_filepath}, got {downloader.sftp_filepath} instead"

    with patch.object(
        downloader, "fetch_data", return_value="mock_data/test_data.xml.zip"
    ) as mock_fetch_data:
        splitter.split_data_into_batches = MagicMock()
        download_and_chunk(partner_name, downloader, splitter)

        # Check if fetch_data was called once
        mock_fetch_data.assert_called_once()

        # Check if split_data_into_batches was called with the correct parameters
        # Here we use the same path returned by the mock to ensure consistency
        splitter.split_data_into_batches.assert_called_once_with(
            input_filepath="mock_data/test_data.xml.zip"
        )


@pytest.mark.parametrize(
    "partner_name, ftp_filepath",
    [
        (
            Partners.BOPF.value,
            "/BOPF-Business-of-preloved-Fashion-Affiliate-Program/BOPF-Business-of-preloved-Fashion_IR.csv.gz",  # noqa
        ),
        (Partners.GRAILED.value, "/Grailed/Entire-Feed_IR.csv.gz"),
        (
            Partners.HEWI.value,
            "/Hardly-Ever-Worn-It/HEWI-Main-Site-Products_IR.csv.gz",
        ),
        (
            Partners.LUXE_COLLECTIVE.value,
            "/Luxe-Collective/Luxe-Collective_IR.csv.gz",
        ),
        (
            Partners.STOCKX.value,
            "/StockX/United-States-All-In-Eng-USD-v2_IR.csv.gz",
        ),
        (
            Partners.THE_REAL_REAL.value,
            "/The-RealReal/The-RealReal-Product-Catalog-NEW_CUSTOM.csv.gz",
        ),
    ],
)
def test_download_and_chunk_impact_partners(partner_name, ftp_filepath):
    downloader, splitter = ConfigurationFactory.get_partner_configuration(partner_name)

    assert isinstance(
        splitter, CSVSplitter
    ), f"splitter should be an instance of CSVSplitter, got {type(splitter)} instead"
    assert isinstance(
        downloader, ImpactDataDownloader
    ), f"download should be an instance of ImpactDataDownloader, got {type(downloader)} instead"

    assert (
        downloader.ftp_filepath == ftp_filepath
    ), f"downloader.ftp_filepath should be {ftp_filepath}, got {downloader.ftp_filepath} instead"

    with patch.object(
        downloader, "fetch_data", return_value="mock_data/test_data.csv.gz"
    ) as mock_fetch_data:
        splitter.split_data_into_batches = MagicMock()
        download_and_chunk(partner_name, downloader, splitter)

        # Check if fetch_data was called once
        mock_fetch_data.assert_called_once()

        # Check if split_data_into_batches was called with the correct parameters
        # Here we use the same path returned by the mock to ensure consistency
        splitter.split_data_into_batches.assert_called_once_with(
            input_filepath="mock_data/test_data.csv.gz"
        )


@pytest.mark.parametrize(
    "partner_name, api_endpoint, file_type, file_handler",
    [
        (
            Partners.ETSY.value,
            "https://productdata.awin.com/datafeed/download/apikey/32cc69065523725b5cbad1be497b3be2/fid/87273/format/csv/language/en/delimiter/%2C/compression/gzip/adultcontent/1/columns/data_feed_id%2Cmerchant_id%2Cmerchant_name%2Caw_product_id%2Caw_deep_link%2Caw_image_url%2Caw_thumb_url%2Ccategory_id%2Ccategory_name%2Cbrand_id%2Cbrand_name%2Cmerchant_product_id%2Cmpn%2Cproduct_name%2Cdescription%2Clanguage%2Cmerchant_deep_link%2Cmerchant_image_url%2Cdelivery_time%2Ccurrency%2Csearch_price%2Cdelivery_cost%2Cin_stock%2Cstock_quantity%2Ccondition%2Cproduct_type%2Cdimensions%2Ccolour%2Ckeywords%2Ccustom_1%2Ccustom_2%2Ccustom_3%2Ccustom_4%2Cdelivery_weight%2CFashion%3Asize%2CFashion%3Amaterial%2Calternate_image%2Cproduct_price_old/",  # noqa
            "csv.gz",
            CSVSplitter,
        ),
        (
            Partners.FARFETCH.value,
            "http://feeds.performancehorizon.com/phia/1011l270/productsup_partnerize_phia_us_en_USD.xml.zip",  # noqa
            "xml.zip",
            XMLSplitter,
        ),
    ],
)
def test_download_and_chunk_endpoint_partners(
    partner_name, api_endpoint, file_type, file_handler
):
    downloader, splitter = ConfigurationFactory.get_partner_configuration(partner_name)

    assert isinstance(
        splitter, file_handler
    ), f"splitter should be an instance of {file_handler}, got {type(splitter)} instead"

    assert isinstance(
        downloader, EndpointDataDownloader
    ), f"download should be an instance of EndpointDataDownloader, got {type(downloader)} instead"

    assert (
        downloader.api_endpoint == api_endpoint
    ), f"downloader.api_endpoint should be {api_endpoint}, got {downloader.api_endpoint} instead"

    assert (
        downloader.file_type == file_type
    ), f"downloader.file_type should be {file_type}, got {downloader.file_type} instead"

    with patch.object(
        downloader,
        "fetch_data",
        return_value=f"mock_data/test_data.{file_type}",
    ) as mock_fetch_data:
        splitter.split_data_into_batches = MagicMock()
        download_and_chunk(partner_name, downloader, splitter)

        # Check if fetch_data was called once
        mock_fetch_data.assert_called_once()

        # Check if split_data_into_batches was called with the correct parameters
        # Here we use the same path returned by the mock to ensure consistency
        splitter.split_data_into_batches.assert_called_once_with(
            input_filepath=f"mock_data/test_data.{file_type}"
        )


def test_download_and_chunk_poshmark():
    downloader, splitter = ConfigurationFactory.get_partner_configuration(
        Partners.POSHMARK.value
    )

    assert isinstance(
        splitter, CSVSplitter
    ), f"splitter should be an instance of CSVSplitter, got {type(splitter)} instead"
    assert isinstance(
        downloader, PoshmarkDataDownloader
    ), f"download should be an instance of PoshmarkDataDownloader, got {type(downloader)} instead"

    with patch.object(
        downloader, "fetch_data", return_value="mock_data"
    ) as mock_fetch_data:
        splitter.split_data_into_batches = MagicMock()
        download_and_chunk(Partners.POSHMARK.value, downloader, splitter)

        # Check if fetch_data was called once
        mock_fetch_data.assert_called_once()

        # Check if split_data_into_batches was called with the correct parameters
        # poshmark fetch_data returns path to a folder instead of a single file
        # Here we use the same path returned by the mock to ensure consistency
        splitter.split_data_into_batches.assert_called_once_with(
            input_filepath="mock_data"
        )


def test_download_and_chunk_ebay():
    downloader, splitter = ConfigurationFactory.get_partner_configuration(
        Partners.EBAY.value
    )

    assert isinstance(
        splitter, TSVSplitter
    ), f"splitter should be an instance of TSVSplitter, got {type(splitter)} instead"
    assert isinstance(
        downloader, EbayDataDownloader
    ), f"download should be an instance of EbayDataDownloader, got {type(downloader)} instead"

    with patch.object(
        downloader, "fetch_data", return_value="mock_data/test_data.tsv.gz"
    ) as mock_fetch_data:
        splitter.split_data_into_batches = MagicMock()
        download_and_chunk(Partners.EBAY.value, downloader, splitter)

        # Check if fetch_data was called once
        mock_fetch_data.assert_called_once()

        # Check if split_data_into_batches was called with the correct parameters
        splitter.split_data_into_batches.assert_called_once_with(
            input_filepath="mock_data/test_data.tsv.gz"
        )
