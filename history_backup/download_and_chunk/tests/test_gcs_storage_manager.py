import pytest
from unittest.mock import patch, Mock
from storage.gcs_storage_manager import GCSStorageManager


@pytest.fixture
def mock_storage_client():
    with patch("google.cloud.storage.Client") as mock:
        yield mock


@pytest.fixture
def mock_task_client():
    with patch("storage.gcs_storage_manager.GCSTaskManager") as mock:
        yield mock


@pytest.fixture
def mock_bucket(mock_storage_client):
    mock_bucket = Mock()
    mock_storage_client.return_value.bucket.return_value = mock_bucket
    return mock_bucket


@pytest.fixture
def mock_blob(mock_bucket):
    mock_blob = Mock()
    mock_bucket.blob.return_value = mock_blob
    return mock_blob


def test_upload_xml_to_gcs(
    mock_storage_client, mock_bucket, mock_blob, mock_task_client
):
    gcs_manager = GCSStorageManager()
    xml_str = "<root>test</root>"
    file_name = "test.xml"
    partner_name = "partner1"

    gcs_manager.upload_xml_to_gcs(xml_str, file_name, partner_name)

    mock_storage_client.assert_called_once()
    mock_bucket.blob.assert_called_once_with(f"{partner_name}/{file_name}")
    mock_blob.upload_from_string.assert_called_once_with(
        xml_str, content_type="text/xml", timeout=120
    )


def test_upload_csv_to_gcs(
    mock_storage_client, mock_bucket, mock_blob, mock_task_client
):
    gcs_manager = GCSStorageManager()
    csv_str = "header\nvalue"
    file_name = "test.csv"
    partner_name = "partner1"

    gcs_manager.upload_csv_to_gcs(csv_str, file_name, partner_name)

    mock_storage_client.assert_called_once()
    mock_bucket.blob.assert_called_once_with(f"{partner_name}/{file_name}")
    mock_blob.upload_from_string.assert_called_once_with(
        csv_str, content_type="text/csv", timeout=120
    )


def test_upload_tsv_to_gcs(
    mock_storage_client, mock_bucket, mock_blob, mock_task_client
):
    gcs_manager = GCSStorageManager()
    tsv_str = "header\nvalue"
    file_name = "test.tsv"
    partner_name = "partner1"

    gcs_manager.upload_tsv_to_gcs(tsv_str, file_name, partner_name)

    mock_storage_client.assert_called_once()
    mock_bucket.blob.assert_called_once_with(f"{partner_name}/{file_name}")
    mock_blob.upload_from_string.assert_called_once_with(
        tsv_str, content_type="text/tsv", timeout=120
    )
