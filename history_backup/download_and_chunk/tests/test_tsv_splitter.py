import os

import pytest
import io
import csv
import time
from unittest.mock import patch, Mock

from file_handlers.tsv_splitter import TSVSplitter
from storage.gcs_storage_manager import GCSStorageManager
from enums import Partners


@pytest.fixture
def setup_and_teardown():
    # Set up any required resources before each test
    input_gzip_file = os.getcwd() + "/tests/mock_data/test_data.csv.gz"
    batch_size = 10000
    batch_count = 0
    file_timestamp = round(time.time())
    yield input_gzip_file, batch_size, batch_count, file_timestamp


@patch.object(GCSStorageManager, "upload_tsv_to_gcs")
def test_split_data_into_batches(mock_upload, setup_and_teardown):
    input_gzip_file, batch_size, batch_count, file_timestamp = setup_and_teardown
    tsv_splitter = TSVSplitter(partner_name=Partners.EBAY.value, batch_size=batch_size)
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    tsv_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    tsv_splitter.split_data_into_batches(
        input_filepath=input_gzip_file,
    )
    assert mock_upload.call_count == 10


@patch.object(GCSStorageManager, "upload_tsv_to_gcs")
def test_upload_to_cloud_storage(mock_upload, setup_and_teardown):
    input_gzip_file, batch_size, batch_count, file_timestamp = setup_and_teardown
    tsv_splitter = TSVSplitter(partner_name=Partners.EBAY.value, batch_size=batch_size)
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    tsv_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    tsv_splitter._upload_to_cloud_storage(
        batch_data=input_gzip_file,
        record_count=batch_size,
        batch_count=batch_count,
        file_timestamp=file_timestamp,
        session=Mock(),
    )
    mock_upload.assert_called()


@patch.object(GCSStorageManager, "upload_tsv_to_gcs")
def test_batch_content(mock_upload, setup_and_teardown):
    # Test if the batch files contain the correct number of entries
    input_gzip_file, batch_size, batch_count, file_timestamp = setup_and_teardown
    tsv_splitter = TSVSplitter(partner_name=Partners.EBAY.value, batch_size=batch_size)
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    tsv_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    tsv_splitter.set_last_modified_timestamp(1714766400)
    tsv_splitter.split_data_into_batches(
        input_filepath=input_gzip_file,
    )
    # Check if the uploaded TSV strings contain the correct number of records
    for call_args in mock_upload.call_args_list:
        tsv = call_args[0][0]
        tsv_like_object = io.StringIO(tsv)
        reader = csv.reader(tsv_like_object, delimiter="\t")
        record_count = sum(1 for row in reader) - 1
        assert record_count == batch_size

        filename = call_args[0][1]
        assert "1714766400" in filename
