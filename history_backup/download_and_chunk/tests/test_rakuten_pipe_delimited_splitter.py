import os

import pytest
import io
import csv
import time
from unittest.mock import patch, Mock

from file_handlers.rakuten_pipe_delimited_splitter import PipeDelimitedSplitter
from storage.gcs_storage_manager import GCSStorageManager
from enums import Partners


@pytest.fixture
def setup_and_teardown():
    # Set up any required resources before each test
    input_gzip_file = os.getcwd() + "/tests/mock_data/test_data.txt.gz"
    batch_size = 10000
    batch_count = 0
    file_timestamp = round(time.time())
    yield input_gzip_file, batch_size, batch_count, file_timestamp


@patch.object(GCSStorageManager, "upload_csv_to_gcs")
def test_split_data_into_batches(mock_upload, setup_and_teardown):
    input_gzip_file, batch_size, batch_count, file_timestamp = setup_and_teardown
    csv_splitter = PipeDelimitedSplitter(
        partner_name=Partners.SHOP_PREMIUM_OUTLETS.value, batch_size=batch_size
    )
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    csv_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    csv_splitter.split_data_into_batches(
        input_filepath=input_gzip_file,
    )
    assert mock_upload.call_count == 1


@patch.object(GCSStorageManager, "upload_csv_to_gcs")
def test_upload_to_cloud_storage(mock_upload, setup_and_teardown):
    input_gzip_file, batch_size, batch_count, file_timestamp = setup_and_teardown
    csv_splitter = PipeDelimitedSplitter(
        partner_name=Partners.SHOP_PREMIUM_OUTLETS.value, batch_size=batch_size
    )
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    csv_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    csv_splitter._upload_to_cloud_storage(
        batch_data=input_gzip_file,
        record_count=batch_size,
        batch_count=batch_count,
        file_timestamp=file_timestamp,
        session=Mock(),
    )
    assert mock_upload.called is True


@patch.object(GCSStorageManager, "upload_csv_to_gcs")
def test_batch_content(mock_upload, setup_and_teardown):
    # Test if the batch files contain the correct number of entries
    input_gzip_file, batch_size, batch_count, file_timestamp = setup_and_teardown
    csv_splitter = PipeDelimitedSplitter(
        partner_name=Partners.SHOP_PREMIUM_OUTLETS.value, batch_size=batch_size
    )
    create_partner_product_file_sync_mock = Mock()
    create_partner_product_file_sync_mock.return_value = "some_id"
    csv_splitter.create_partner_product_file_sync = (
        create_partner_product_file_sync_mock
    )
    csv_splitter.split_data_into_batches(
        input_filepath=input_gzip_file,
    )
    # Check if the uploaded CSV strings contain the correct number of records
    for call_args in mock_upload.call_args_list:
        csv_str = call_args[0][0]
        csv_file_like_object = io.StringIO(csv_str)
        reader = csv.reader(csv_file_like_object)
        record_count = sum(1 for row in reader) - 1
        assert record_count == 3
