import sys

import functions_framework
import logging
import google.cloud.logging
import time
import os
from concurrent.futures import ProcessPoolExecutor

from sqlalchemy.orm import sessionmaker

from factory import ConfigurationFactory
from utils.database.connector import connect_with_connector
from utils.database.service import (
    create_partner_product_sync,
    update_partner_product_syncs_status,
)


@functions_framework.http
def fetch_partner_data(request):
    """HTTP Cloud Function to fetch data for a given partner."""
    setup_logging()
    request_json = request.get_json(silent=True)
    if request_json and "partner_name" in request_json:
        partner_name = request_json["partner_name"]

        if partner_name in ConfigurationFactory.SUPPORTED_PARTNERS:
            logging.info(f"Fetching data for {partner_name}...")
            try:
                engine = connect_with_connector()
                Session = sessionmaker(bind=engine)
                with Session() as session:
                    partner_product_sync_id = create_partner_product_sync(
                        session, partner_name
                    )
                downloader, splitter = ConfigurationFactory.get_partner_configuration(
                    partner_name
                )
                splitter.partner_product_sync_id = partner_product_sync_id
                # Execute the download-and-chunk flow for the partner
                download_and_chunk(partner_name, downloader, splitter)
                return (
                    f"Data fetched and processed successfully for {partner_name}!",
                    200,
                )
            except Exception as e:
                logging.error(
                    f"Download and chunk failed for {partner_name} with exception: {e}"
                )
                if partner_product_sync_id is not None:
                    with Session() as session:
                        update_partner_product_syncs_status(
                            session, partner_product_sync_id, "FAILED"
                        )
                return f"Error processing data for {partner_name}: {e}", 500
        else:
            return f"Partner {partner_name} is not supported.", 400
    else:
        return "Invalid request. Please provide a partner_name.", 400


def download_and_chunk(partner_name, downloader, splitter):
    start_time = time.time()
    logging.info(
        f"Starting to fetch data for partner: {partner_name}: {time.time() - start_time}"
    )
    file_name = downloader.fetch_data()
    last_modified_timestamp = downloader.get_last_modified_timestamp()
    if last_modified_timestamp is not None:
        logging.info(
            f"Using the last_modified_timestamp {last_modified_timestamp} from the data downloader"
        )
        splitter.set_last_modified_timestamp(last_modified_timestamp)

    logging.info(
        f"Time downloading data from {partner_name}: {time.time() - start_time}"
    )
    if os.path.isdir(file_name):
        with ProcessPoolExecutor(max_workers=10) as executor:
            for file in os.listdir(file_name):
                executor.submit(
                    splitter.split_data_into_batches, os.path.join(file_name, file)
                )
            executor.shutdown(wait=True)
    else:
        splitter.split_data_into_batches(input_filepath=file_name)
    logging.info(
        f"Time to download, chunk and upload for {partner_name}: {time.time() - start_time}"
    )


def setup_logging():
    if os.getenv("ENVIRONMENT") != "local":
        logging.getLogger().setLevel(logging.INFO)
        client = google.cloud.logging.Client()
        client.setup_logging()
    else:
        logging.basicConfig(stream=sys.stdout, level=logging.INFO)
