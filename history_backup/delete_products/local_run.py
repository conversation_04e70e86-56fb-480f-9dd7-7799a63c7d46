import os

from dotenv import load_dotenv

from main import delete_products

dotenv_path = os.path.join(os.path.dirname(__file__), ".env.dev")


def run():
    # Load the environment variables from .env.dev
    load_dotenv(dotenv_path)
    env = os.getenv("ENVIRONMENT")
    print(f"Running on {env}")
    req = {
        "message": {
            "attributes": {
                "partner_name": "grailed",
                "partner_product_sync_id": "cee805ac-aae9-4b68-b67a-31598874c323",
            }
        }
    }
    delete_products(req)


run()
