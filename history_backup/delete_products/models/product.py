import uuid

from sqlalchemy import (
    Index,
    Column,
    String,
    Numeric,
    Boolean,
    DateTime,
    ForeignKey,
    Enum,
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import ENUM
from .base import Base, metadata

# ENUM definitions
color_enum = ENUM(
    "BLACK",
    "GREY",
    "BROWN",
    "RED",
    "BLUE",
    "PURPLE",
    "PIN<PERSON>",
    "ORANGE",
    "WHIT<PERSON>",
    "GREEN",
    "YELLOW",
    "GOLD",
    "SILVER",
    "BEIGE",
    "PRINT",
    name="Color",
    metadata=metadata,
    create_type=True,
)
condition_enum = ENUM(
    "NEW_WITH_TAG",
    "PRISTINE",
    "VERY_GOOD",
    "GOOD",
    name="Condition",
    metadata=metadata,
    create_type=True,
)
gender_enum = ENUM(
    "WOMEN", "MEN", "UNISEX", name="Gender", metadata=metadata, create_type=True
)


class Brand(Base):
    __tablename__ = "Brand"
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    displayName = Column(String)


class Product(Base):
    __tablename__ = "Product"

    # Primary fields
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(256))

    # This may change, we can consider using JSONB for storing complex data, or setup full-text
    # search with tsvector if using PostgreSQL
    description = Column(String, nullable=True)

    priceUsd = Column(
        Numeric(precision=10, scale=2)
    )  # Define precision and scale for money
    productUrl = Column(String)
    imgUrl = Column(String)
    additionalImgUrls = Column(ARRAY(String))  # Use ARRAY for list of strings
    firsthandRetailerId = Column(
        String, ForeignKey("FirsthandRetailer.id"), nullable=True
    )
    secondhandRetailerId = Column(String, ForeignKey("SecondhandRetailer.id"))

    # Filterable properties
    primaryColor = Column(color_enum, nullable=True)
    colors = Column(
        ARRAY(color_enum)
    )  # Consider custom type or JSONB if complex data needed
    condition = Column(condition_enum, nullable=True)
    gender = Column(gender_enum, nullable=True)
    isAuthenticated = Column(Boolean, default=False)
    isFreeShipping = Column(Boolean, default=False)
    isReturnable = Column(Boolean, default=False)
    isVintage = Column(Boolean, default=False)
    sizeId = Column(String, ForeignKey("Size.id"), nullable=True)
    primaryBrandId = Column(String, ForeignKey("Brand.id"), nullable=True)

    # Timestamps
    createdAt = Column(DateTime, default=func.now())
    updatedAt = Column(DateTime, default=func.now(), onupdate=func.now())
    lastSeenAt = Column(DateTime)
    soldAt = Column(DateTime, nullable=True)

    # Raw
    rawBrand = Column(String, nullable=True)
    rawSize = Column(String, nullable=True)

    __table_args__ = (
        Index("idx_product_colors", "colors", postgresql_using="hash"),
        Index("idx_product_condition", "condition", postgresql_using="hash"),
        Index("idx_product_priceUsd", "priceUsd"),
        Index(
            "idx_product_secondhandRetailerId",
            "secondhandRetailerId",
            postgresql_using="hash",
        ),
        Index("idx_product_sizeId", "sizeId", postgresql_using="hash"),
    )
