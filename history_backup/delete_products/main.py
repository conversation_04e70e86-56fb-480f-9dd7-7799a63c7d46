import logging
import os

import functions_framework
import google.cloud.logging
from sqlalchemy.orm import sessionmaker

from schemas.product_schema import ProductSchema
from util.main_db.connector import connect_to_main_db_with_connector
from util.main_db.service import update_sold_at_before_timestamp
from util.pipeline_db.connector import connect_to_pipeline_db_with_connector
from util.pipeline_db.models import Status
from util.pipeline_db.service import (
    update_partner_product_syncs_status,
    get_all_syncs_ingested,
)

schema = ProductSchema()


@functions_framework.http
def delete_products(request):
    """HTTP Cloud Function to fetch data for a given partner."""
    setup_logging()
    try:
        request_json = request.get_json(silent=True)
        logging.info(f"Received request to ingest data chunk {request_json}")
        if (
            request_json
            and "message" in request_json
            and "attributes" in request_json["message"]
        ):
            message = request_json["message"]["attributes"]
            logging.info(f"Received message: {message}")
        else:
            logging.error("Missing message or attributes in the request")
            return "Bad Request", 400

        if (
            message
            and "partner_name" in message
            and "partner_product_sync_id" in message
        ):
            partner_name = message["partner_name"]
            partner_product_sync_id = message["partner_product_sync_id"]
            logging.info(
                f"Received deletion request for partner {partner_name} and product {partner_product_sync_id}"
            )
        else:
            logging.error(
                "Missing partner_name or partner_product_sync_id in the request"
            )
            return "Bad Request", 400

    except Exception as e:
        logging.error(f"Error parsing request data for {request}: {e}")
        return f"Error parsing request data for {request}: {e}", 500

    try:
        datapipeline_engine = connect_to_pipeline_db_with_connector()
        db_session_maker = sessionmaker(bind=datapipeline_engine)

        with db_session_maker() as pipeline_session:
            all_syncs_ingested, created_at = get_all_syncs_ingested(
                pipeline_session, partner_product_sync_id
            )
        if all_syncs_ingested and partner_name == "ebay":
            logging.info(f"All syncs complete for ebay. Skipping deletion step.")
            with db_session_maker() as pipeline_session:
                update_partner_product_syncs_status(
                    pipeline_session, partner_product_sync_id, Status.PROCESSED.value
                )
            return "Deletion step completed successfully", 200
        if all_syncs_ingested:
            logging.info(
                f"Beginning deletion process for {partner_name} and {partner_product_sync_id}"
            )
            engine = connect_to_main_db_with_connector()
            main_db_session_maker = sessionmaker(bind=engine)
            with main_db_session_maker() as session:
                update_sold_at_before_timestamp(session, partner_name, created_at)
            logging.info(
                f"Deletion complete for {partner_name} and {partner_product_sync_id}"
            )
            with db_session_maker() as pipeline_session:
                update_partner_product_syncs_status(
                    pipeline_session, partner_product_sync_id, Status.PROCESSED.value
                )
            return "Deletion step completed successfully", 200

        logging.info(
            f"Deletion step skipped. All syncs not ingested for: {partner_product_sync_id}"
        )
        return (
            f"Deletion step skipped. All syncs not ingested for {partner_product_sync_id}",
            200,
        )
    except Exception as e:
        logging.error(f"Product deletion failed for {partner_product_sync_id}: {e}")
        datapipeline_engine = connect_to_pipeline_db_with_connector()
        db_session_maker = sessionmaker(bind=datapipeline_engine)
        with db_session_maker() as pipeline_session:
            update_partner_product_syncs_status(
                pipeline_session, partner_product_sync_id, Status.FAILED.value
            )
        return f"Product deletion failed for {partner_product_sync_id}: {e}", 500


def setup_logging():
    logging.getLogger().setLevel(logging.INFO)
    if os.getenv("ENVIRONMENT") != "local":
        client = google.cloud.logging.Client()
        client.setup_logging()
