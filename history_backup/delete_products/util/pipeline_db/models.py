import uuid
from enum import Enum

from sqlalchemy import (
    Column,
    String,
    ForeignKey,
    DateTime,
    JSON,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID as UUID_PG
from sqlalchemy.sql.functions import now

Base = declarative_base()


class Status(Enum):
    PENDING = "PENDING"
    PROCESSED = "PROCESSED"
    TRANSFORMING = "TRANSFORMING"
    TRANSFORMED = "TRANSFORMED"
    FAILED_TRANSFORM = "FAILED_TRANSFORM"
    BATCH_PROCESSING = "BATCH_PROCESSING"
    BATCH_PROCESSED = "BATCH_PROCESSED"
    FAILED_BATCH_PROCESS = "FAILED_BATCH_PROCESS"
    INGESTING = "INGESTING"
    INGESTED = "INGESTED"
    FAILED_INGEST = "FAILED_INGEST"
    FAILED = "FAILED"


class PartnerProductSync(Base):
    __tablename__ = "PartnerProductSync"

    id = Column(UUID_PG(as_uuid=True), primary_key=True, default=uuid.uuid4)
    partner = Column(String, nullable=False)
    status = Column(String(20), nullable=False, default="Pending")
    meta = JSON()
    created_at = Column(DateTime, default=now())
    updated_at = Column(DateTime, default=now())


class PartnerProductFileSync(Base):
    __tablename__ = "PartnerProductFileSync"

    id = Column(UUID_PG(as_uuid=True), primary_key=True, default=uuid.uuid4)
    partner_product_sync_id = Column(
        UUID_PG(as_uuid=True), ForeignKey("PartnerProductSync.id"), nullable=False
    )
    file_name = Column(String(255), nullable=False)
    status = Column(String(20), nullable=False, default="Pending")
    partner = Column(String, nullable=False)
    created_at = Column(DateTime, default=now())
    updated_at = Column(DateTime, default=now())


class SynonymSet(Base):
    __tablename__ = "SynonymSet"
    id = Column(UUID_PG(as_uuid=True), primary_key=True, default=uuid.uuid4)
    display_name = Column(String, nullable=True)

    last_modified_by = Column(String, nullable=True)
    created_at = Column(DateTime, default=now())
    updated_at = Column(DateTime, default=now())


class SynonymRule(Base):
    __tablename__ = "SynonymRule"
    id = Column(UUID_PG(as_uuid=True), primary_key=True, default=uuid.uuid4)
    display_name = Column(String, nullable=True)
    value = Column(String)

    synoynm_set_id = Column(String, ForeignKey("SynonymSet.id"), nullable=True)

    last_modified_by = Column(String, nullable=True)
    created_at = Column(DateTime, default=now())
    updated_at = Column(DateTime, default=now())
