import os

import pg8000
import sqlalchemy
from google.cloud.sql.connector import Connector, IPTypes


def connect_to_main_db_with_connector() -> sqlalchemy.engine.base.Engine:
    if os.getenv("ENVIRONMENT") == "local":
        pool = sqlalchemy.create_engine(
            "postgresql+pg8000://chandler:bing@localhost:20432/phia_dev",
        )
    else:
        pool = sqlalchemy.create_engine(
            "postgresql+pg8000://",
            creator=get_google_cloud_creator(),
        )
    return pool


def get_google_cloud_creator():
    main_db_connection_string = os.getenv("MAIN_DB_CONNECTION_STRING")
    db_user = os.getenv("DB_USER")
    db_pass = os.getenv("DB_PASS")
    db_name = os.getenv("DB_NAME", "postgres")

    idx = main_db_connection_string.rindex("/") + 1
    instance_connection_name = main_db_connection_string[idx:]
    # TODO: look into this
    ip_type = IPTypes.PUBLIC

    connector = Connector()

    def getconn() -> pg8000.dbapi.Connection:
        conn = connector.connect(
            instance_connection_name,
            "pg8000",
            user=db_user,
            password=db_pass,
            db=db_name,
            ip_type=ip_type,
        )
        return conn

    return getconn
