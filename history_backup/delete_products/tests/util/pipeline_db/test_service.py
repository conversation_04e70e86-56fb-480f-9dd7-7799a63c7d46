import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker
from testcontainers.postgres import PostgresContainer

from util.pipeline_db.models import (
    PartnerProductSync,
    Base,
    PartnerProductFileSync,
    Status,
)
from util.pipeline_db.service import get_all_syncs_ingested


@pytest.fixture(scope="session")
def db_engine():
    with PostgresContainer(image="postgres:16", driver="pg8000") as postgres:
        connection_url = postgres.get_connection_url().replace(
            "postgresql://", "postgresql+pg8000://"
        )
        engine = create_engine(connection_url)
        Base.metadata.create_all(engine)
        yield engine
        engine.dispose()


@pytest.fixture(scope="function")
def db_session(db_engine) -> Session:
    connection = db_engine.connect()
    transaction = connection.begin()
    session = sessionmaker(bind=connection)()
    try:
        yield session
    finally:
        session.close()
        transaction.rollback()
        connection.close()


def test_get_partner_product_file_syncs_status_by_partner_product_sync_id(db_session):
    new_partner_product_sync = PartnerProductSync(
        partner="rebag",
        status="PROCESSING",
    )
    db_session.add(new_partner_product_sync)
    db_session.commit()

    partner_product_file_sync_one = PartnerProductFileSync(
        partner_product_sync_id=new_partner_product_sync.id,
        file_name="rebag/rebag_one.xml",
        status=Status.INGESTED.value,
        partner="rebag",
    )
    partner_product_file_sync_two = PartnerProductFileSync(
        partner_product_sync_id=new_partner_product_sync.id,
        file_name="rebag/rebag_one.xml",
        status=Status.INGESTED.value,
        partner="rebag",
    )
    partner_product_file_sync_three = PartnerProductFileSync(
        partner_product_sync_id=new_partner_product_sync.id,
        file_name="rebag/rebag_one.xml",
        status=Status.INGESTED.value,
        partner="rebag",
    )
    db_session.add_all(
        [
            partner_product_file_sync_one,
            partner_product_file_sync_two,
            partner_product_file_sync_three,
        ]
    )
    db_session.commit()

    all_syncs_ingested, created_at = get_all_syncs_ingested(
        db_session, new_partner_product_sync.id
    )
    assert all_syncs_ingested


def test_get_partner_product_file_syncs_status_by_partner_product_sync_id(db_session):
    new_partner_product_sync = PartnerProductSync(
        partner="rebag",
        status="PROCESSING",
    )
    db_session.add(new_partner_product_sync)
    db_session.commit()

    partner_product_file_sync_one = PartnerProductFileSync(
        partner_product_sync_id=new_partner_product_sync.id,
        file_name="rebag/rebag_one.xml",
        status=Status.INGESTED.value,
        partner="rebag",
    )
    partner_product_file_sync_two = PartnerProductFileSync(
        partner_product_sync_id=new_partner_product_sync.id,
        file_name="rebag/rebag_one.xml",
        status=Status.INGESTED.value,
        partner="rebag",
    )
    partner_product_file_sync_three = PartnerProductFileSync(
        partner_product_sync_id=new_partner_product_sync.id,
        file_name="rebag/rebag_one.xml",
        status=Status.INGESTING.value,
        partner="rebag",
    )
    db_session.add_all(
        [
            partner_product_file_sync_one,
            partner_product_file_sync_two,
            partner_product_file_sync_three,
        ]
    )
    db_session.commit()

    all_syncs_ingested, created_at = get_all_syncs_ingested(
        db_session, new_partner_product_sync.id
    )
    assert not all_syncs_ingested
