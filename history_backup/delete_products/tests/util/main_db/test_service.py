from datetime import datetime, timedelta

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.sql.functions import now
from testcontainers.postgres import PostgresContainer

from models import Base, Product, SecondhandRetailer
from util.main_db.service import update_sold_at_before_timestamp


@pytest.fixture(scope="session")
def db_engine():
    with PostgresContainer(image="postgres:16", driver="pg8000") as postgres:
        connection_url = postgres.get_connection_url().replace(
            "postgresql://", "postgresql+pg8000://"
        )
        engine = create_engine(connection_url)
        Base.metadata.create_all(engine)
        yield engine
        engine.dispose()


@pytest.fixture(scope="function")
def db_session(db_engine) -> Session:
    connection = db_engine.connect()
    transaction = connection.begin()
    session = sessionmaker(bind=connection)()
    try:
        yield session
    finally:
        session.close()
        transaction.rollback()
        connection.close()


@pytest.fixture(scope="function")
def setup_retailers(db_session):
    rebag = SecondhandRetailer(id="ebay", displayName="Ebay")
    db_session.add(rebag)
    db_session.commit()

    ebay = SecondhandRetailer(id="rebag", displayName="Rebag")
    db_session.add(ebay)
    db_session.commit()
    return rebag, ebay


@pytest.fixture(scope="function")
def setup_products(db_session):
    new_product_one = Product(
        name="Test Product",
        description="Description",
        priceUsd=100.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        secondhandRetailerId="rebag",
        lastSeenAt=datetime.now() - timedelta(hours=1),
    )
    new_product_two = Product(
        name="Test Product",
        description="Description",
        priceUsd=100.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        secondhandRetailerId="rebag",
        lastSeenAt=datetime.now() - timedelta(hours=1),
    )
    new_product_three = Product(
        name="Test Product",
        description="Description",
        priceUsd=100.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        secondhandRetailerId="rebag",
        lastSeenAt=datetime.now() - timedelta(hours=3),
    )
    new_product_four = Product(
        name="Test Product",
        description="Description",
        priceUsd=100.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        secondhandRetailerId="rebag",
        lastSeenAt=datetime.now() - timedelta(minutes=5),
    )
    db_session.add_all(
        [new_product_one, new_product_two, new_product_three, new_product_four]
    )
    db_session.commit()
    return new_product_one, new_product_two, new_product_three, new_product_four


def test_update_sold_at_before_timestamp(db_session, setup_retailers, setup_products):
    new_product_one, new_product_two, new_product_three, new_product_four = (
        setup_products
    )

    partner_product_sync_created_at = datetime.now() - timedelta(hours=2)
    update_sold_at_before_timestamp(
        db_session, "rebag", partner_product_sync_created_at
    )

    assert (
        get_product_by_id(db_session, new_product_three.id).soldAt
        == partner_product_sync_created_at
    )
    # We do not update a product with an existing soldAt date
    assert (
        get_product_by_id(db_session, new_product_four.id).soldAt
        == new_product_four.soldAt
    )
    assert get_product_by_id(db_session, new_product_one.id).soldAt is None
    assert get_product_by_id(db_session, new_product_two.id).soldAt is None


def test_update_sold_at_with_different_partner(
    db_session, setup_retailers, setup_products
):
    new_product_one, new_product_two, new_product_three, new_product_four = (
        setup_products
    )

    partner_product_sync_created_at = datetime.now() - timedelta(hours=2)
    update_sold_at_before_timestamp(db_session, "ebay", partner_product_sync_created_at)

    assert get_product_by_id(db_session, new_product_three.id).soldAt is None
    assert get_product_by_id(db_session, new_product_one.id).soldAt is None
    assert get_product_by_id(db_session, new_product_two.id).soldAt is None


def get_product_by_id(db_session, product_id):
    return db_session.query(Product).filter(Product.id == product_id).one()
