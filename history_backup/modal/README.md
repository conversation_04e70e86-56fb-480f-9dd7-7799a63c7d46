# Image Embedding Modal Endpoint

This repository contains the logic for the modal endpoint used for image embedding. The primary function of this endpoint is to embed images and insert them into the `ImageEmbedding` table.

The `ImageEmbedding` table stores the embedded images, which are used in various parts of the application. The modal endpoint facilitates this process by providing an interface for embedding and storing images.

This document provides instructions on how to use the `embed_images` endpoint, which takes a list of `ImageModel` objects, embeds the images using the "patrickjohncyh/fashion-clip" model, and returns a list of the IDs along with their embeddings.

## Input Format
The endpoint expects a POST request with a JSON body containing a list of `ImageModel` objects. Each `ImageModel` should have the following structure:
```json
{
  "id": "string",
  "imgUrl": "string"
}
```

## Output Format
The response will be a JSON list where each element is an object containing the `id` of the image and its corresponding `embedding`:
```json
[
  {
    "id": "string",
    "embedding": [float]
  }
]
```

## Setup Instructions
1. Install all necessary packages in your local environment using `pip3 install -r requirements.txt`
2. Ensure that the Modal Python package is installed and authenticated.
3. Place the `embed_images.py` script in your working directory.

## Invocation
To deploy the endpoint, use the Modal platform's `modal deploy` command to deploy a persistent web endpoint:
```bash
modal deploy embed.py
```

## Example Usage
Here's an example of how to call the `embed_images` endpoint with a sample image:
```python
import requests

ENDPOINT_URL = "https://phiadev--image-embedding-service-main.modal.run"

# Sample image data
images_data = {
  "images": [
    {
      "id": "1",
      "imgUrl": "http://example.com/image1.png"
    },
    {
      "id": "2",
      "imgUrl": "http://example.com/image2.png"
    }
  ]
}

# POST request to the endpoint
response = requests.post(ENDPOINT_URL, json=images_data)

# API key for authentication
API_KEY = "<API KEY GOES HERE>"

# POST request to the endpoint with the API key
response = requests.post(
    ENDPOINT_URL,
    headers={"x-api-key": API_KEY},
    json=images_data
)

# Print out the response
print(response.json())
```

## Secrets

The secrets used in this app are all stored in modal. You can view them in the dashboard there.


