from PIL import Image as PILImage
from io import BytesIO
import asyncio
import aiohttp


async def download_image(session, url, image_id):
    try:
        # Temporary hack to get around Vestiaire Collective's bot detection. We should move this to
        # a secret and handle more elegantly.
        headers = (
            {
                "Authorization": "Bearer RUHGyIuzm6CYcs00QQ7PFJ4cLHXrNRoo",
                "User-Agent": "Phia Crawler",
            }
            if "vestiairecollective" in url
            else {}
        )
        async with session.get(url, headers=headers) as response:
            if response.status == 200:
                # TODO: add in header for Vestiaire collective downloads
                image_data = await response.read()
                image = PILImage.open(BytesIO(image_data)).convert("RGB")
                print(f"Successfully downloaded image from {url}")
                return (image, image_id)
            else:
                print(
                    f"Failed to download image from {url}: Status code {response.status}"
                )
    except Exception as e:
        print(f"Exception occurred while downloading image from {url}: {e}")
    return (None, image_id)


async def download_images_async(image_urls, image_ids):
    try:
        print(f"Attempting to download {len(image_urls)} images.")
        async with aiohttp.ClientSession() as session:
            tasks = [
                download_image(session, url, id)
                for url, id in zip(image_urls, image_ids)
            ]
            print("Starting download of images.")
            images_and_ids = await asyncio.gather(*tasks)
            successful_images_and_ids = [
                (image, id) for image, id in images_and_ids if image is not None
            ]

            failed_ids = [id for image, id in images_and_ids if image is None]
            print(f"Downloaded and filtered {len(images_and_ids)} images.")
            return successful_images_and_ids, failed_ids
    except Exception as e:
        print(f"Exception occurred while downloading images asynchronously: {e}")
        return []
