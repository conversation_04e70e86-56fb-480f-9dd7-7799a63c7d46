import asyncio
from embed import process_embeddings
from utils import download_images_async


async def run():
    await process_embeddings()


"""
This method can be used to run the modal app asynchronously. Note
that it is necessary to comment out the @app.function annotation
on the process_embeddings function as this is modal specific and
will throw an exception if used in this context.  
"""


asyncio.run(run())
