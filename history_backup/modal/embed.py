import asyncio
from typing import Dict
from modal import App, web_endpoint, Image, Secret

import numpy as np
import pandas as pd

from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer
from fastapi import HTTPException, status, Request
import os
import modal
import time

from sqlalchemy.orm import sessionmaker

from database import connect_with_connector, Embedding


def download_model():
    from huggingface_hub import snapshot_download

    snapshot_download(repo_id="patrickjohncyh/fashion-clip", cache_dir="/cache")


# Define a custom image with the necessary Python packages
custom_image = (
    Image.debian_slim(python_version="3.8")
    .pip_install(
        "transformers",
        "torch",
        "requests",
        "Pillow",
        "pandas",
        "numpy",
        "huggingface_hub",
        "psycopg2-binary",
        "sqlalchemy",
        "pgvector",
        "aiohttp",
        "asyncio",
        "cuid",
        "google-auth",
    )
    .run_function(download_model)
)

app = App(name="image-embedding-service")

auth_scheme = HTTPBearer()


@app.function(
    image=custom_image,
    secrets=[Secret.from_name("api-key"), Secret.from_name("main-db-secrets")],
    mounts=[
        modal.Mount.from_local_python_packages("clip_embedder", "utils", "database"),
    ],
)
@web_endpoint(method="POST")
async def main(images: Dict, request: Request):

    # Access the API key from secrets and perform necessary checks if needed
    received_api_key = request.headers.get("X-API-Key")
    if received_api_key != os.getenv("api_key"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "API Key"},
        )

    call = process_embeddings.spawn()
    return {"call_id": call.object_id}


@app.function(
    schedule=modal.Cron("0 * * * *"),
    timeout=7200,
    image=custom_image,
    secrets=[Secret.from_name("main-db-secrets")],
    mounts=[
        modal.Mount.from_local_python_packages("database"),
    ],
)
async def process_embeddings():
    from database import (
        bulk_update_embeddings,
        connect_with_connector,
        get_embeddings_by_status_and_update,
    )

    # Convert list of dictionaries to DataFrame for batch processing
    engine = connect_with_connector()
    Session = sessionmaker(bind=engine)

    start = time.time()
    with Session() as session:
        """
        Query the database for embeddings with a status of pending. Update those records' status
        to processing and return the entries.
        """
        images_pending_embedding = get_embeddings_by_status_and_update(session)

    print(
        f"Completed Embedding query for {len(images_pending_embedding)} records in: {time.time() - start} seconds"
    )
    if len(images_pending_embedding) == 0:
        print("No images pending embedding found")
        return

    images_df = pd.DataFrame(
        [
            {"id": embedding.id, "imgUrl": embedding.imageUrl}
            for embedding in images_pending_embedding
        ]
    )

    print(f"Starting batch processing for {len(images_df)} images.")
    num_batches = max(1, len(images_df) // 20)

    batches = np.array_split(images_df, num_batches)

    flat_embedded_images = []

    start = time.time()
    async for embedding_list in embed_images_wrapper.map.aio(batches):
        for embedding in embedding_list:
            flat_embedded_images.append(embedding)

    print(
        f"Completed embedding of {num_batches} batches in: {time.time() - start} seconds"
    )

    failed = 0
    for embedding in flat_embedded_images:
        if embedding.get("status") == "FAILED":
            failed += 1

    print(f"Preparing to insert {len(flat_embedded_images)} embedding records.")
    print(f"{len(flat_embedded_images) - failed} embeddings were successful.")
    print(f"{failed} embeddings failed.")
    start = time.time()
    with Session() as session:
        bulk_update_embeddings(session, flat_embedded_images)

    # BATCH_SIZE = 50000
    # batches = [
    #     flat_embedded_images[i : i + BATCH_SIZE]
    #     for i in range(0, len(flat_embedded_images), BATCH_SIZE)
    # ]
    # async for a in update_batch.map.aio(batches):
    #     print(a)
    print(f"Completed updating Embedding table in: {time.time() - start} seconds")
    print(f"Batch processing completed.")
    return flat_embedded_images


@app.function(
    timeout=7200,
    image=custom_image,
    secrets=[Secret.from_name("main-db-secrets")],
    mounts=[
        modal.Mount.from_local_python_packages("database"),
    ],
)
async def update_batch(batch):
    engine = connect_with_connector()
    Session = sessionmaker(bind=engine)
    with Session() as session:
        session.bulk_update_mappings(Embedding, batch)
        session.commit()
    return f"Updated {len(batch)} images in batch."


@app.function(
    timeout=3600,
    image=custom_image,
    mounts=[
        modal.Mount.from_local_python_packages("clip_embedder", "utils"),
    ],
)
async def embed_images_wrapper(batch):
    from utils import download_images_async
    from clip_embedder import ClipEmbedder

    embedder = ClipEmbedder(pretrained_name="patrickjohncyh/fashion-clip")

    image_ids = batch["id"].tolist()
    image_urls = batch["imgUrl"].tolist()

    print(f"Processing batch with {len(image_urls)} images.")
    embedded_images = []
    images_and_ids, failed_ids = await download_images_async(image_urls, image_ids)

    for image_id in failed_ids:
        embedded_image = {
            "id": image_id,
            "status": "FAILED",
        }
        embedded_images.append(embedded_image)

    if images_and_ids:
        images, valid_ids = zip(*images_and_ids) if images_and_ids else ([], [])
        print(f"Downloaded {len(images)} images for embedding.")

        embeddings_list = embed_images(embedder, images)
        if not embeddings_list:
            print("No images to embed for this batch.")
            return
        for image_id, embedding, img_url in zip(valid_ids, embeddings_list, image_urls):
            embedded_image = {
                "id": image_id,
                "imageUrl": img_url,
                "vector": embedding,
                "status": "SUCCEEDED",
            }
            embedded_images.append(embedded_image)
    return embedded_images


def embed_images(embedder, images):
    """Embed images and return their embeddings."""
    if not images:
        return [], []
    inputs = embedder.processor(images=images, return_tensors="pt", padding=True).to(
        embedder.device
    )
    image_features = embedder._embed_images(inputs)
    return image_features.detach().cpu().numpy().tolist()
