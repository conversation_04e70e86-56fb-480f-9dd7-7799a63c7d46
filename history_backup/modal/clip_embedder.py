import torch
from transformers import AutoProcessor, AutoModelForZeroShotImageClassification
from typing import List, Optional, Tuple, Any
import numpy as np


class ClipEmbedder(torch.nn.Module):
    def __init__(
        self,
        pretrained_name: str,
        cache_dir: str = "/cache",
    ) -> None:
        super().__init__()
        self.processor = AutoProcessor.from_pretrained(
            pretrained_name,
            cache_dir=cache_dir,
        )
        self.model = AutoModelForZeroShotImageClassification.from_pretrained(
            pretrained_name,
            cache_dir=cache_dir,
        )
        if torch.cuda.is_available():
            self.cuda()
        self.device = self.model.device
        print(f"placed on device {self.device}")

    def embed(
        self,
        images: Optional[List[np.ndarray]],
        texts: Optional[str],
        inputs: Optional[Any] = None,
        normalize: bool = False,
    ) -> Tuple[Optional[torch.Tensor], Optional[torch.Tensor]]:
        if inputs is None:
            inputs = self.processor(
                text=texts, images=images, return_tensors="pt", padding=True
            )
        inputs.to(self.device)

        image_features = self._embed_images(inputs, normalize)
        text_features = self._embed_text(inputs, normalize)
        return image_features, text_features

    def _embed_images(self, inputs, normalize: bool = False) -> torch.Tensor:
        if "pixel_values" in inputs:
            with torch.no_grad():
                image_features = self.model.get_image_features(
                    pixel_values=inputs["pixel_values"]
                )
            if normalize:
                image_features /= image_features.norm(dim=-1, keepdim=True)
            return image_features

    def _embed_text(self, inputs, normalize: bool = False) -> torch.Tensor:
        if "input_ids" in inputs:
            if inputs["input_ids"].shape[-1] > 77:
                inputs["input_ids"] = inputs["input_ids"][:, :77]
                inputs["attention_mask"] = inputs["attention_mask"][:, :77]
            with torch.no_grad():
                text_features = self.model.get_text_features(
                    input_ids=inputs["input_ids"],
                    attention_mask=inputs["attention_mask"],
                )
            if normalize:
                text_features /= text_features.norm(dim=-1, keepdim=True)
            return text_features
