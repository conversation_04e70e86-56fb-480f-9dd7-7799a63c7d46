import enum

import time

import sqlalchemy
import os
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import SQLAlchemyError

import cuid

from sqlalchemy import Column, String, Enum, text, URL
from pgvector.sqlalchemy import Vector

from sqlalchemy.orm import DeclarativeBase

EMBEDDING_QUERY_LIMIT = 1000


class Base(DeclarativeBase):
    pass


class EmbeddingStatus(enum.Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    SUCCEEDED = "SUCCEEDED"
    FAILED = "FAILED"


class Embedding(Base):
    __tablename__ = "Embedding"
    id = Column(String, primary_key=True, default=cuid.cuid)
    imageUrl = Column(String, nullable=True)
    vector = Column(Vector(512), nullable=True)
    productId = Column(String)
    status = Column(Enum(EmbeddingStatus), nullable=True)


def connect_with_connector() -> sqlalchemy.engine.base.Engine:

    db_user = os.getenv("MODAL_MAIN_DB_USER")
    db_pass = os.getenv("MODAL_MAIN_DB_PASS")
    db_name = os.getenv("DB_NAME")
    db_host = os.getenv("DB_HOST")

    url_object = URL.create(
        "postgresql+psycopg2",
        username=db_user,
        password=db_pass,
        host=db_host,
        database=db_name,
    )
    """
    When using the pg8000 postgres driver and the google.cloud.sql.connector, 
    doing a select and update of 5000 records often took ~10 minutes regardless 
    of whether we used sqlalchemy's bulk_update_mapping method or raw sql. 
    Since we update twice during a run, this would mean the total 
    processing time took over 20minutes even though the actual embedding 
    part was only a few minutes. We now only use psycopg2 
    which allows us to configure executemany_mode and executemany_batch_page_size on the engine. 
    This seems to have greatly improved performance as the queries now complete in a few seconds. 
    pg8000 doesn't support these params and I couldn't find something analogous. 
    I also removed the google.cloud.sql.connector, which I think is actually 
    the main bottleneck because using pg8000 directly took ~90 seconds to update 5000 records.
    """
    return sqlalchemy.create_engine(
        url_object,
        executemany_mode="values_plus_batch",
        executemany_batch_page_size=5000,
    )


def bulk_insert_embeddings(session, embeddings):
    insert_stmt = insert(Embedding).values(embeddings)
    try:
        result = session.execute(insert_stmt)
        session.commit()
        rowcount = result.rowcount
        print(f"Inserted {rowcount} embeddings")
        return rowcount
    except SQLAlchemyError as e:
        session.rollback()
        print(f"Database operation failed: {str(e)}")
        raise


def bulk_update_embeddings(session, embeddings):
    session.bulk_update_mappings(Embedding, embeddings)
    session.commit()


def get_embeddings_by_status_and_update(
    session, query_status="PENDING", new_status="PROCESSING"
):
    embeddings_to_update = (
        session.query(Embedding)
        .filter(Embedding.status == query_status)
        .limit(EMBEDDING_QUERY_LIMIT)
        .all()
    )
    embedding_ids = [
        {"id": embedding.id, "status": new_status} for embedding in embeddings_to_update
    ]

    # Update the status of the tasks
    for embedding_to_update in embeddings_to_update:
        embedding_to_update.status = new_status

    start = time.time()
    session.bulk_update_mappings(Embedding, embedding_ids)
    session.commit()
    print(
        f"Selecting and updating records to {new_status} from Embedding table took {time.time() - start} seconds"
    )
    return (
        session.query(Embedding)
        .filter(Embedding.id.in_([e["id"] for e in embedding_ids]))
        .all()
    )
