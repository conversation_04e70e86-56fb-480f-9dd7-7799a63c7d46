import json
import logging
import os

from google.cloud import tasks_v2


class GCSTaskManager:
    def __init__(self):
        self.task_client = tasks_v2.CloudTasksClient()
        self.project_id = os.getenv("PROJECT_ID")
        self.location = os.getenv("LOCATION")
        self.queue = os.getenv("DOWNSTREAM_TASK_QUEUE")
        self.task_url = os.getenv("DOWNSTREAM_TASK_URI")
        self.service_account_email = os.getenv("SERVICE_ACCOUNT_EMAIL")

    def create_task(self, contentful_page_id: str):
        try:

            parent = self.task_client.queue_path(
                self.project_id, self.location, self.queue
            )

            task = tasks_v2.Task(
                http_request={
                    "http_method": "POST",
                    "url": self.task_url,
                    "headers": {"Content-Type": "application/json"},
                    "oidc_token": {
                        "service_account_email": self.service_account_email,
                    },
                    "body": json.dumps(
                        {"contentful_page_id": contentful_page_id}
                    ).encode(),
                },
                dispatch_deadline={"seconds": 1800},
            )

            resp = self.task_client.create_task(
                tasks_v2.CreateTaskRequest(
                    {
                        "parent": parent,
                        "task": task,
                    }
                )
            )
            logging.info(f"Task sent to {self.queue}: {resp}")
        except Exception as e:
            logging.error(f"Failed to create task: {e}")
            raise e
