import logging
import os
import sys

from python_graphql_client import GraphqlClient

import functions_framework
import google.cloud.logging

from content_pages_query import QUERY
from gcs_task_manager import GCSTaskManager

ALLOWED_REPLEN_PAGE_IDS = [
    "5JrCGV0PJ9Cswq0sRKG83r",  # <PERSON>
    "38cZ25ZEF5ZcufOy2CJlQD",  # Coach
    "7nMt7rFxCurnhft4AwdefD",  # <PERSON><PERSON><PERSON>
    "2uMPIW1nZ8r58uRzJ2mWEO",  # Rag & <PERSON>
    "7eb5UdtwmbxVbMpCwki7ZU",  # <PERSON>
    "1hC9lEGTm5LKVg9pAtA0Hd",  # Acne Studios
    "4g4xfzzRS3btl97csH7YVc",  # Celine
    "7xe2mCQe7ktDGY20kd3nTw",  # Chanel
    "4ak4h58j3PfzBRdTHDS0X",  # Favorite Daughter
    "iBWkvGQSekTpkflFgHFdz",  # Mejuri
]


@functions_framework.http
def start_replenishment_job(request):
    return pull_pages()


def pull_pages():
    try:
        setup_logging()
        logging.info(f"Starting replenishment job")
        token = os.getenv("CONTENTFUL_GRAPHQL_API_KEY")
        client = GraphqlClient(
            endpoint="https://graphql.contentful.com/content/v1/spaces/jh5y1lmiedby",
            headers={"Authorization": f"Bearer {token}"},
        )
        resp = client.execute(query=QUERY)
        data = resp["data"]
        category_page_collections = data.get("categoryPageCollection", {})
        partner_page_collections = data.get("partnerPageCollection", {})
        brand_page_collections = data.get("brandPageCollection", {})
        editorial_page_collections = data.get("editorialPageCollection", {})

        task_manager = GCSTaskManager()
        items = (
            category_page_collections.get("items", [])
            + partner_page_collections.get("items", [])
            + brand_page_collections.get("items", [])
            + editorial_page_collections.get("items", [])
        )

        pages_with_products = []
        for item in items:
            if item.get("products") is not None:
                if (
                    item.get("products").get("products") is not None
                    and len(item.get("products").get("products")) > 0
                ):
                    pages_with_products.append(item)
                    contentful_page_id = item.get("sys").get("id")
                    task_manager.create_task(contentful_page_id)

        logging.info(
            f"Replenishment puller completed successfully for {len(pages_with_products)} pages"
        )
        return "Replenishment puller completed successfully", 200
    except Exception as e:
        logging.info(f"Replenishment puller failed {e}")
        return f"Replenishment puller failed {e}", 500


def setup_logging():
    if os.getenv("ENVIRONMENT") != "local":
        logging.getLogger().setLevel(logging.INFO)
        client = google.cloud.logging.Client()
        client.setup_logging()
    else:
        logging.basicConfig(stream=sys.stdout, level=logging.INFO)
