QUERY = """
 query getPages {
    categoryPageCollection(preview:true) {
      items {
        sys {
            id
            publishedVersion
        }
        __typename
        slug
        heading
        subheading
        products
        metaDescription
      }
    }
    partnerPageCollection {
      items {
        __typename
        sys {
            id
            publishedVersion
        }
        slug
        partnerId
        heading
        subheading
        products
        metaDescription
        imagesCollection(limit: 5) {
          items {
            url
          }
        }
      }
    }
    brandPageCollection {
      items {
        __typename
        sys {
            id
            publishedVersion
        }
        slug
        metaDescription
        brandId
        heading
        subheading
        backgroundColor
        products
        logo {
          url
        }
        imagesCollection(limit: 5) {
          items {
            url
          }
        }
      }
    }
   editorialPageCollection(preview:true) {
      items {
        sys {
            id
            publishedVersion
        }
        __typename
        slug
        heroStyle
        image {
          url
        }
        bgImage {
          url
        }
        eyebrow
        heading
        author
        date
        description
        backgroundColor
        products
        textAlignment
        textColor
        metaDescription
      }
    }
  }
"""
