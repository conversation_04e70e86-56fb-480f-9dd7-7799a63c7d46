import logging
import multiprocessing
import os
import time
from concurrent.futures import Thread<PERSON><PERSON>Executor, as_completed
from datetime import datetime, timedelta

import google.cloud.logging
from dotenv import load_dotenv
from psycopg2.extras import RealDictCursor, execute_batch
from psycopg2.pool import ThreadedConnectionPool

DATABASE_CONFIG = {
    "host": "***********",
    "port": "5432",
    "dbname": "postgres",
    "user": "ingest_data_user",
    "password": "",
}
RETAILERS = [
    "bopf",
    "grailed",
    "etsy",
    "farfetch",
    "stockx",
    "the_real_real",
    "vestiaire_collective",
    "the_luxury_closet",
    "luxe_collective",
    "hewi",
    "rebag",
]

NUM_WORKERS = multiprocessing.cpu_count() * 2
BATCH_SIZE = 5000  # Adjust this based on your performance tests
min_conn = 5
max_conn = NUM_WORKERS * 2
conn_pool = ThreadedConnectionPool(minconn=5, maxconn=max_conn, **DATABASE_CONFIG)


# nohup python3 product_embedding_sync.py > product_embedding_sync.log 2>&1 &
# Fetch products by secondhandRetailerId using keyset pagination
def fetch_products_by_retailer(retailer_id, last_updated_at=None, last_product_id=None):
    conn = conn_pool.getconn()
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            query = """
                DECLARE cur CURSOR FOR
                SELECT * FROM "public"."Product" p
                WHERE p."secondhandRetailerId" = %s
                AND p."updatedAt" > %s
                ORDER BY p."updatedAt", p."id"
            """
            cursor.execute(query, (retailer_id, last_updated_at))
            products = cursor.fetchall()
        return products
    finally:
        conn_pool.putconn(conn)


# Main function to sync product embeddings using keyset pagination
def sync_product_embeddings_by_retailer(retailer, last_updated_at):
    logging.info(f"Syncing retailer {retailer} with last updated at {last_updated_at}")
    get_products_conn = conn_pool.getconn()
    try:
        with get_products_conn.cursor(cursor_factory=RealDictCursor) as cursor:
            start_time = time.time()
            query = """
                DECLARE cur CURSOR FOR
                SELECT * FROM "public"."Product" p
                WHERE p."secondhandRetailerId" = %s
                AND p."updatedAt" > %s
                ORDER BY p."updatedAt", p."id"
            """
            cursor.execute(query, (retailer, last_updated_at))
            while True:
                # products = fetch_products_by_retailer(
                #     retailer, last_updated_at, last_product_id
                # )
                try:
                    batch_start_time = time.time()
                    cursor.execute(f"FETCH FORWARD {BATCH_SIZE} FROM cur")
                    products = cursor.fetchall()
                    logging.info(
                        f"Fetched {len(products)} products for {retailer} in {time.time() - batch_start_time:.2f} seconds"
                    )
                    if not products:
                        logging.warning(
                            f"Completed processing of products for {retailer} in {time.time() - start_time:.2f} seconds"
                        )
                        break

                    products_to_delete = []
                    products_to_upsert = []

                    product_embeddings = get_product_embeddings(products, retailer)
                    for product in product_embeddings:
                        if product["soldAt"]:
                            products_to_delete.append(product)
                        else:
                            products_to_upsert.append(product)

                    if products_to_delete:
                        delete_sold_products(products_to_delete, retailer)

                    if products_to_upsert:
                        upsert_product_embeddings(products_to_upsert, retailer)

                    last_updated_at = products[-1]["updatedAt"]
                    last_product_id = products[-1]["id"]
                    logging.info(
                        f"Updating last_updated_at to: {last_updated_at} and "
                        f"last_product_id: {last_product_id} for retailer: {retailer}"
                    )
                except Exception as e:
                    logging.error(
                        f"Exception processing sync for retailer {retailer}: {e}"
                    )
                    raise e
    finally:
        conn_pool.putconn(get_products_conn)


def get_product_embeddings(products, retailer):
    product_ids = [p["id"] for p in products]
    conn = conn_pool.getconn()
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            start_time = time.time()
            logging.info(
                f"Fetching {len(product_ids)} product embeddings for retailer {retailer}"
            )
            query = """
                SELECT id as "embeddingId", "productId", "imageUrl" as "embeddingImageUrl", vector, "createdAt" as "embeddingCreatedAt" FROM "public"."Embedding" WHERE "productId" = ANY(%s)
            """
            # Execute the query and pass the list of product_ids as a tuple
            cursor.execute(query, (product_ids,))
            embeddings = cursor.fetchall()
            logging.info(
                f"Fetched {len(embeddings)} embeddings in {time.time() - start_time:.2f} seconds for retailer {retailer}"
            )
            product_dict = {product["id"]: product for product in products}
            product_embeddings = []

            for embedding in embeddings:
                product_id = embedding["productId"]
                if product_id in product_dict:
                    combined_entry = {**product_dict[product_id], **embedding}
                    product_embeddings.append(combined_entry)
        return product_embeddings
    except Exception as e:
        logging.error(f"Error getting embeddings for {retailer}")
        logging.exception(e)
    finally:
        conn_pool.putconn(conn)


def upsert_product_embeddings(product_embeddings, retailer):
    query = """
        INSERT INTO "public"."product_embedding_table" (
            id, name, description, "priceUsd", "productUrl", "imgUrl",
            "additionalImgUrls", "secondhandRetailerId", colors, condition,
            gender, "isAuthenticated", "isFreeShipping", "isReturnable",
            "isVintage", "sizeId", "createdAt", "updatedAt", "lastSeenAt",
            "soldAt", "primaryBrandId", "primaryColor", "imageQuality",
            "embeddingId", "embeddingImageUrl", vector, "embeddingCreatedAt"
        ) VALUES (
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s
        )
        ON CONFLICT ("embeddingId")
        DO UPDATE SET
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            "priceUsd" = EXCLUDED."priceUsd",
            "productUrl" = EXCLUDED."productUrl",
            "imgUrl" = EXCLUDED."imgUrl",
            "additionalImgUrls" = EXCLUDED."additionalImgUrls",
            "secondhandRetailerId" = EXCLUDED."secondhandRetailerId",
            colors = EXCLUDED.colors,
            condition = EXCLUDED.condition,
            gender = EXCLUDED.gender,
            "isAuthenticated" = EXCLUDED."isAuthenticated",
            "isFreeShipping" = EXCLUDED."isFreeShipping",
            "isReturnable" = EXCLUDED."isReturnable",
            "isVintage" = EXCLUDED."isVintage",
            "sizeId" = EXCLUDED."sizeId",
            "createdAt" = EXCLUDED."createdAt",
            "updatedAt" = EXCLUDED."updatedAt",
            "lastSeenAt" = EXCLUDED."lastSeenAt",
            "soldAt" = EXCLUDED."soldAt",
            "primaryBrandId" = EXCLUDED."primaryBrandId",
            "primaryColor" = EXCLUDED."primaryColor",
            "imageQuality" = EXCLUDED."imageQuality",
            "embeddingImageUrl" = EXCLUDED."embeddingImageUrl",
            vector = EXCLUDED.vector,
            "embeddingCreatedAt" = EXCLUDED."embeddingCreatedAt"
    """

    # Prepare the data as a list of tuples
    data = [
        (
            pe["id"],
            pe["name"],
            pe["description"],
            pe["priceUsd"],
            pe["productUrl"],
            pe["imgUrl"],
            pe["additionalImgUrls"],
            pe["secondhandRetailerId"],
            pe["colors"],
            pe["condition"],
            pe["gender"],
            pe["isAuthenticated"],
            pe["isFreeShipping"],
            pe["isReturnable"],
            pe["isVintage"],
            pe["sizeId"],
            pe["createdAt"],
            pe["updatedAt"],
            pe["lastSeenAt"],
            pe["soldAt"],
            pe["primaryBrandId"],
            pe["primaryColor"],
            pe["imageQuality"],
            pe["embeddingId"],
            pe["embeddingImageUrl"],
            pe["vector"],
            pe["embeddingCreatedAt"],
        )
        for pe in product_embeddings
    ]

    conn = conn_pool.getconn()
    try:
        # Execute the batch upsert
        with conn.cursor() as cursor:
            start_time = time.time()
            logging.info(f"Starting upsert for {len(data)} for retailer {retailer}")
            execute_batch(cursor, query, data, page_size=len(data))
            conn.commit()
            logging.info(
                f"Upserted {len(data)} product_embeddings in {time.time() - start_time:.2f} for {retailer}"
                f" seconds for retailer {retailer}"
            )
    except Exception as e:
        logging.error(f"Error performing upsert for {retailer}")
        logging.exception(e)
    finally:
        conn_pool.putconn(conn)


def delete_sold_products(products, retailer):
    embedding_ids = [p["embeddingId"] for p in products]
    logging.info(f"Starting delete for retailer {retailer}")
    start_time = time.time()
    conn = conn_pool.getconn()
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            delete_query = """
                DELETE FROM "public"."product_embedding_table" WHERE "embeddingId" = ANY(%s)
                """
            cursor.execute(delete_query, (embedding_ids,))
            conn.commit()
            logging.info(
                f"Deleted {len(products)} product_embeddings in {time.time() - start_time:.2f}"
                f" seconds for retailer {retailer}"
            )
            logging.info(
                f"Deleted products for {retailer}. Product Embedding IDs: {embedding_ids}"
            )
    except Exception as e:
        logging.error(f"Error performing delete for {retailer}")
        logging.exception(e)
    finally:
        conn_pool.putconn(conn)


def main():
    last_updated_at = datetime.utcnow() - timedelta(days=5)
    with ThreadPoolExecutor(max_workers=len(RETAILERS)) as executor:
        futures = [
            executor.submit(
                sync_product_embeddings_by_retailer, retailer, last_updated_at
            )
            for retailer in RETAILERS
        ]
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                logging.error(f"Error in thread: {e}")


if __name__ == "__main__":
    dotenv_path = os.path.join(os.path.dirname(__file__), ".env.dev")
    load_dotenv(dotenv_path)
    s = time.time()
    env = os.getenv("ENVIRONMENT")
    try:
        logging.basicConfig(level=logging.INFO)
        if env != "local":
            logging_client = google.cloud.logging.Client()
            logging_client.setup_logging()
            logging.info("Setup logging")
        logging.info("Starting product embedding sync")
        main()
        logging.info(
            f"Completed product embedding sync in {time.time() - s:.2f} seconds"
        )
    except Exception as ex:
        logging.info(
            f"Failed to sync product embedding sync in {time.time() - s:.2f} seconds"
        )
        logging.exception(ex)
    finally:
        conn_pool.closeall()
