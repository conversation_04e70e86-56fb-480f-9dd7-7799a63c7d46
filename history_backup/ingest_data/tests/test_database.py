import unittest
from unittest.mock import MagicMock, patch
from database import upsert_products, get_embedding_inserts
from models import Product, Embedding


class TestUpsertProducts(unittest.TestCase):

    def setUp(self):
        # Create a mock session
        self.session = MagicMock()

        # Example products
        self.products = [
            Product(
                id="1",
                name="Product 1",
                description="Product 1",
                priceUsd=1000,
                productUrl="https://example.com",
                imgUrl="http://example.com/image1.jpg",
                additionalImgUrls=["http://example.com/image1_1.jpg"],
                isAuthenticated=True,
                sizeId="123",
                primaryBrandId="1",
                primaryColor="blue",
                condition="Excellent",
                lastSeenAt="2024-07-01",
            ),
            Product(
                id="2",
                imgUrl="http://example.com/image2.jpg",
                priceUsd=1000,
                productUrl="https://example.com",
                isAuthenticated=True,
                sizeId="123",
                primaryBrandId="1",
                primaryColor="blue",
                condition="Excellent",
                lastSeenAt="2024-07-01",
            ),
        ]

    @patch("database.get_embedding_inserts", return_value=[])
    def test_upsert_products_successful(self, mock_get_embedding_inserts):
        # Mock the query to return no existing products
        self.session.query().filter().all.return_value = []

        # Mock the result of the execute method
        self.session.execute.return_value.rowcount = 2

        # Call the method
        affected_rows, embedding_row_count = upsert_products(
            self.session, self.products, "poshmark", []
        )

        # Assertions
        self.assertEqual(affected_rows, 2)
        self.assertEqual(embedding_row_count, 0)
        self.session.commit.assert_called()

    @patch("database.get_embedding_inserts", return_value=[])
    def test_upsert_products_with_existing_and_new_products(
        self, mock_get_embedding_inserts
    ):
        # Mock the query to return existing products
        self.session.query().filter().all.return_value = [
            self.products[0],
            self.products[1],
        ]

        # Call the method
        products = self.products + [
            Product(
                id="3",
                imgUrl="http://example.com/image2.jpg",
                priceUsd=1000,
                productUrl="https://example.com",
                isAuthenticated=True,
                sizeId="123",
                primaryBrandId="1",
                primaryColor="blue",
                condition="Excellent",
                lastSeenAt="2024-07-01",
            )
        ]
        self.session.execute.return_value.rowcount = 1

        affected_rows, embedding_row_count = upsert_products(
            self.session, products, "poshmark", []
        )

        # Assertions
        self.assertEqual(affected_rows, 3)
        self.assertEqual(embedding_row_count, 0)
        self.session.execute.assert_called()
        self.session.bulk_update_mappings.assert_called()
        self.session.commit.assert_called()

    @patch("database.get_embedding_inserts", return_value=[])
    def test_upsert_products_with_existing_products(self, mock_get_embedding_inserts):
        # Mock the query to return existing products
        self.session.query().filter().all.return_value = [
            self.products[0],
            self.products[1],
        ]

        # Call the method
        affected_rows, embedding_row_count = upsert_products(
            self.session, self.products, "poshmark", []
        )

        # Assertions
        self.assertEqual(affected_rows, 2)
        self.assertEqual(embedding_row_count, 0)
        # We do not call execute because this is only for full updates or new products
        self.session.execute.assert_not_called()
        self.session.bulk_update_mappings.assert_called()
        self.session.commit.assert_called()

    @patch("database.get_embedding_inserts")
    def test_upsert_products_inserts_embeddings(self, mock_get_embedding_inserts):
        # Mock the query to return no existing products
        self.session.query().filter().all.return_value = []

        # Mock the result of the execute method
        self.session.execute.return_value.rowcount = 2

        # Mock embedding inserts
        embedding_inserts = [
            {
                "imageUrl": "http://example.com/image1.jpg",
                "productId": "1",
                "status": "PENDING",
                "embeddingType": "IMAGE",
            },
            {
                "imageUrl": "http://example.com/image2.jpg",
                "productId": "2",
                "status": "PENDING",
                "embeddingType": "IMAGE",
            },
        ]
        mock_get_embedding_inserts.return_value = embedding_inserts

        # Call the method
        affected_rows, embedding_row_count = upsert_products(
            self.session, self.products, "poshmark", []
        )

        # Assertions
        self.assertEqual(affected_rows, 2)
        self.assertEqual(embedding_row_count, 2)
        self.session.commit.assert_called()

        # Extract the arguments from the embedding insert call
        calls = self.session.execute.call_args_list
        embedding_insert_call_args = calls[1][0][0]  # This should be the second call
        compiled_sql = embedding_insert_call_args.compile(
            compile_kwargs={"literal_binds": True}
        )

        # Validate the embedding insert statement and values
        for embedding_insert in embedding_inserts:
            for key, value in embedding_insert.items():
                self.assertIn(value, str(compiled_sql))

    def test_upsert_products_exception(self):
        # Mock the query to raise an exception
        self.session.execute.side_effect = Exception("Database error")

        # Call the method and assert it raises an exception
        with self.assertRaises(Exception) as context:
            upsert_products(self.session, self.products, "poshmark", [])

        self.assertEqual(str(context.exception), "Database error")
        self.session.rollback.assert_called()

    def test_get_embedding_inserts_no_existing_embeddings(self):
        # Mock the query to return no existing embeddings
        self.session.query().filter().all.return_value = []

        # Call the method
        embedding_inserts = get_embedding_inserts(self.session, self.products)

        # Expected embedding inserts
        expected_inserts = [
            {
                "imageUrl": "http://example.com/image1.jpg",
                "productId": "1",
                "status": "PENDING",
                "embeddingType": "IMAGE",
            },
            {
                "imageUrl": "http://example.com/image1_1.jpg",
                "productId": "1",
                "status": "PENDING",
                "embeddingType": "IMAGE",
            },
            {
                "imageUrl": "http://example.com/image2.jpg",
                "productId": "2",
                "status": "PENDING",
                "embeddingType": "IMAGE",
            },
        ]

        # Assertions
        self.assertEqual(len(embedding_inserts), len(expected_inserts))
        for insert in expected_inserts:
            self.assertIn(insert, embedding_inserts)

    def test_get_embedding_inserts_with_existing_embeddings(self):
        # Mock the query to return existing embeddings
        self.session.query().filter().all.return_value = [
            Embedding(id="1", productId="1", imageUrl="http://example.com/image1.jpg"),
        ]

        # Call the method
        embedding_inserts = get_embedding_inserts(self.session, self.products)

        # Expected embedding inserts (excluding the existing embedding)
        expected_inserts = [
            {
                "imageUrl": "http://example.com/image1_1.jpg",
                "productId": "1",
                "status": "PENDING",
                "embeddingType": "IMAGE",
            },
            {
                "imageUrl": "http://example.com/image2.jpg",
                "productId": "2",
                "status": "PENDING",
                "embeddingType": "IMAGE",
            },
        ]

        # Assertions
        self.assertEqual(len(embedding_inserts), len(expected_inserts))
        for insert in expected_inserts:
            self.assertIn(insert, embedding_inserts)

    def test_get_embedding_inserts_no_images(self):
        # Products without images
        products = [
            Product(id="3", imgUrl=None, lastSeenAt="2024-07-01"),
        ]

        # Call the method
        embedding_inserts = get_embedding_inserts(self.session, products)

        # Expected embedding inserts
        expected_inserts = []

        # Assertions
        self.assertEqual(embedding_inserts, expected_inserts)
