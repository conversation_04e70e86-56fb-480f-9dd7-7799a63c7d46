import enum

import cuid

from sqlalchemy import Column, String, <PERSON><PERSON><PERSON>, Enum, Integer
from sqlalchemy.orm import relationship
from .base import Base
from pgvector.sqlalchemy import Vector


class EmbeddingStatus(enum.Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    SUCCEEDED = "SUCCEEDED"
    FAILED = "FAILED"


class EmbeddingType(enum.Enum):
    IMAGE = "IMAGE"
    TEXT = "TEXT"
    IMAGE_AND_TEXT = "IMAGE_AND_TEXT"


class Embedding(Base):
    __tablename__ = "Embedding"
    id = Column(String, primary_key=True, default=cuid.cuid)
    imageUrl = Column(String, nullable=True)
    vector = Column(Vector(512), nullable=True)
    productId = Column(String)
    status = Column(Enum(EmbeddingStatus), nullable=True)
    embeddingType = Column(Enum(EmbeddingType), nullable=True)

    # Relationship to Product model
    # product = relationship("Product", back_populates="embeddings")


class Embedding_v2(Base):
    __tablename__ = "Embedding_v2"
    id = Column(Integer, primary_key=True, autoincrement=True)
    imageUrl = Column(String, nullable=True)
    vector = Column(Vector(512), nullable=True)
    productId = Column(String)
    status = Column(Enum(EmbeddingStatus), nullable=True)

    # Relationship to Product model
    # product = relationship("Product", back_populates="embeddings")
