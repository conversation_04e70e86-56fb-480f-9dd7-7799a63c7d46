from sqlalchemy import (
    Column,
    String,
    ForeignKey,
    PrimaryKeyConstraint,
)

from .base import Base


class _ProductAttributes(Base):
    __tablename__ = "_ProductAttributes"
    A = Column(
        String,
        ForeignKey("Attribute.text", onupdate="CASCADE", ondelete="CASCADE"),
        nullable=False,
    )
    B = Column(
        String,
        ForeignKey("Product.text", onupdate="CASCADE", ondelete="CASCADE"),
        nullable=False,
    )

    __table_args__ = (PrimaryKeyConstraint("A", "B"),)
