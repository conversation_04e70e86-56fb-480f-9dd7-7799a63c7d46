import json
import logging
import os
import sys
import time

import sqlalchemy
from google.cloud import storage
from sqlalchemy import URL
from sqlalchemy.orm import sessionmaker

from models import Embedding
from models.embedding import Embedding_v2


def connect_with_connector() -> sqlalchemy.engine.base.Engine:
    db_user = os.getenv("DB_USER", "ingest_data_user")
    db_pass = os.getenv("DB_PASS")
    db_name = os.getenv("DB_NAME", "postgres")
    db_host = os.getenv("MAIN_DB_IP")

    url_object = URL.create(
        "postgresql+psycopg2",
        username=db_user,
        password=db_pass,
        host=db_host,
        database=db_name,
    )

    return sqlalchemy.create_engine(
        url_object,
        executemany_mode="values_plus_batch",
        executemany_batch_page_size=5000,
    )


def setup_loggin():
    logging.basicConfig(stream=sys.stdout, level=logging.INFO)


def insert_embeddings_v2(batch_size=1000, total_records=50000000):
    engine = connect_with_connector()
    Session = sessionmaker(bind=engine)
    setup_loggin()
    offset = 0
    total_query_time = 0
    total_insert_time = 0
    start_time = time.time()

    with Session() as session:
        while offset < total_records:
            logging.info(f"Migrating records {offset} to {offset + batch_size}")

            # Measure query time
            query_start_time = time.time()
            records = (
                session.query(Embedding)
                .order_by(Embedding.id)
                .offset(offset)
                .limit(batch_size)
                .all()
            )
            query_end_time = time.time()
            query_time = query_end_time - query_start_time
            total_query_time += query_time
            logging.info(f"Query time for batch: {query_time:.4f} seconds")

            # Transform records for Embedding_v2
            new_records = []
            for record in records:
                new_record = Embedding_v2(
                    imageUrl=record.imageUrl,
                    productId=record.productId,
                    status=record.status,
                )
                new_records.append(new_record)

            # Insert records into Embedding_v2
            insert_start_time = time.time()
            session.bulk_save_objects(new_records)
            session.commit()
            insert_end_time = time.time()
            insert_time = insert_end_time - insert_start_time
            total_insert_time += insert_time
            logging.info(f"Insert time for batch: {insert_time:.4f} seconds")
            session.bulk_save_objects(new_records)
            session.commit()

            offset += batch_size

    end_time = time.time()
    total_time = end_time - start_time
    logging.info(f"Total migration time: {total_time:.4f} seconds")
    logging.info(f"Total query time: {total_query_time:.4f} seconds")
    logging.info(f"Total insert time: {total_insert_time:.4f} seconds")


insert_embeddings_v2()
