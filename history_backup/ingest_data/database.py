import time
from datetime import datetime

from sqlalchemy import URL

from models import Product, Embedding

from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import Session
import sqlalchemy
import os
import logging
from typing import List, Dict, Tuple
from sqlalchemy.exc import SQLAlchemyError

from models.product_attribute import _ProductAttributes


def connect_with_connector() -> sqlalchemy.engine.base.Engine:
    db_user = os.getenv("DB_USER", "ingest_data_user")
    db_pass = os.getenv("DB_PASS")
    db_name = os.getenv("DB_NAME", "postgres")
    db_host = os.getenv("MAIN_DB_IP")

    url_object = URL.create(
        "postgresql+psycopg2",
        username=db_user,
        password=db_pass,
        host=db_host,
        database=db_name,
    )

    return sqlalchemy.create_engine(
        url_object,
        executemany_mode="values_plus_batch",
        executemany_batch_page_size=5000,
    )


def mark_sold_out(session: Session, unavailable_products: List[Dict]) -> None:
    product_ids = [p.get("id") for p in unavailable_products]
    existing_products = session.query(Product).filter(Product.id.in_(product_ids)).all()

    for product in existing_products:
        product.soldAt = product.lastSeenAt
        product.updatedAt = str(datetime.now())
        product.lastSeenAt = str(datetime.now())

    session.commit()


def upsert_products(
    session: Session,
    products: List[Product],
    partner_name: str,
    attribute_list: List[dict],
) -> tuple[int, int]:
    """
    Bulk upsert product data into the database using SQLAlchemy.
    Returns the number of records affected and the set of product IDs requiring embedding- those
    that are new products or those where the url has changed.

    Args:
        session (Session): The database session.
        products (List[Product]): List of product instances to be upserted.

    Returns:
        Tuple[int, Dict[str, str]]: Number of records affected, Dictionary of product IDs to image URLs requiring embedding.

    Raises:
        SQLAlchemyError: If a database operation fails.
    """
    if not products:
        return 0, 0

    product_url_map = {product.id: product.imgUrl for product in products}

    existing_products = (
        session.query(
            Product.id,
            Product.lastSeenAt,
            Product.name,
            Product.description,
            Product.priceUsd,
            Product.productUrl,
            Product.imgUrl,
            Product.additionalImgUrls,
            Product.isAuthenticated,
            Product.sizeId,
            Product.primaryBrandId,
            Product.primaryColor,
            Product.condition,
            Product.gender,
            Product.rawCategoryBroad,
        )
        .filter(Product.id.in_(product_url_map.keys()))
        .all()
    )
    existing_product_map = {prod.id: prod for prod in existing_products}

    products_data_full_upsert = []
    products_data_last_seen_update = []
    skipped_products = []
    product_scan_start = time.time()
    for product in products:
        existing_product = existing_product_map.get(product.id)
        if existing_product:
            if existing_product.lastSeenAt > product.lastSeenAt:
                skipped_products.append(product.id)
                continue

            # Check if product fields have changed
            fields_changed = any(
                getattr(existing_product, field) != getattr(product, field)
                for field in [
                    "name",
                    "description",
                    "priceUsd",
                    "productUrl",
                    "imgUrl",
                    "additionalImgUrls",
                    "isAuthenticated",
                    "sizeId",
                    "primaryBrandId",
                    "primaryColor",
                    "condition",
                    "gender",
                    "rawCategoryBroad",
                ]
            )

            if not fields_changed:
                # If fields haven't changed, only update lastSeenAt
                products_data_last_seen_update.append(
                    {
                        "id": product.id,
                        "lastSeenAt": product.lastSeenAt,
                    }
                )
            else:
                products_data_full_upsert.append(
                    {
                        **{
                            "createdAt": getattr(product, "lastSeenAt"),
                            "updatedAt": getattr(product, "lastSeenAt"),
                        },
                        **{
                            column.name: getattr(product, column.name)
                            for column in Product.__table__.columns
                            if column.name
                            not in [
                                "createdAt",
                                "updatedAt",
                            ]
                        },
                    }
                )
        else:
            products_data_full_upsert.append(
                {
                    **{
                        "createdAt": getattr(product, "lastSeenAt"),
                        "updatedAt": getattr(product, "lastSeenAt"),
                    },
                    **{
                        column.name: getattr(product, column.name)
                        for column in Product.__table__.columns
                        if column.name
                        not in [
                            "createdAt",
                            "updatedAt",
                        ]
                    },
                }
            )

    product_scan_end = time.time()
    logging.info(f"Product scan took {product_scan_end - product_scan_start} seconds")
    ids_changed = [product["id"] for product in products_data_full_upsert]
    logging.debug(
        f"{len(products_data_full_upsert)} products have changed and require a full update: {ids_changed}"
    )
    logging.info(
        f"{len(products_data_full_upsert)} products have changed and require a full update"
    )
    ids_just_last_updated_changed = [
        product["id"] for product in products_data_last_seen_update
    ]
    logging.debug(
        f"{len(products_data_last_seen_update)} products have not changed and only require lastSeenAt update: {ids_just_last_updated_changed}"
    )
    logging.info(
        f"{len(products_data_last_seen_update)} products have not changed and only require lastSeenAt update"
    )
    logging.info(
        f"Skipping {len(skipped_products)} upserts for products "
        f"because products lastSeenAt date is before the existing db entry"
    )
    if len(products_data_full_upsert) == 0 and len(products_data_last_seen_update) == 0:
        return 0, 0

    image_embedding_insert_stmt = []
    """
        Without splitting the Embedding inserts into chunks we get a
        pg8000 error struct.error: 'H' format requires 0 <= number <= 65535
        Consider refactoring if this error is more easily fixed using a different approach
    """
    embedding_inserts = get_embedding_inserts(session, products)
    logging.info(f"Embedding inserts: {len(embedding_inserts)}")

    embedding_insert_chunks = list(chunks(embedding_inserts, 2500))
    logging.info(f"Embedding insert chunks: {len(embedding_insert_chunks)}")

    for chunk in embedding_insert_chunks:
        image_embedding_insert_stmt.append(insert(Embedding).values(chunk))

    try:
        affected_rows = 0
        upsert_start = time.time()
        if products_data_full_upsert:
            stmt = insert(Product).values(products_data_full_upsert)
            update_dict = {
                column.name: stmt.excluded[column.name]
                for column in Product.__table__.columns
                if column.name != "id"
                and column.name != "createdAt"
                and column.name != "secondhandRetailerId"
            }
            do_update_stmt = stmt.on_conflict_do_update(
                index_elements=["id"], set_=update_dict
            )
            result = session.execute(do_update_stmt)
            affected_rows += result.rowcount

        # Only update lastSeenAt because data has not changed
        if products_data_last_seen_update:
            session.bulk_update_mappings(Product, products_data_last_seen_update)
            affected_rows += len(products_data_last_seen_update)

        session.commit()
        upsert_end = time.time()
        logging.info(
            f"Product upserts took {upsert_end - upsert_start:.2f} seconds for partner {partner_name}"
        )
        embedding_row_count = 0

        embedding_start = time.time()
        if embedding_inserts:
            embedding_row_count = 0
            for insrt in image_embedding_insert_stmt:
                embedding_result = session.execute(insrt)
                session.commit()
                embedding_row_count += embedding_result.rowcount
        embedding_end = time.time()
        logging.info(
            f"Embedding inserts took {embedding_end-embedding_start:.2f} seconds for partner {partner_name}"
        )

        if len(attribute_list) > 0:
            attribute_insert_start = time.time()
            attribute_inst_stmt = insert(_ProductAttributes).values(attribute_list)
            attribute_inst_stmt_on_conflict = (
                attribute_inst_stmt.on_conflict_do_nothing()
            )
            session.execute(attribute_inst_stmt_on_conflict)
            session.commit()
            logging.info(
                f"Attribute insert took {time.time() - attribute_insert_start:.2f} seconds"
            )
        return affected_rows, embedding_row_count
    except SQLAlchemyError as e:
        session.rollback()
        logging.error(f"Database SQL operation failed: {str(e)}")
        logging.exception(e)
        raise
    except Exception as e:
        session.rollback()

        logging.error(f"Database operation failed: {str(e)}")
        logging.exception(e)
        raise


def get_embedding_inserts(session: Session, products: List[Product]) -> list:
    product_url_map = {}
    for product in products:
        if product.imgUrl is not None:
            image_url_list = [product.imgUrl]
            if product.additionalImgUrls:
                image_url_list.extend(product.additionalImgUrls)
            product_url_map[product.id] = image_url_list

    get_existing_embeddings_start = time.time()
    existing_embeddings = (
        session.query(Embedding.id, Embedding.productId, Embedding.imageUrl)
        .filter(Embedding.productId.in_(product_url_map.keys()))
        .all()
    )
    get_existing_embeddings_end = time.time()
    logging.info(
        f"Getting existing embeddings took {get_existing_embeddings_end-get_existing_embeddings_start:.2f} seconds"
    )
    for embedding in existing_embeddings:
        if embedding.productId in product_url_map:
            product_images = product_url_map[embedding.productId]
            if embedding.imageUrl in product_images:
                product_images.remove(embedding.imageUrl)

    image_embedding_data = []
    for product_id, image_url_list in product_url_map.items():
        image_embedding_data.extend(
            [
                {
                    "imageUrl": img,
                    "productId": product_id,
                    "status": "PENDING",
                    "embeddingType": "IMAGE",
                }
                for img in image_url_list
            ]
        )

    return image_embedding_data


def chunks(lst, n):
    """Yield successive n-sized chunks from lst."""
    for i in range(0, len(lst), n):
        yield lst[i : i + n]
