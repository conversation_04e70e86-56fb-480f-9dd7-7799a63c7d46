import logging
import os
import sys
from typing import List
from sqlalchemy.orm import Session
from dotenv import load_dotenv
from marshmallow import ValidationError
import json
import functions_framework
import google.cloud.logging
from google.cloud import storage
from sqlalchemy.orm import sessionmaker
from schemas.product_schema import ProductSchema
from database import connect_with_connector, upsert_products, mark_sold_out
from util.gcs_task_manager import GCSTaskManager
from util.pipeline_db.connector import connect_to_pipeline_db_with_connector
from util.pipeline_db.models import Status
from util.pipeline_db.service import (
    update_partner_product_file_syncs_status,
    get_partner_product_file_syncs_status,
)

dotenv_path = os.path.join(os.path.dirname(__file__), ".env.dev")

# Load the environment variables from .env.dev
load_dotenv(dotenv_path)

schema = ProductSchema()


@functions_framework.http
def ingest_chunk(request):
    """HTTP Cloud Function to fetch data for a given partner."""
    setup_logging()
    file_name = ""
    try:
        request_json = request.get_json(silent=True)
        logging.info(f"Received request to ingest data chunk {request_json}")
        if (
            request_json
            and "message" in request_json
            and "attributes" in request_json["message"]
        ):
            message = request_json["message"]["attributes"]
            logging.info(f"Received message: {message}")
        else:
            logging.error("Missing message or attributes in the request")
            return "Bad Request", 400

        if message and "objectId" in message and "bucketId" in message:
            file_name = message["objectId"]
            bucket_name = message["bucketId"]
            logging.info(f"Received file: {file_name} from bucket: {bucket_name}")
        else:
            logging.error("Missing file name or bucket name in the request")
            return "Bad Request", 400

        logging.info(f"Processing file: {file_name} from bucket: {bucket_name}")
        # Extract the folder path from the file name
        folder_path = "/".join(file_name.split("/")[:-1])
        if folder_path:
            logging.info(f"Folder path: {folder_path}")

        # Placeholder for additional processing logic
        return stream_jsonl_in_batches(bucket_name, file_name, folder_path)
    except Exception as e:
        logging.error(f"Error processing data for {file_name}: {e}")
        logging.exception(e)
        return f"Error processing data for {request}: {e}", 500


def setup_logging():
    if os.getenv("ENVIRONMENT") != "local":
        logging.getLogger().setLevel(logging.INFO)
        client = google.cloud.logging.Client()
        client.setup_logging()
    else:
        logging.basicConfig(stream=sys.stdout, level=logging.INFO)


def stream_jsonl_in_batches(bucket_name, blob_name, partner_name, batch_size=1000):
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    logging.info(
        f"Processing file: {blob_name} from bucket: {bucket_name} for {partner_name}"
    )

    blob.reload()
    partner_product_file_sync_id = blob.metadata.get("partner_product_file_sync_id")

    datapipeline_engine = connect_to_pipeline_db_with_connector()
    PipelineDbSession = sessionmaker(bind=datapipeline_engine)

    with PipelineDbSession() as pipeline_session:
        status = get_partner_product_file_syncs_status(
            pipeline_session, partner_product_file_sync_id
        )
        if status in [
            Status.INGESTING.value,
            Status.INGESTED.value,
        ]:
            logging.warning(
                f"{blob_name} with id {partner_product_file_sync_id} is already in {status} state. "
                f"Will skip processing."
            )
            return f"{blob_name} already in {status} state. Skipping processing.", 200
        update_partner_product_file_syncs_status(
            pipeline_session,
            partner_product_file_sync_id,
            Status.INGESTING.value,
        )

    # Connect to the database
    engine = connect_with_connector()
    Session = sessionmaker(bind=engine)
    # Open the blob for streaming
    blob_stream = blob.open("rt")
    try:
        batch = []
        for line in blob_stream:
            data = json.loads(line)
            batch.append(data)
            # Process the batch when it reaches the specified size
            if len(batch) == batch_size:
                process_batch(
                    session_maker=Session, partner_name=partner_name, batch=batch
                )
                batch = []  # Reset the batch after processing

        # Process any remaining records
        if batch:
            process_batch(session_maker=Session, partner_name=partner_name, batch=batch)

        with PipelineDbSession() as pipeline_session:
            partner_name, partner_product_sync_id = (
                update_partner_product_file_syncs_status(
                    pipeline_session,
                    partner_product_file_sync_id,
                    Status.INGESTED.value,
                )
            )
        GCSTaskManager().create_task(partner_name, partner_product_sync_id)
    except Exception as e:
        logging.error(f"Error processing data for {partner_product_file_sync_id}!")
        logging.error(e)

        with PipelineDbSession() as pipeline_session:
            update_partner_product_file_syncs_status(
                pipeline_session,
                partner_product_file_sync_id,
                Status.FAILED_INGEST.value,
            )
        raise e
    finally:
        blob_stream.close()

    logging.info(f"Ingest data completed successfully for {blob_name}!")
    return "Ingest data completed successfully", 200


def process_batch(session_maker: Session, partner_name: str, batch: List[str]):
    # Placeholder function to process each batch of records
    logging.info(f"Processing batch of size: {len(batch)}")
    # Add your actual processing logic here
    with session_maker() as session:
        process_jsonl_products(
            session=session, partner_name=partner_name, json_lines=batch
        )


def process_jsonl_products(session: Session, partner_name: str, json_lines: List[dict]):
    """Process multiple JSON lines of product data and bulk upsert using the database session.

    Args:
        session (Session): Database session to be used for the bulk upsert.
        json_lines (List[str]): List of JSONL strings containing product data.

    Returns:
        Result of the database operation.

    Raises:
        ValueError: If JSON decoding fails or data validation fails.
        RuntimeError: If an unexpected error occurs.
    """
    # Using a set to remove duplicates
    products = {}
    unavailable_products = {}
    attribute_list = []
    for data in json_lines:
        if data.get("is_available") is False:
            unavailable_products[data.get("id")] = data
            # Do not add unavailable product to list of products to upsert
            continue
        try:
            if "attribute_list" in data:
                data_attributes = data.pop("attribute_list")
                attribute_list.extend(
                    [
                        {"A": attribute["attribute_id"], "B": data["id"]}
                        for attribute in data_attributes
                    ]
                )
            product_data = schema.load(data)
            products[product_data.id] = product_data
        except ValidationError as e:
            logging.info(f"Data validation failed for line {data}: {str(e)}")
        except Exception as e:
            logging.info(
                f"An unexpected error occurred while processing the product line {data}: {str(e)}"
            )
    if unavailable_products:
        logging.info(f"Marking {len(unavailable_products)} products as sold out")
        mark_sold_out(session, list(unavailable_products.values()))

    if products:
        logging.info(f"Upserting {len(products)} products...")
        print(f"Attributes: {attribute_list}")
        rowcount, ids_to_embed = upsert_products(
            session, list(products.values()), partner_name, attribute_list
        )
        logging.info(f"Upserted {rowcount} products.")
        return rowcount, ids_to_embed

    logging.info(f"No products to upsert for {partner_name}!")
    return None
