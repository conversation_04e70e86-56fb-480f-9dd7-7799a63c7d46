import pytest
from testcontainers.postgres import PostgresContainer
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy import create_engine
from models import Product, Base, SecondhandRetailer
from main import upsert_products, process_jsonl_products
from decimal import Decimal
import logging
from io import StringIO


@pytest.fixture(scope="session")
def db_engine():
    with PostgresContainer(image="postgres:16", driver="pg8000") as postgres:
        connection_url = postgres.get_connection_url().replace(
            "postgresql://", "postgresql+pg8000://"
        )
        engine = create_engine(connection_url)
        Base.metadata.create_all(engine)
        yield engine
        engine.dispose()


@pytest.fixture(scope="function")
def db_session(db_engine) -> Session:
    connection = db_engine.connect()
    transaction = connection.begin()
    session = sessionmaker(bind=connection)()
    try:
        yield session
    finally:
        session.close()
        transaction.rollback()
        connection.close()


@pytest.fixture(scope="function")
def setup_retailer(db_session):
    retailer = SecondhandRetailer(id="retailer123", displayName="Retailer 123")
    db_session.add(retailer)
    db_session.commit()
    return retailer


@pytest.mark.skip("pgvector type not available in testcontainer")
def test_insert_and_upsert_product(db_session, setup_retailer):
    retailer = setup_retailer  # Use the retailer created in the setup fixture

    # Insert new product
    new_product = Product(
        id="existing_product_id",
        name="Test Product",
        description="Description",
        priceUsd=100.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        secondhandRetailerId=retailer.id,
    )
    db_session.add(new_product)
    db_session.commit()

    inserted_product = db_session.query(Product).filter_by(name="Test Product").one()
    assert inserted_product
    assert inserted_product.priceUsd == 100.00

    # Upsert product with updated information
    product_to_upsert = Product(
        id="existing_product_id",
        name="Test Product",
        description="Updated Description",
        priceUsd=150.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        secondhandRetailerId=retailer.id,
    )
    products_upserted, embeddings_inserted = upsert_products(
        db_session, [product_to_upsert]
    )
    # Image didn't change, so no embedding required
    assert products_upserted == 1
    assert embeddings_inserted == 3
    db_session.expire_all()

    updated_product = db_session.query(Product).filter_by(name="Test Product").one()
    assert updated_product.description == "Updated Description"
    assert updated_product.priceUsd == 150.00


@pytest.mark.skip("pgvector type not available in testcontainer")
def test_insert_and_upsert_produc_no_new_embedding(db_session, setup_retailer):
    retailer = setup_retailer  # Use the retailer created in the setup fixture

    # Insert new product
    new_product = Product(
        id="existing_product_id",
        name="Test Product",
        description="Description",
        priceUsd=100.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        secondhandRetailerId=retailer.id,
    )
    products_upserted, embeddings_inserted = upsert_products(db_session, [new_product])

    product = get_product(db_session, "existing_product_id")
    assert product.name == "Test Product"
    assert embeddings_inserted == 3

    updated_product = Product(
        id="existing_product_id",
        name="Updated Product",
        description="Updated Description",
        priceUsd=100.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        secondhandRetailerId=retailer.id,
    )
    products_upserted, embeddings_inserted = upsert_products(
        db_session, [updated_product]
    )

    assert embeddings_inserted == 0

    updated_product_another_img = Product(
        id="existing_product_id",
        name="Updated Product",
        description="Updated Description",
        priceUsd=100.00,
        productUrl="http://example.com/product",
        imgUrl="http://example.com/image.jpg",
        additionalImgUrls=[
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg",
        ],
        secondhandRetailerId=retailer.id,
    )
    products_upserted, embeddings_inserted = upsert_products(
        db_session, [updated_product_another_img]
    )

    assert embeddings_inserted == 1


@pytest.mark.skip("pgvector type not available in testcontainer")
def test_process_jsonl_products_valid(db_session, setup_retailer):
    log_stream = StringIO()
    logging.basicConfig(stream=log_stream, level=logging.WARNING)

    retailer = setup_retailer
    product_data = {
        "id": "12345",
        "name": "Test Product 2",
        "description": "Description",
        "priceUsd": 19.99,
        "productUrl": "http://example.com/product",
        "imgUrl": "http://example.com/image.jpg",
        "additionalImgUrls": [
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        "secondhandRetailerId": str(retailer.id),
    }

    result_counts, embedding_count = process_jsonl_products(db_session, [product_data])
    assert result_counts == 1
    assert embedding_count == 3

    updated_product = db_session.query(Product).filter_by(id="12345").one()
    assert updated_product.name == "Test Product 2"
    assert updated_product.description == "Description"
    assert updated_product.priceUsd == Decimal("19.99")
    assert updated_product.productUrl == "http://example.com/product"

    # Updated data for the same product with a changed imgUrl
    updated_product_data = {
        "id": "12345",
        "name": "Updated Test Product",
        "description": "Updated Description",
        "priceUsd": 19.99,
        "productUrl": "http://example.com/updated_product",
        "imgUrl": "http://example.com/updated_image.jpg",
        "additionalImgUrls": [
            "http://example.com/updated_image1.jpg",
            "http://example.com/updated_image2.jpg",
        ],
        "secondhandRetailerId": str(retailer.id),
    }

    # Another new product for testing multiple updates
    new_product_data = {
        "id": "5678",
        "name": "Another Test Product",
        "description": "Another Description",
        "priceUsd": 29.99,
        "productUrl": "http://example.com/another_product",
        "imgUrl": "http://example.com/another_image.jpg",
        "additionalImgUrls": [
            "http://example.com/another_image1.jpg",
            "http://example.com/another_image2.jpg",
        ],
        "secondhandRetailerId": str(retailer.id),
    }

    # Testing processing updated and new products
    result_counts, embedding_count = process_jsonl_products(
        db_session, [updated_product_data, new_product_data]
    )
    assert result_counts == 2
    assert embedding_count == 6
    assert get_product(db_session, "12345").description == "Updated Description"

    log_contents = log_stream.getvalue()
    assert "warning" not in log_contents.lower()

    logging.basicConfig(level=logging.WARNING)


@pytest.mark.skip("pgvector type not available in testcontainer")
def test_process_jsonl_products_valid_snake_case(db_session, setup_retailer):
    log_stream = StringIO()
    logging.basicConfig(stream=log_stream, level=logging.WARNING)

    retailer = setup_retailer
    product_data = {
        "id": "1234",
        "name": "Test Product 2",
        "description": "Description",
        "price_usd": 19.99,
        "product_url": "http://example.com/product",
        "img_url": "http://example.com/image.jpg",
        "additional_img_urls": [
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        "secondhand_retailer_id": str(retailer.id),
    }

    results = process_jsonl_products(db_session, [product_data])
    assert results is not None

    updated_product = db_session.query(Product).filter_by(id="1234").one()
    assert updated_product.name == "Test Product 2"
    assert updated_product.description == "Description"
    assert updated_product.priceUsd == Decimal("19.99")
    assert updated_product.productUrl == "http://example.com/product"

    # Ensure no warnings were logged
    log_contents = log_stream.getvalue()
    assert "warning" not in log_contents.lower()

    logging.basicConfig(level=logging.WARNING)


@pytest.mark.skip("pgvector type not available in testcontainer")
def test_process_jsonl_products_with_mixed_inputs(db_session, setup_retailer):
    log_stream = StringIO()
    handler = logging.StreamHandler(log_stream)
    logger = logging.getLogger()
    logger.addHandler(handler)
    logger.setLevel(logging.WARNING)

    retailer = setup_retailer
    valid_product_data = {
        "id": "12345",
        "name": "Test Product 2",
        "description": "Description",
        "priceUsd": 19.99,
        "productUrl": "http://example.com/product",
        "imgUrl": "http://example.com/image.jpg",
        "additionalImgUrls": [
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
        ],
        "secondhandRetailerId": str(retailer.id),
    }

    invalid_product_data = (
        '{ "id": "invalid", "name": "Faulty Product", "description": }'
    )

    invalid_product_data2 = {
        "id": "12345",
        "name": "Test Product 2",
        "description": "Description",
        "priceUsd": 19.99,
    }  # Missing required fields

    json_lines = [
        valid_product_data,
        invalid_product_data,
        invalid_product_data2,
        valid_product_data,
    ]
    process_jsonl_products(db_session, json_lines)

    products = db_session.query(Product).order_by(Product.id).all()
    assert len(products) == 1
    assert all(product.name == "Test Product 2" for product in products)
    assert all(product.description == "Description" for product in products)
    assert all(product.priceUsd == Decimal("19.99") for product in products)

    log_contents = log_stream.getvalue()
    assert "An unexpected error occurred while" in log_contents
    assert "Data validation failed for line" in log_contents

    logger.removeHandler(handler)
    logger.setLevel(logging.NOTSET)


def get_product(db_session, product_id):
    return db_session.query(Product).filter_by(id=product_id).one()
