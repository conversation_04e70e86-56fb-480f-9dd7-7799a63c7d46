import re

from marshmallow import fields, EXCLUDE, ValidationError
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema, auto_field
from marshmallow_enum import En<PERSON><PERSON><PERSON>
from stringcase import camelcase
import uuid
from enum import Enum

from models import Product, color_enum, condition_enum, gender_enum


class Gender(Enum):
    MEN = "MEN"
    WOMEN = "WOMEN"
    UNISEX = "UNISEX"


class Condition(Enum):
    NEW_WITH_TAG = "NEW_WITH_TAG"
    PRISTINE = "PRISTINE"
    VERY_GOOD = "VERY_GOOD"
    GOOD = "GOOD"


class Color(Enum):
    BLACK = "BLACK"
    GREY = "GREY"
    BROWN = "BROWN"
    RED = "RED"
    BLUE = "BLUE"
    PURPLE = "PURPLE"
    PINK = "PINK"
    ORANGE = "ORANGE"
    WHITE = "WHITE"
    GREEN = "GREEN"
    YELLOW = "YELLOW"
    GOLD = "GOLD"
    SILVER = "SILVER"
    BEIGE = "BEIGE"
    PRINT = "PRINT"


class ProductSchema(SQLAlchemyAutoSchema):
    class Meta:
        model = Product
        load_instance = True  # Make deserialization create model instances
        unknown = EXCLUDE  # Ignore unknown fields

    def load(self, data, many=None, partial=None):
        """Convert snake_case keys to camelCase before deserialization."""
        data = {camelcase(key): value for key, value in data.items()}
        data["primaryBrandId"] = data.get("brand", {}).get("id")
        data["name"] = clean_string(data["name"])
        data["description"] = clean_string(data["description"])
        data["rawBrand"] = data.get("brand", {}).get("raw_brand")
        data["sizeId"] = data.get("size", {}).get("id")
        data["rawSize"] = data.get("size", {}).get("raw_size_value")
        data["rawCategoryBroad"] = data.get("categoryBroad")
        data["rawCategorySpecific"] = data.get("categoryRaw")
        if data.get("priceUsd") and not validate_numeric(data.get("priceUsd")):
            raise ValidationError("Price must be numeric precision=10, scale=2")
        return super().load(data, transient=True, many=many, partial=partial)

    id = auto_field(default=lambda: str(uuid.uuid4()))
    name = auto_field(required=True)
    description = auto_field()
    priceUsd = auto_field(required=True)
    productUrl = auto_field(required=True)
    imgUrl = auto_field(required=True)
    additionalImgUrls = fields.List(fields.String)
    firsthandRetailerId = auto_field()
    secondhandRetailerId = auto_field(required=True)
    isAuthenticated = auto_field()
    isFreeShipping = auto_field()
    isReturnable = auto_field()
    isVintage = auto_field()
    sizeId = auto_field()
    primaryBrandId = auto_field()
    imageQuality = auto_field()
    createdAt = auto_field(dump_only=True)
    updatedAt = auto_field(dump_only=True)
    lastSeenAt = auto_field()
    soldAt = auto_field()


def clean_string(input_string):
    if input_string:
        # Remove null characters and other control characters - temporary fix for bad Ebay files
        # Proper fix implemented in transform cleaners. Will remove after bad files processed.
        return re.sub(r"[\x00-\x1F\x7F]", "", input_string)
    return input_string


def validate_numeric(value, precision=10, scale=2):
    # Convert the value to a string to check its length and decimal places
    value_str = str(value)

    # Split the string at the decimal point
    if "." in value_str:
        integer_part, decimal_part = value_str.split(".")
    else:
        integer_part = value_str
        decimal_part = ""

    # Check the length of the integer and decimal parts
    if len(integer_part) > (precision - scale):
        return False

    if len(integer_part) + len(decimal_part) > precision:
        return False

    if len(decimal_part) > scale:
        return False

    # Check the absolute value against the maximum value for the integer part
    max_integer_value = 10 ** (precision - scale) - 1
    if abs(float(integer_part)) > max_integer_value:
        return False

    return True
