from marshmallow import fields, EXCLUDE
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema, auto_field
from marshmallow_enum import Enum<PERSON>ield
from stringcase import camelcase
import uuid
from enum import Enum

from models import Product, color_enum, condition_enum, gender_enum


class Gender(Enum):
    MEN = "MEN"
    WOMEN = "WOMEN"
    UNISEX = "UNISEX"


class Condition(Enum):
    NEW_WITH_TAG = "NEW_WITH_TAG"
    PRISTINE = "PRISTINE"
    VERY_GOOD = "VERY_GOOD"
    GOOD = "GOOD"


class Color(Enum):
    BLACK = "BLACK"
    GREY = "GREY"
    BROWN = "BROWN"
    RED = "RED"
    BLUE = "BLUE"
    PURPLE = "PURPLE"
    PINK = "PINK"
    ORANGE = "ORANGE"
    WHITE = "WHITE"
    GREEN = "GREEN"
    YELLOW = "YELLOW"
    GOLD = "GOLD"
    SILVER = "SILVER"
    BEIGE = "BEIGE"
    PRINT = "PRINT"


class ProductSchema(SQLAlchemyAutoSchema):
    class Meta:
        model = Product
        load_instance = True  # Make deserialization create model instances
        unknown = EXCLUDE  # Ignore unknown fields

    def load(self, data, many=None, partial=None):
        """Convert snake_case keys to camelCase before deserialization."""
        data = {camelcase(key): value for key, value in data.items()}
        data["primaryBrandId"] = data.get("brand", {}).get("id")
        data["rawBrand"] = data.get("brand", {}).get("raw_brand")
        data["sizeId"] = data.get("size", {}).get("id")
        data["rawSize"] = data.get("size", {}).get("raw_size_value")

        return super().load(data, transient=True, many=many, partial=partial)

    id = auto_field(default=lambda: str(uuid.uuid4()))
    name = auto_field(required=True)
    description = auto_field()
    priceUsd = auto_field(required=True)
    productUrl = auto_field(required=True)
    imgUrl = auto_field(required=True)
    additionalImgUrls = fields.List(fields.String)
    firsthandRetailerId = auto_field()
    secondhandRetailerId = auto_field(required=True)
    isAuthenticated = auto_field()
    isFreeShipping = auto_field()
    isReturnable = auto_field()
    isVintage = auto_field()
    sizeId = auto_field()
    primaryBrandId = auto_field()
    createdAt = auto_field(dump_only=True)
    updatedAt = auto_field(dump_only=True)
    lastSeenAt = auto_field()
    soldAt = auto_field()
