from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from util.pipeline_db.models import PartnerProductSync, PartnerProductFileSync, Status


def create_partner_product_sync(session: Session, partner_name: str):
    try:
        status = Status.PENDING.value
        new_partner_product_sync = PartnerProductSync(
            partner=partner_name, status=status
        )
        session.add(new_partner_product_sync)
        session.commit()
        return new_partner_product_sync.id
    except SQLAlchemyError as e:
        print(f"Error creating partner product sync: {e}")
        session.rollback()


def get_partner_product_sync(session: Session, partner_product_sync_id: str):
    try:
        partner_product_sync = (
            session.query(PartnerProductSync)
            .filter(PartnerProductSync.id == partner_product_sync_id)
            .first()
        )
        return partner_product_sync
    except SQLAlchemyError as e:
        print(f"Error creating partner product sync: {e}")
        session.rollback()


def create_partner_product_file_sync(
    session: Session, partner_product_sync_id: str, file_name: str, partner_name: str
):
    try:
        new_partner_product_file_sync = PartnerProductFileSync(
            partner_product_sync_id=partner_product_sync_id,
            file_name=file_name,
            partner=partner_name,
            status=Status.PENDING.value,
        )
        session.add(new_partner_product_file_sync)
        session.commit()
        return new_partner_product_file_sync.id
    except SQLAlchemyError as e:
        session.rollback()
        print(f"Error creating partner product file sync: {e}")


def update_partner_product_syncs_status(
    session: Session, partner_product_sync_id: str, status: str
):
    try:
        # Fetch the record
        file_sync = (
            session.query(PartnerProductSync)
            .filter(PartnerProductSync.id == partner_product_sync_id)
            .first()
        )

        if file_sync:
            file_sync.status = status
            session.commit()
            print(
                f"Updated PartnerProductSync ID {partner_product_sync_id} to status {status}"
            )
        else:
            print(f"PartnerProductSync ID {partner_product_sync_id} not found.")

    except SQLAlchemyError as e:
        # Rollback the transaction in case of an error
        session.rollback()
        print(f"Error updating status: {e}")


def get_partner_product_file_syncs_status(
    session: Session, partner_product_file_sync_id: str
):
    try:
        # Fetch the record
        file_sync = (
            session.query(PartnerProductFileSync)
            .filter(PartnerProductFileSync.id == partner_product_file_sync_id)
            .first()
        )
        if file_sync:
            return file_sync.status
    except SQLAlchemyError as e:
        print(f"Error updating status: {e}")
        session.rollback()


def get_partner_product_file_syncs_status_by_partner_product_sync_id(
    session: Session, partner_product_sync_id: str
):
    try:
        # Fetch the record
        file_syncs = (
            session.query(PartnerProductFileSync)
            .filter(
                PartnerProductFileSync.partner_product_sync_id
                == partner_product_sync_id
            )
            .all()
        )
        return [f.status for f in file_syncs] if file_syncs else []
    except SQLAlchemyError as e:
        print(f"Error updating status: {e}")
        session.rollback()


def update_partner_product_file_syncs_status(
    session: Session, partner_product_file_sync_id: str, status: str
):
    try:
        # Fetch the record
        file_sync = (
            session.query(PartnerProductFileSync)
            .filter(PartnerProductFileSync.id == partner_product_file_sync_id)
            .first()
        )

        partner_name = file_sync.partner
        partner_product_sync_id = file_sync.partner_product_sync_id

        if file_sync:
            # Update the status
            file_sync.status = status
            # Commit the transaction
            session.commit()
            print(
                f"Updated PartnerProductFileSync ID {partner_product_file_sync_id} to status {status}"
            )
            return partner_name, partner_product_sync_id
        else:
            print(
                f"PartnerProductFileSync ID {partner_product_file_sync_id} not found."
            )

    except SQLAlchemyError as e:
        # Rollback the transaction in case of an error
        session.rollback()
        print(f"Error updating status: {e}")


def get_all_syncs_ingested(session: Session, partner_product_sync_id: str):
    statuses = get_partner_product_file_syncs_status_by_partner_product_sync_id(
        session, partner_product_sync_id
    )
    all_syncs_ingested = all(status == Status.INGESTED.value for status in statuses)
    partner_product_sync = get_partner_product_sync(session, partner_product_sync_id)
    return all_syncs_ingested, partner_product_sync.created_at
