import logging

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from models import Product


def update_sold_at_before_timestamp(
    session: Session, partner_name: str, created_at, batch_size=10000
):

    embeddings = (
        session.query(Embedding).filter(Embedding.partner_name == partner_name).all()
    )
    offset = 0
    while True:
        try:

            # Query a batch of products where last_seen_at is before the given created_at timestamp
            products = (
                session.query(Product)
                .filter(
                    Product.secondhandRetailerId == partner_name,
                    Product.lastSeenAt < created_at,
                    Product.soldAt.is_(None),
                )
                .offset(offset)
                .limit(batch_size)
                .all()
            )

            if not products:
                logging.info(f"No products to update for {partner_name}")
                break

            logging.info(
                f"Updating sold_at to {created_at} on {len(products)} products for {partner_name}"
            )
            for product in products:
                product.soldAt = created_at

            session.commit()
            offset += batch_size
        except SQLAlchemyError as e:
            logging.error(
                f"SQLAlchemy Error processing batch starting at offset {offset}: {e}"
            )
            offset += batch_size
            session.rollback()
            break
        except Exception as e:
            logging.error(
                f"Exception processing batch starting at offset {offset}: {e}"
            )
            session.rollback()
            break
