import logging
import os

import functions_framework
import google.cloud.logging
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker, Session

from schemas.product_schema import ProductSchema
from util.main_db.connector import connect_to_main_db_with_connector

schema = ProductSchema()


@functions_framework.http
def start_monitor(request):
    """HTTP Cloud Function to fetch data for a given partner."""
    setup_logging()
    logging.info("Running monitoring function for data pipeline.")
    try:
        engine = connect_to_main_db_with_connector()
        Session = sessionmaker(bind=engine)
        with Session() as session:
            statuses = check_embedding_status(session)
            for partner, status, count in statuses:
                if count > 200:
                    logging.error(
                        f"Alert: Retailer {partner} has {count} items with status {status}"
                    )
        return "Monitoring function ran successfully.", 200
    except Exception as e:
        logging.exception(e)
        return "Monitoring function ran unsuccessfully.", 500


def send_alert(retailer_id, status, count):
    alert_message = (
        f"Alert: Retailer {retailer_id} has {count} items with status {status}"
    )
    logging.error(alert_message)


def check_embedding_status(session: Session):
    query = """
    select "secondhandRetailerId", e.status, count(*) from "Embedding" e join public."Product" P on e."productId" = P.id
    where e.status = 'FAILED'
    group by "secondhandRetailerId", e.status order by status;
    """
    result = session.execute(text(query))
    return result.fetchall()


def setup_logging():
    logging.getLogger().setLevel(logging.INFO)
    if os.getenv("ENVIRONMENT") != "local":
        client = google.cloud.logging.Client()
        client.setup_logging()
