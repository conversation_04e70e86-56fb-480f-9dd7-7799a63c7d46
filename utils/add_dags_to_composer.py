from __future__ import annotations

import argparse
import glob
import os
from shutil import copytree, ignore_patterns
import tempfile

# Imports the Google Cloud client library
from google.cloud import storage

import os
import shutil
import sys

from google.cloud import storage


def download_schema(root_dir: str = "dags/dependencies"):
    """Download the generated schema from GCS to local directory."""
    # Create the target directory if it doesn't exist
    target_dir = os.path.join(root_dir, "generated_schema", "python")
    os.makedirs(target_dir, exist_ok=True)

    try:
        # Initialize GCS client
        client = storage.Client()
        bucket = client.bucket("phia-schema")

        # List and download all Python schema files
        blobs = bucket.list_blobs(prefix="generated_schema/python/")
        files_downloaded = 0
        for blob in blobs:
            if not blob.name.endswith("/"):  # Skip directories
                # Get the relative path without the prefix
                relative_path = blob.name.replace("generated_schema/python/", "")
                target_path = os.path.join(target_dir, relative_path)

                # Ensure the directory exists
                os.makedirs(os.path.dirname(target_path), exist_ok=True)

                # Download the file
                blob.download_to_filename(target_path)
                files_downloaded += 1
                print(f"Downloaded: {target_path}")
        # Ensure __init__.py exists in generated_schema/python/
        init_python_path = os.path.join(target_dir, "__init__.py")
        with open(init_python_path, "w") as f:
            f.write("# This file allows the directory to be treated as a Python package.\n")
            f.write("\nimport sys\n")
            f.write("import os\n\n")
            f.write("sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))\n")
        print(f"Created: {init_python_path}")

        # Ensure __init__.py exists in generated_schema/
        init_generated_schema_path = os.path.join(root_dir, "generated_schema", "__init__.py")
        with open(init_generated_schema_path, "w") as f:
            f.write("# This file allows the directory to be treated as a Python package.\n")
            f.write("\nimport sys\n")
            f.write("import os\n\n")
            f.write("sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))\n")
        print(f"Created: {init_generated_schema_path}")
        if files_downloaded == 0:
            print("Warning: No schema files were found in the bucket.", file=sys.stderr)
            sys.exit(1)

        print(f"Successfully downloaded {files_downloaded} schema files.")

    except Exception as e:
        print(f"Error downloading schema: {str(e)}", file=sys.stderr)
        sys.exit(1)

def _create_dags_list(dags_directory: str) -> tuple[str, list[str]]:
    temp_dir = tempfile.mkdtemp()
    try:
        # Copy everything to a temp directory
        copytree(dags_directory, f"{temp_dir}/", dirs_exist_ok=True)

        # Get all Python files in the temp directory, including subdirectories
        dags = glob.glob(f"{temp_dir}/**/*", recursive=True)
        # Filter out directories, keeping only files
        dags = [dag for dag in dags if os.path.isfile(dag)]
    except Exception as e:
        print(f"An error occurred: {e}")
        dags = []
    return (temp_dir, dags)


def upload_dags_to_composer(
    dags_directory: str, bucket_name: str, name_replacement: str
) -> None:
    """
    Given a directory, this function moves all files from that directory
    to a temporary directory, then uploads all contents of the temporary directory
    to a given cloud storage bucket
    Args:
        dags_directory (str): a fully qualified path to a directory that contains a "dags/" subdirectory
        bucket_name (str): the GCS bucket of the Cloud Composer environment to upload DAGs to
        name_replacement (str, optional): the name of the "dags/" subdirectory that will be used when constructing the temporary directory path name Defaults to "dags/".
    """
    temp_dir, dags = _create_dags_list(dags_directory)

    if len(dags) > 0:
        # Note - the GCS client library does not currently support batch requests on uploads
        # if you have a large number of files, consider using
        # the Python subprocess module to run gsutil -m cp -r on your dags
        # See https://cloud.google.com/storage/docs/gsutil/commands/cp for more info
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)

        for dag in dags:
            # Remove path to temp dir
            dag = dag.replace(f"{temp_dir}/", name_replacement)

            try:
                # Upload to your bucket
                blob = bucket.blob(dag)
                blob.upload_from_filename(dag)
                print(f"File {dag} uploaded to {bucket_name}/{dag}.")
            except FileNotFoundError:
                current_directory = os.listdir()
                print(
                    f"{name_replacement} directory not found in {current_directory}, you may need to override the default value of name_replacement to point to a relative directory"
                )
                raise

    else:
        print("No DAGs to upload.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description=__doc__, formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument(
        "--dags_directory",
        help="Relative path to the source directory containing your DAGs",
    )
    parser.add_argument(
        "--dags_bucket",
        help="Name of the DAGs bucket of your Composer environment without the gs:// prefix",
    )

    parser.add_argument(
        "--name_replacement",
        help="Name of the DAGs bucket of your Composer environment without the gs:// prefix",
    )
    parser.add_argument(
        "--environment",
        help="Name of the environment to upload the DAGs to",
    )

    args = parser.parse_args()

    download_schema()

    upload_dags_to_composer(args.dags_directory, args.dags_bucket, args.name_replacement)