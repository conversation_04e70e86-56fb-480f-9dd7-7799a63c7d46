#!/usr/bin/env python3

# This script is used to download the generated schema from GCS to local directory.
# It is used to update the schema in the local environment.

import os
import shutil
import sys

from google.cloud import storage


def download_schema(root_dir: str = "dags/dependencies"):
    """Download the generated schema from GCS to local directory."""
    # Create the target directory if it doesn't exist
    target_dir = os.path.join(root_dir, "generated_schema", "python")
    os.makedirs(target_dir, exist_ok=True)

    try:
        # Initialize GCS client
        client = storage.Client()
        bucket = client.bucket("phia-schema")

        # List and download all Python schema files
        blobs = bucket.list_blobs(prefix="generated_schema/python/")
        files_downloaded = 0
        for blob in blobs:
            if not blob.name.endswith("/"):  # Skip directories
                # Get the relative path without the prefix
                relative_path = blob.name.replace("generated_schema/python/", "")
                target_path = os.path.join(target_dir, relative_path)

                # Ensure the directory exists
                os.makedirs(os.path.dirname(target_path), exist_ok=True)

                # Download the file
                blob.download_to_filename(target_path)
                files_downloaded += 1
                print(f"Downloaded: {target_path}")
        # Ensure __init__.py exists in generated_schema/python/
        init_python_path = os.path.join(target_dir, "__init__.py")
        with open(init_python_path, "w") as f:
            f.write("# This file allows the directory to be treated as a Python package.\n")
            f.write("\nimport sys\n")
            f.write("import os\n\n")
            f.write("sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))\n")
        print(f"Created: {init_python_path}")

        # Ensure __init__.py exists in generated_schema/
        init_generated_schema_path = os.path.join(root_dir, "generated_schema", "__init__.py")
        with open(init_generated_schema_path, "w") as f:
            f.write("# This file allows the directory to be treated as a Python package.\n")
            f.write("\nimport sys\n")
            f.write("import os\n\n")
            f.write("sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))\n")
        print(f"Created: {init_generated_schema_path}")
        if files_downloaded == 0:
            print("Warning: No schema files were found in the bucket.", file=sys.stderr)
            sys.exit(1)

        print(f"Successfully downloaded {files_downloaded} schema files.")

    except Exception as e:
        print(f"Error downloading schema: {str(e)}", file=sys.stderr)
        sys.exit(1)


def copy_schema_to_dags(dags_directory: str, bucket_name: str):
    """Copy the schema files to the dags directory."""
    # Create the target directory if it doesn't exist
    target_dir = os.path.join("dags", "dependencies", "generated_schema", "python")
    os.makedirs(target_dir, exist_ok=True)

if __name__ == "__main__":
    download_schema()
