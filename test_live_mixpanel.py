#!/usr/bin/env python3
"""
Live Mixpanel API Test Script

This script tests the MixpanelService with real Mixpanel credentials
and outputs actual data to validate the implementation.

Usage:
    python test_live_mixpanel.py
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from dags.dependencies.mixpanel.mixpanel_service import MixpanelService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_credentials():
    """Load Mixpanel credentials from .env file."""
    # Load .env file
    load_dotenv()
    
    # Get credentials (note the actual variable names from your .env)
    credentials = {
        'project_id': os.getenv('MIXPANEL_PROJECT_ID'),
        'service_account_username': os.getenv('MIXPANEL_SERVICE_USERNAME'),  # Note: different name in .env
        'service_account_secret': os.getenv('MIXPANEL_SERVICE_SECRET'),      # Note: different name in .env  
        'workspace_id': os.getenv('MIXPANEL_WORKSPACE_ID')
    }
    
    # Validate credentials
    missing = [k for k, v in credentials.items() if not v]
    if missing:
        raise ValueError(f"Missing credentials: {missing}")
    
    logger.info("✓ Successfully loaded Mixpanel credentials from .env")
    logger.info(f"Project ID: {credentials['project_id']}")
    logger.info(f"Workspace ID: {credentials['workspace_id']}")
    
    return credentials

def test_connection(service):
    """Test basic connection to Mixpanel."""
    logger.info("\n" + "="*60)
    logger.info("TESTING MIXPANEL CONNECTION")
    logger.info("="*60)
    
    try:
        if service.test_connection():
            logger.info("✓ Connection to Mixpanel successful!")
            
            # Get service health
            health = service.get_service_health()
            logger.info(f"✓ Service health: {health}")
            return True
        else:
            logger.error("✗ Connection to Mixpanel failed!")
            return False
    except Exception as e:
        logger.error(f"✗ Connection test failed: {str(e)}")
        return False

def test_basic_events(service):
    """Test fetching basic events to see what data exists."""
    logger.info("\n" + "="*60)
    logger.info("TESTING BASIC EVENTS")
    logger.info("="*60)
    
    # Test date range: last 7 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    from_date = start_date.strftime('%Y-%m-%d')
    to_date = end_date.strftime('%Y-%m-%d')
    
    logger.info(f"Testing date range: {from_date} to {to_date}")
    
    try:
        # Test basic events data call
        events_data = service.client.get_events_data(
            events=["phia_clicked", "phia_shown", "heartbeat", "page_view"],
            from_date=from_date,
            to_date=to_date,
            event_type="general",
            unit="day"
        )
        
        if events_data:
            logger.info("✓ Successfully fetched events data!")
            logger.info(f"Events data structure: {json.dumps(events_data, indent=2)}")
            
            # Check which events have data
            if "data" in events_data and "values" in events_data["data"]:
                for event, daily_data in events_data["data"]["values"].items():
                    total_events = sum(daily_data.values()) if daily_data else 0
                    logger.info(f"  {event}: {total_events} total events")
            
            return events_data
        else:
            logger.warning("✗ No events data returned")
            return None
            
    except Exception as e:
        logger.error(f"✗ Failed to fetch events data: {str(e)}")
        return None

def test_user_profiles(service):
    """Test fetching user profiles."""
    logger.info("\n" + "="*60)
    logger.info("TESTING USER PROFILES")
    logger.info("="*60)
    
    try:
        # Get a small sample of user profiles
        profiles_data = service.client.get_user_profiles(
            output_properties=["$email", "$first_seen", "$last_seen", "phia_id"],
            page=0
        )
        
        if profiles_data:
            logger.info("✓ Successfully fetched user profiles!")
            logger.info(f"Total users: {profiles_data.get('total', 0)}")
            logger.info(f"Page size: {profiles_data.get('page_size', 0)}")
            logger.info(f"Results count: {len(profiles_data.get('results', []))}")
            
            # Show first few users (without sensitive data)
            results = profiles_data.get('results', [])
            for i, user in enumerate(results[:3]):
                user_id = user.get('$distinct_id', 'Unknown')
                props = user.get('$properties', {})
                first_seen = props.get('$first_seen', 'Unknown')
                logger.info(f"  User {i+1}: ID={user_id}, First seen={first_seen}")
            
            return profiles_data
        else:
            logger.warning("✗ No user profiles returned")
            return None
            
    except Exception as e:
        logger.error(f"✗ Failed to fetch user profiles: {str(e)}")
        return None

def test_segmentation(service):
    """Test segmentation API with real data."""
    logger.info("\n" + "="*60)
    logger.info("TESTING SEGMENTATION")
    logger.info("="*60)
    
    # Test date range: last 7 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    from_date = start_date.strftime('%Y-%m-%d')
    to_date = end_date.strftime('%Y-%m-%d')
    
    try:
        # Test segmentation by platform for phia_clicked
        segmentation_data = service.client.get_segmentation_data(
            event="phia_clicked",
            from_date=from_date,
            to_date=to_date,
            segment_property="properties.platform",
            event_type="general",
            unit="day",
            limit=20
        )
        
        if segmentation_data:
            logger.info("✓ Successfully fetched segmentation data!")
            logger.info(f"Segmentation structure: {json.dumps(segmentation_data, indent=2)}")
            
            # Show platform breakdown
            if "data" in segmentation_data and "values" in segmentation_data["data"]:
                logger.info("Platform breakdown:")
                for platform, daily_data in segmentation_data["data"]["values"].items():
                    total_events = sum(daily_data.values()) if daily_data else 0
                    logger.info(f"  {platform}: {total_events} events")
            
            return segmentation_data
        else:
            logger.warning("✗ No segmentation data returned")
            return None
            
    except Exception as e:
        logger.error(f"✗ Failed to fetch segmentation data: {str(e)}")
        return None

def test_site_activation_metrics(service):
    """Test the actual site activation metrics from our service."""
    logger.info("\n" + "="*60)
    logger.info("TESTING SITE ACTIVATION METRICS")
    logger.info("="*60)
    
    # Test date range: last 14 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=14)
    from_date = start_date.strftime('%Y-%m-%d')
    to_date = end_date.strftime('%Y-%m-%d')
    
    try:
        metrics = service.get_site_activation_metrics(from_date, to_date, limit=10)
        
        logger.info(f"✓ Successfully fetched site activation metrics!")
        logger.info(f"Found {len(metrics)} sites with metrics")
        
        for i, metric in enumerate(metrics[:5]):  # Show top 5
            logger.info(f"\n  Site {i+1}: {metric.site_domain}")
            logger.info(f"    Phia Shown: {metric.phia_shown_count}")
            logger.info(f"    Phia Clicked: {metric.phia_clicked_count}")
            logger.info(f"    Click Rate: {metric.click_rate}%")
            logger.info(f"    Mobile Extension Shown: {metric.mobile_extension_shown}")
            logger.info(f"    Mobile Extension Activated: {metric.mobile_extension_activated}")
            logger.info(f"    Mobile Activation Rate: {metric.mobile_activation_rate}%")
            logger.info(f"    Combined Activation Rate: {metric.combined_activation_rate}%")
        
        return metrics
        
    except Exception as e:
        logger.error(f"✗ Failed to fetch site activation metrics: {str(e)}")
        return None

def test_comprehensive_analytics(service):
    """Test the comprehensive analytics method."""
    logger.info("\n" + "="*60)
    logger.info("TESTING COMPREHENSIVE ANALYTICS")
    logger.info("="*60)
    
    # Test date range: last 14 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=14)
    from_date = start_date.strftime('%Y-%m-%d')
    to_date = end_date.strftime('%Y-%m-%d')
    
    try:
        analytics = service.get_comprehensive_analytics(
            from_date=from_date,
            to_date=to_date,
            cohort_start_date="2024-06-19",
            cohort_end_date="2024-06-25",
            analysis_weeks=4
        )
        
        if analytics['success']:
            logger.info("✓ Successfully fetched comprehensive analytics!")
            
            data = analytics['data']
            
            # Site activation summary
            site_metrics = data.get('site_activation_metrics', [])
            logger.info(f"\n📊 SITE ACTIVATION METRICS ({len(site_metrics)} sites):")
            for i, site in enumerate(site_metrics[:3]):
                logger.info(f"  {i+1}. {site['site_domain']}: {site['combined_activation_rate']}% activation rate")
            
            # Permission funnel summary
            funnel = data.get('permission_funnel_metrics', {})
            logger.info(f"\n🔐 PERMISSION FUNNEL:")
            logger.info(f"  Enable Phia Views: {funnel.get('enable_phia_views', 0)}")
            logger.info(f"  Almost Finished Views: {funnel.get('almost_finished_views', 0)}")
            logger.info(f"  Conversion Rate: {funnel.get('conversion_rate', 0)}%")
            
            # Safari extension summary
            safari = data.get('safari_extension_metrics', {})
            logger.info(f"\n🦁 SAFARI EXTENSION:")
            logger.info(f"  Active Users: {safari.get('active_users', 0)}")
            logger.info(f"  Total Clicks: {safari.get('total_clicks', 0)}")
            
            # Weekly active users summary
            weekly = data.get('weekly_active_users_metrics', {})
            logger.info(f"\n📈 WEEKLY ACTIVE USERS:")
            logger.info(f"  Total Active Users: {weekly.get('total_active_users', 0)}")
            logger.info(f"  Heartbeat Events: {weekly.get('heartbeat_events', 0)}")
            
            # OS breakdown
            os_breakdown = weekly.get('os_breakdown', {})
            if os_breakdown:
                logger.info("  OS Breakdown:")
                for os, count in os_breakdown.items():
                    logger.info(f"    {os}: {count}")
            
            # Retention summary
            retention = data.get('weekly_retention_metrics', {})
            logger.info(f"\n📊 WEEKLY RETENTION:")
            logger.info(f"  Cohort Size: {retention.get('initial_cohort_size', 0)}")
            logger.info(f"  Cohort Period: {retention.get('cohort_start_date')} to {retention.get('cohort_end_date')}")
            
            # Save full results
            output_file = f"live_mixpanel_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w') as f:
                json.dump(analytics, f, indent=2, default=str)
            logger.info(f"\n💾 Full results saved to: {output_file}")
            
            return analytics
        else:
            logger.error(f"✗ Comprehensive analytics failed: {analytics.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        logger.error(f"✗ Failed to fetch comprehensive analytics: {str(e)}")
        return None

def main():
    """Main test function."""
    logger.info("🚀 Starting Live Mixpanel API Test")
    logger.info("="*80)
    
    try:
        # Load credentials
        credentials = load_credentials()
        
        # Initialize service
        service = MixpanelService(
            project_id=credentials['project_id'],
            service_account_username=credentials['service_account_username'],
            service_account_secret=credentials['service_account_secret'],
            workspace_id=credentials['workspace_id'],
            respect_rate_limits=True  # Enable rate limiting for production
        )
        
        # Run tests in sequence
        tests = [
            ("Connection Test", lambda: test_connection(service)),
            ("Basic Events Test", lambda: test_basic_events(service)),
            ("User Profiles Test", lambda: test_user_profiles(service)),
            ("Segmentation Test", lambda: test_segmentation(service)),
            ("Site Activation Metrics Test", lambda: test_site_activation_metrics(service)),
            ("Comprehensive Analytics Test", lambda: test_comprehensive_analytics(service))
        ]
        
        results = {}
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Running {test_name}...")
            try:
                result = test_func()
                results[test_name] = "✓ PASSED" if result is not None else "✗ FAILED"
            except Exception as e:
                logger.error(f"✗ {test_name} failed with exception: {str(e)}")
                results[test_name] = f"✗ FAILED: {str(e)}"
        
        # Final summary
        logger.info("\n" + "="*80)
        logger.info("📋 TEST SUMMARY")
        logger.info("="*80)
        for test_name, result in results.items():
            logger.info(f"{test_name:<30}: {result}")
        
        passed_tests = sum(1 for result in results.values() if result.startswith("✓"))
        total_tests = len(results)
        logger.info(f"\n🎯 Overall: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            logger.info("🎉 All tests passed! Your Mixpanel integration is working!")
        else:
            logger.warning("⚠️  Some tests failed. Check the logs above for details.")
        
    except Exception as e:
        logger.error(f"💥 Test suite failed: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    main()