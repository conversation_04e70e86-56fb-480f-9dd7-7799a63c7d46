"""
Comprehensive test suite for MixpanelService.

This test file validates all functionality and outputs the requested data
for the Phia analytics dashboard.

Test Coverage:
1. Service initialization and connection
2. Site activation metrics
3. Permission funnel metrics
4. Safari extension metrics
5. Weekly active users metrics
6. Weekly retention metrics
7. Comprehensive analytics
8. Error handling and edge cases

Usage:
    python -m pytest tests/test_mixpanel_service.py -v
    python tests/test_mixpanel_service.py  # For direct execution
"""

import os
import json
import logging
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any
import sys

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from dags.dependencies.mixpanel.mixpanel_service import (
    MixpanelService,
    MixpanelServiceError,
    SiteActivationMetrics,
    PermissionFunnelMetrics,
    SafariExtensionMetrics,
    WeeklyActiveUsersMetrics,
    WeeklyRetentionMetrics
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestMixpanelService(unittest.TestCase):
    """Comprehensive test suite for MixpanelService."""

    def setUp(self):
        """Set up test fixtures."""
        # Test configuration
        self.test_config = {
            "project_id": "test_project_123",
            "service_account_username": "test_username",
            "service_account_secret": "test_secret",
            "workspace_id": "test_workspace_456",
            "respect_rate_limits": False  # Disable for testing
        }
        
        # Test date ranges
        self.test_from_date = "2024-06-01"
        self.test_to_date = "2024-06-30"
        self.cohort_start_date = "2024-06-19"
        self.cohort_end_date = "2024-06-25"
        
        # Mock data for different API responses
        self.mock_segmentation_response = {
            "data": {
                "series": ["2024-06-01", "2024-06-02", "2024-06-03"],
                "values": {
                    "example.com": {
                        "2024-06-01": 100,
                        "2024-06-02": 150,
                        "2024-06-03": 120
                    },
                    "test.com": {
                        "2024-06-01": 80,
                        "2024-06-02": 90,
                        "2024-06-03": 85
                    }
                }
            }
        }
        
        self.mock_events_response = {
            "data": {
                "series": ["2024-06-01", "2024-06-02", "2024-06-03"],
                "values": {
                    "phia_clicked": {
                        "2024-06-01": 500,
                        "2024-06-02": 750,
                        "2024-06-03": 600
                    },
                    "heartbeat": {
                        "2024-06-01": 1000,
                        "2024-06-02": 1200,
                        "2024-06-03": 1100
                    }
                }
            }
        }
        
        self.mock_user_profiles_response = {
            "page": 0,
            "page_size": 1000,
            "total": 150,
            "results": [
                {
                    "$distinct_id": "user_001",
                    "$properties": {
                        "$first_seen": "2024-06-19T10:00:00",
                        "$last_seen": "2024-06-25T15:30:00",
                        "phia_id": "phia_001",
                        "permission_activated": True
                    }
                },
                {
                    "$distinct_id": "user_002",
                    "$properties": {
                        "$first_seen": "2024-06-20T11:00:00",
                        "$last_seen": "2024-06-26T16:30:00",
                        "phia_id": "phia_002",
                        "permission_activated": True
                    }
                }
            ]
        }

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_service_initialization(self, mock_client_class):
        """Test MixpanelService initialization."""
        logger.info("Testing service initialization...")
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        mock_client.test_connection.return_value = True
        
        service = MixpanelService(**self.test_config)
        
        # Verify service attributes
        self.assertEqual(service.project_id, self.test_config["project_id"])
        self.assertEqual(service.workspace_id, self.test_config["workspace_id"])
        self.assertEqual(service.max_retries, 3)
        self.assertIsNotNone(service.client)
        
        # Test connection
        self.assertTrue(service.test_connection())
        
        logger.info("✓ Service initialization test passed")

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_site_activation_metrics(self, mock_client_class):
        """Test site activation metrics functionality."""
        logger.info("Testing site activation metrics...")
        
        # Set up mock client
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Mock API responses for different events
        phia_shown_response = {
            "data": {
                "values": {
                    "example.com": {"2024-06-01": 200, "2024-06-02": 250},
                    "test.com": {"2024-06-01": 150, "2024-06-02": 180}
                }
            }
        }
        
        phia_clicked_response = {
            "data": {
                "values": {
                    "example.com": {"2024-06-01": 100, "2024-06-02": 120},
                    "test.com": {"2024-06-01": 75, "2024-06-02": 90}
                }
            }
        }
        
        mobile_shown_response = {
            "data": {
                "values": {
                    "example.com": {"2024-06-01": 50, "2024-06-02": 60},
                    "test.com": {"2024-06-01": 40, "2024-06-02": 45}
                }
            }
        }
        
        mobile_activated_response = {
            "data": {
                "values": {
                    "example.com": {"2024-06-01": 25, "2024-06-02": 35},
                    "test.com": {"2024-06-01": 20, "2024-06-02": 25}
                }
            }
        }
        
        # Configure mock to return different responses for different events
        def mock_get_segmentation_side_effect(event, **kwargs):
            if event == "phia_shown":
                return phia_shown_response
            elif event == "phia_clicked":
                return phia_clicked_response
            elif event == "resale_insights_activate_shown":
                return mobile_shown_response
            elif event == "resale_insights_activated":
                return mobile_activated_response
            return None
        
        mock_client.get_segmentation_data.side_effect = mock_get_segmentation_side_effect
        
        # Test the service
        service = MixpanelService(**self.test_config)
        metrics = service.get_site_activation_metrics(self.test_from_date, self.test_to_date)
        
        # Verify results
        self.assertIsInstance(metrics, list)
        self.assertEqual(len(metrics), 2)  # Two sites
        
        for metric in metrics:
            self.assertIsInstance(metric, SiteActivationMetrics)
            self.assertIn(metric.site_domain, ["example.com", "test.com"])
            self.assertGreater(metric.phia_shown_count, 0)
            self.assertGreater(metric.phia_clicked_count, 0)
            self.assertGreaterEqual(metric.click_rate, 0)
            self.assertGreaterEqual(metric.mobile_activation_rate, 0)
        
        # Check specific calculations for example.com
        example_metric = next(m for m in metrics if m.site_domain == "example.com")
        self.assertEqual(example_metric.phia_shown_count, 450)  # 200 + 250
        self.assertEqual(example_metric.phia_clicked_count, 220)  # 100 + 120
        self.assertEqual(example_metric.mobile_extension_shown, 110)  # 50 + 60
        self.assertEqual(example_metric.mobile_extension_activated, 60)  # 25 + 35
        
        logger.info("✓ Site activation metrics test passed")
        logger.info(f"✓ Found metrics for {len(metrics)} sites")

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_permission_funnel_metrics(self, mock_client_class):
        """Test permission funnel metrics functionality."""
        logger.info("Testing permission funnel metrics...")
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Mock responses for funnel steps
        enable_phia_response = {
            "data": {
                "values": {
                    "/mobile/enable-phia": {
                        "2024-06-01": 500,
                        "2024-06-02": 600,
                        "2024-06-03": 550
                    }
                }
            }
        }
        
        almost_finished_response = {
            "data": {
                "values": {
                    "/mobile/almost-finished": {
                        "2024-06-01": 300,
                        "2024-06-02": 360,
                        "2024-06-03": 330
                    }
                }
            }
        }
        
        def mock_get_segmentation_side_effect(event, **kwargs):
            where_filter = kwargs.get('where_filter', '')
            if 'enable-phia' in where_filter:
                return enable_phia_response
            elif 'almost-finished' in where_filter:
                return almost_finished_response
            return None
        
        mock_client.get_segmentation_data.side_effect = mock_get_segmentation_side_effect
        
        # Test the service
        service = MixpanelService(**self.test_config)
        metrics = service.get_permission_funnel_metrics(self.test_from_date, self.test_to_date)
        
        # Verify results
        self.assertIsInstance(metrics, PermissionFunnelMetrics)
        self.assertEqual(metrics.enable_phia_views, 1650)  # 500 + 600 + 550
        self.assertEqual(metrics.almost_finished_views, 990)  # 300 + 360 + 330
        self.assertEqual(metrics.conversion_rate, 60.0)  # 990/1650 * 100
        
        logger.info("✓ Permission funnel metrics test passed")
        logger.info(f"✓ Conversion rate: {metrics.conversion_rate}%")

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_safari_extension_metrics(self, mock_client_class):
        """Test Safari extension metrics functionality."""
        logger.info("Testing Safari extension metrics...")
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Mock platform breakdown response
        platform_response = {
            "data": {
                "values": {
                    "IOS_SAFARI_EXTENSION": {
                        "2024-06-01": 150,
                        "2024-06-02": 180,
                        "2024-06-03": 165
                    },
                    "CHROME_EXTENSION": {
                        "2024-06-01": 300,
                        "2024-06-02": 350,
                        "2024-06-03": 320
                    }
                }
            }
        }
        
        mock_client.get_segmentation_data.return_value = platform_response
        mock_client.get_events_data.return_value = self.mock_events_response
        
        # Test the service
        service = MixpanelService(**self.test_config)
        metrics = service.get_safari_extension_metrics(self.test_from_date, self.test_to_date)
        
        # Verify results
        self.assertIsInstance(metrics, SafariExtensionMetrics)
        self.assertEqual(metrics.active_users, 495)  # 150 + 180 + 165
        self.assertIn("IOS_SAFARI_EXTENSION", metrics.platform_breakdown)
        self.assertIn("CHROME_EXTENSION", metrics.platform_breakdown)
        
        logger.info("✓ Safari extension metrics test passed")
        logger.info(f"✓ Safari extension active users: {metrics.active_users}")

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_weekly_active_users_metrics(self, mock_client_class):
        """Test weekly active users metrics functionality."""
        logger.info("Testing weekly active users metrics...")
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Mock OS breakdown response
        os_breakdown_response = {
            "data": {
                "values": {
                    "iOS": {
                        "2024-06-01": 600,
                        "2024-06-02": 650
                    },
                    "Android": {
                        "2024-06-01": 400,
                        "2024-06-02": 450
                    },
                    "macOS": {
                        "2024-06-01": 300,
                        "2024-06-02": 320
                    }
                }
            }
        }
        
        permission_breakdown_response = {
            "data": {
                "values": {
                    "All": {
                        "2024-06-01": 1000,
                        "2024-06-02": 1100
                    },
                    "Partial": {
                        "2024-06-01": 200,
                        "2024-06-02": 180
                    }
                }
            }
        }
        
        def mock_get_segmentation_side_effect(event, segment_property, **kwargs):
            if "Operating System" in segment_property:
                return os_breakdown_response
            elif "permission_state" in segment_property:
                return permission_breakdown_response
            return None
        
        mock_client.get_segmentation_data.side_effect = mock_get_segmentation_side_effect
        mock_client.get_events_data.return_value = self.mock_events_response
        
        # Test the service
        service = MixpanelService(**self.test_config)
        metrics = service.get_weekly_active_users_metrics(self.test_from_date, self.test_to_date)
        
        # Verify results
        self.assertIsInstance(metrics, WeeklyActiveUsersMetrics)
        self.assertGreater(metrics.total_active_users, 0)
        self.assertIn("iOS", metrics.os_breakdown)
        self.assertIn("Android", metrics.os_breakdown)
        self.assertIn("All", metrics.permission_state_breakdown)
        
        logger.info("✓ Weekly active users metrics test passed")
        logger.info(f"✓ Total active users: {metrics.total_active_users}")

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_weekly_retention_metrics(self, mock_client_class):
        """Test weekly retention metrics functionality."""
        logger.info("Testing weekly retention metrics...")
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Mock user profiles response for cohort
        mock_client.get_user_profiles.return_value = self.mock_user_profiles_response
        mock_client.get_events_data.return_value = self.mock_events_response
        
        # Test the service
        service = MixpanelService(**self.test_config)
        metrics = service.get_weekly_retention_metrics(
            self.cohort_start_date, 
            self.cohort_end_date,
            analysis_weeks=4
        )
        
        # Verify results
        self.assertIsInstance(metrics, WeeklyRetentionMetrics)
        self.assertEqual(metrics.cohort_start_date, self.cohort_start_date)
        self.assertEqual(metrics.cohort_end_date, self.cohort_end_date)
        self.assertEqual(metrics.initial_cohort_size, 2)  # Two users in mock data
        self.assertEqual(len(metrics.weekly_retention_rates), 4)  # 4 weeks
        
        # Check week structure
        for week_key, week_data in metrics.weekly_retention_rates.items():
            self.assertIn("week_start", week_data)
            self.assertIn("week_end", week_data)
            self.assertIn("heartbeat_retention_rate", week_data)
            self.assertIn("phia_clicked_retention_rate", week_data)
        
        logger.info("✓ Weekly retention metrics test passed")
        logger.info(f"✓ Cohort size: {metrics.initial_cohort_size}")

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_comprehensive_analytics(self, mock_client_class):
        """Test comprehensive analytics functionality."""
        logger.info("Testing comprehensive analytics...")
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Set up mock responses for all API calls
        mock_client.get_segmentation_data.return_value = self.mock_segmentation_response
        mock_client.get_events_data.return_value = self.mock_events_response
        mock_client.get_user_profiles.return_value = self.mock_user_profiles_response
        
        # Test the service
        service = MixpanelService(**self.test_config)
        analytics = service.get_comprehensive_analytics(
            from_date=self.test_from_date,
            to_date=self.test_to_date,
            cohort_start_date=self.cohort_start_date,
            cohort_end_date=self.cohort_end_date
        )
        
        # Verify comprehensive results structure
        self.assertIsInstance(analytics, dict)
        self.assertTrue(analytics["success"])
        self.assertIn("data", analytics)
        self.assertIn("timestamp", analytics)
        
        # Verify all required metrics are present
        data = analytics["data"]
        self.assertIn("site_activation_metrics", data)
        self.assertIn("permission_funnel_metrics", data)
        self.assertIn("safari_extension_metrics", data)
        self.assertIn("weekly_active_users_metrics", data)
        self.assertIn("weekly_retention_metrics", data)
        
        logger.info("✓ Comprehensive analytics test passed")
        logger.info(f"✓ Analytics data contains {len(data)} metric types")

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_error_handling(self, mock_client_class):
        """Test error handling functionality."""
        logger.info("Testing error handling...")
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Test API failure
        mock_client.get_segmentation_data.side_effect = Exception("API Error")
        
        service = MixpanelService(**self.test_config)
        
        # Should raise MixpanelServiceError after retries
        with self.assertRaises(MixpanelServiceError):
            service.get_site_activation_metrics(self.test_from_date, self.test_to_date)
        
        # Test invalid date range
        with self.assertRaises(MixpanelServiceError):
            service.get_site_activation_metrics("2024-06-30", "2024-06-01")  # Invalid range
        
        logger.info("✓ Error handling test passed")

    @patch('dags.dependencies.mixpanel.mixpanel_service.MixpanelClient')
    def test_service_health(self, mock_client_class):
        """Test service health functionality."""
        logger.info("Testing service health...")
        
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        mock_client.test_connection.return_value = True
        mock_client.request_count = 42
        
        service = MixpanelService(**self.test_config)
        health = service.get_service_health()
        
        # Verify health response structure
        self.assertIsInstance(health, dict)
        self.assertIn("service_name", health)
        self.assertIn("connection_healthy", health)
        self.assertIn("api_calls_made", health)
        self.assertTrue(health["connection_healthy"])
        
        logger.info("✓ Service health test passed")


def run_live_test_with_real_data():
    """
    Run a live test with real Mixpanel data (if credentials are available).
    
    This function demonstrates how to use the service with real data
    and outputs the actual requested analytics.
    """
    logger.info("=" * 60)
    logger.info("RUNNING LIVE TEST WITH REAL DATA")
    logger.info("=" * 60)
    
    # Check for environment variables with real credentials
    project_id = os.getenv("MIXPANEL_PROJECT_ID")
    username = os.getenv("MIXPANEL_SERVICE_ACCOUNT_USERNAME")
    secret = os.getenv("MIXPANEL_SERVICE_ACCOUNT_SECRET")
    workspace_id = os.getenv("MIXPANEL_WORKSPACE_ID")
    
    if not all([project_id, username, secret]):
        logger.warning("Real Mixpanel credentials not found in environment variables.")
        logger.warning("Set MIXPANEL_PROJECT_ID, MIXPANEL_SERVICE_ACCOUNT_USERNAME, and MIXPANEL_SERVICE_ACCOUNT_SECRET to run live tests.")
        return
    
    try:
        # Initialize service with real credentials
        service = MixpanelService(
            project_id=project_id,
            service_account_username=username,
            service_account_secret=secret,
            workspace_id=workspace_id,
            respect_rate_limits=True
        )
        
        # Test connection
        logger.info("Testing connection to Mixpanel...")
        if not service.test_connection():
            logger.error("Failed to connect to Mixpanel with provided credentials")
            return
        
        logger.info("✓ Successfully connected to Mixpanel")
        
        # Define test date ranges
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        cohort_start = "2024-06-19"
        cohort_end = "2024-06-25"
        
        from_date = start_date.strftime("%Y-%m-%d")
        to_date = end_date.strftime("%Y-%m-%d")
        
        logger.info(f"Fetching analytics for date range: {from_date} to {to_date}")
        
        # Get comprehensive analytics
        analytics = service.get_comprehensive_analytics(
            from_date=from_date,
            to_date=to_date,
            cohort_start_date=cohort_start,
            cohort_end_date=cohort_end
        )
        
        if analytics["success"]:
            logger.info("✓ Successfully retrieved comprehensive analytics")
            
            # Output the requested data in a formatted way
            print("\n" + "=" * 80)
            print("PHIA ANALYTICS DASHBOARD DATA")
            print("=" * 80)
            
            data = analytics["data"]
            
            # 1. Site Activation Metrics
            print("\n1. NEW SITE ACTIVATION RATE BROKEN DOWN BY SITE:")
            print("-" * 50)
            site_metrics = data.get("site_activation_metrics", [])
            for i, site in enumerate(site_metrics[:10]):  # Top 10 sites
                print(f"{i+1:2d}. {site['site_domain']:<25} | "
                      f"Shown: {site['phia_shown_count']:>6} | "
                      f"Clicked: {site['phia_clicked_count']:>6} | "
                      f"Click Rate: {site['click_rate']:>6.2f}% | "
                      f"Mobile Rate: {site['mobile_activation_rate']:>6.2f}%")
            
            # 2. Permission Funnel
            print("\n2. ENABLED PERMISSIONS % (FIRST TIME RUN):")
            print("-" * 50)
            funnel = data.get("permission_funnel_metrics", {})
            print(f"Enable Phia Views:      {funnel.get('enable_phia_views', 0):>8}")
            print(f"Almost Finished Views:  {funnel.get('almost_finished_views', 0):>8}")
            print(f"Conversion Rate:        {funnel.get('conversion_rate', 0):>7.2f}%")
            
            # 3. Safari Extension Metrics
            print("\n3. WEEKLY ACTIVE SAFARI EXTENSION USERS:")
            print("-" * 50)
            safari = data.get("safari_extension_metrics", {})
            print(f"Active Safari Users:    {safari.get('active_users', 0):>8}")
            print(f"Total Clicks:           {safari.get('total_clicks', 0):>8}")
            print(f"Platform Breakdown:")
            for platform, count in safari.get('platform_breakdown', {}).items():
                print(f"  {platform:<20}: {count:>8}")
            
            # 4. Weekly Active Users
            print("\n4. WEEKLY ACTIVE USERS SINCE LAUNCH:")
            print("-" * 50)
            weekly = data.get("weekly_active_users_metrics", {})
            print(f"Total Active Users:     {weekly.get('total_active_users', 0):>8}")
            print(f"Heartbeat Events:       {weekly.get('heartbeat_events', 0):>8}")
            print(f"OS Breakdown:")
            for os, count in weekly.get('os_breakdown', {}).items():
                print(f"  {os:<20}: {count:>8}")
            
            # 5. Weekly Retention
            print("\n5. WEEKLY RETENTION METRICS:")
            print("-" * 50)
            retention = data.get("weekly_retention_metrics", {})
            print(f"Cohort Period:          {retention.get('cohort_start_date')} to {retention.get('cohort_end_date')}")
            print(f"Initial Cohort Size:    {retention.get('initial_cohort_size', 0):>8}")
            print(f"Weekly Retention Rates:")
            for week, data_point in retention.get('weekly_retention_rates', {}).items():
                print(f"  {week:<15}: Heartbeat {data_point.get('heartbeat_retention_rate', 0):>6.2f}% | "
                      f"Phia Clicked {data_point.get('phia_clicked_retention_rate', 0):>6.2f}%")
            
            print("\n" + "=" * 80)
            
            # Save full results to file
            output_file = f"mixpanel_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w') as f:
                json.dump(analytics, f, indent=2, default=str)
            
            logger.info(f"✓ Full analytics data saved to: {output_file}")
            
        else:
            logger.error(f"Failed to retrieve analytics: {analytics.get('error', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"Live test failed: {str(e)}")


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run all unit tests
    print("Running MixpanelService Unit Tests...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestMixpanelService)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Summary
    print("\n" + "=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.wasSuccessful():
        print("✓ All unit tests passed!")
        
        # Ask if user wants to run live test
        response = input("\nWould you like to run the live test with real Mixpanel data? (y/N): ")
        if response.lower() == 'y':
            run_live_test_with_real_data()
    else:
        print("✗ Some tests failed")
        
    print("\nTest execution completed.")