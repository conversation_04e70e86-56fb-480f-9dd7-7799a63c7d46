import requests
from datetime import datetime, UTC
import hmac
import hashlib
import pandas as pd
import logging
from typing import Dict, List
from .common import (
    WILDFIRE_NETWORK_RANK, 
    WILDFIRE_NETWORK_NAME,
    DEFAULT_COOKIE_DURATION,
    DEVICE_DESKTOP,
    DEVICE_MOBILE,
    DEVICE_BOTH
)

WILDFIRE_APPS ={
    'ADMIN': {
        'app_id': 448,
        'app_secret': "?Y(g<)&3L0I5y#6Tc~2VXB-{@sk48179",
        'device_key': '42ca516040e3dffe01d5223fd17af8e5bae394'
    },
    DEVICE_DESKTOP: {
        'app_id': 449,
        'app_secret': "#n2p9P)=7E0{gS(hR&183L^5<aN@46x_",
        'device_key': 'b0692110fa98ac3e16c537c1370e0e9078c109'
    },
    DEVICE_MOBILE: {
        'app_id': 453,
        'app_secret': "L)J{>~49u8m&=H37R[6(Wj0<5h1s_fM2",
        'device_key': '321907f813838241ed2a4bf49bf35529148cb6'
    }
}

logger = logging.getLogger(__name__)

AOV: float = 100.0  # Average Order Value for flat rates

def fetch_json(url: str) -> List[Dict]:
    logger.info(f"Fetching JSON from URL: {url}")
    resp = requests.get(url)
    resp.raise_for_status()
    return resp.json()

def calculate_commission_rate(max_rate: Dict) -> float:
    if max_rate['Kind'] == 'PERCENTAGE':
        return float(max_rate['Amount'])
    else:
        return (float(max_rate['Amount']) / AOV) * 100

def get_device_type(app_id: int) -> str:
    if app_id == WILDFIRE_APPS[DEVICE_DESKTOP]['app_id']:
        return DEVICE_DESKTOP
    elif app_id == WILDFIRE_APPS[DEVICE_MOBILE]['app_id']:
        return DEVICE_MOBILE
    else:
        return DEVICE_BOTH

def get_device(device_type: str) -> Dict:
    if device_type == DEVICE_DESKTOP:
        app_id = WILDFIRE_APPS[DEVICE_DESKTOP]['app_id']
        app_secret = WILDFIRE_APPS[DEVICE_DESKTOP]['app_secret']
        device_key = WILDFIRE_APPS[DEVICE_DESKTOP]['device_key']
    elif device_type == DEVICE_MOBILE:
        app_id = WILDFIRE_APPS[DEVICE_MOBILE]['app_id']
        app_secret = WILDFIRE_APPS[DEVICE_MOBILE]['app_secret']
        device_key = WILDFIRE_APPS[DEVICE_MOBILE]['device_key']
    else:
        app_id = WILDFIRE_APPS['ADMIN']['app_id']
        app_secret = WILDFIRE_APPS['ADMIN']['app_secret']
        device_key = WILDFIRE_APPS['ADMIN']['device_key']

    wf_time = datetime.now(UTC).isoformat(timespec='milliseconds').replace('+00:00', 'Z')
    device_token = '' # empty because not auth'd
    sender_token = '' # empty because not auth'd
    string_to_sign = "\n".join([wf_time, device_token, sender_token]) + "\n"
    app_signature = hmac.new(
        app_secret.encode('utf-8'),
        string_to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    authorization = f"WFAV1 {app_id}:{app_signature}:{device_token}:{sender_token}"

    headers = {
        'Content-Type': 'application/json',
        'Authorization': authorization,
        'X-WF-DateTime': wf_time,
    }
    body = {
        'DeviceKey': device_key,
    }

    resp = requests.post('https://api.wfi.re/v3/device', headers=headers, json=body)

    resp.raise_for_status()

    resp_data = resp.json()
    print(device_type + " device: " + resp_data['DeviceKey'])
    return {
        'device_id': resp_data['DeviceID'],
        'device_key': resp_data['DeviceKey'],
        'device_token': resp_data['DeviceToken'],
        'uuid': resp_data['UUID']
    }

def load_wildfire_affiliates(**kwargs) -> pd.DataFrame:
    logger.info("Starting Wildfire affiliate data load")
    application_ids: List[int] = [WILDFIRE_APPS[DEVICE_DESKTOP]['app_id'], WILDFIRE_APPS[DEVICE_MOBILE]['app_id']]
    all_rows = []

    for app_id in application_ids:
        device_type = get_device_type(app_id)
        device_id = get_device(device_type)['device_id']

        domain_url: str = f"https://www.wildlink.me/data/{app_id}/active-domain/1"
        merchant_url: str = f"https://www.wildlink.me/data/{app_id}/merchant/1"

        domains: List[Dict] = fetch_json(domain_url)
        merchants: Dict = {m['ID']: m for m in fetch_json(merchant_url)}
        logger.info(f"Fetched {len(domains)} domains and {len(merchants)} merchants for app_id {app_id}")

        for d in domains:
            m = merchants.get(d['Merchant']['ID'])
            if not m:
                continue
            max_rate = d['Merchant']['MaxRate']
            commission = calculate_commission_rate(max_rate)

            all_rows.append({
                'networkRank': WILDFIRE_NETWORK_RANK,
                'networkName': WILDFIRE_NETWORK_NAME,
                'AdvertiserId': f"wildfire_{app_id}_{m['ID']}",
                'Name': m['Name'],
                'Website': m.get('URL'),
                'domain': d['Domain'],
                'commissionRateMax': commission,
                'evergreenUrl': f"https://wild.link/e?c={m['ID']}&d={device_id}" + "&url={{encoded_url}}&tc={{phia_merchant_product_url_id}}",
                'evergreenUrlOverride': None,
                'cookieDurationHours': DEFAULT_COOKIE_DURATION,
                'evergreenUrlLastTestedAt': None,
                'evergreenUrlTestStatus': None,
                'device': device_type
            })
    
    df = pd.DataFrame(all_rows)
    logger.info(f"Transformed data, shape: {df.shape}")
    return df 