CREATE OR REPLACE TABLE {{ params.affiliate_table_id }} AS
WITH ranked_networks AS (
    SELECT 
        'eBay Partner Network' AS network_name, 1 AS networkRank
    UNION ALL
    SELECT 
        'Impact.com', 2
    UNION ALL
    SELECT 
        'Rakuten Advertising', 3
    UNION ALL
    SELECT 
        'CJ Affiliate', 4
    UNION ALL
    SELECT 
        'Awin', 5
    UNION ALL
    SELECT 
        'Partnerize', 6
    UNION ALL
    SELECT 
        'Ascend by Partnerize', 7
    UNION ALL
    SELECT 
        'ShareASale', 8
    UNION ALL
    SELECT 
        'FlexOffers', 9
    UNION ALL
    SELECT
        'digidip', 10
),

linkbuilder as (
    select
    distinct
    advertisers.id,
    program.status_id as programStatus,
    advertisers.network_name as networkName,
    advertisers.source_id as advertiserId,
    advertisers.name as name,
    lower(REPLACE(advertisers.website, 'http://', 'https://')) AS website,
    LOWER(REGEXP_REPLACE(advertisers.website, r'^https?://(www\.)?', '')) AS domainWithPath,
    REGEXP_REPLACE(LOWER(advertisers.website), r'^https?://(www\.)?|/.*$', '') AS domain,
    REPLACE(link.url, 'http://', 'https://') AS linkWebsite,
    LOWER(REGEXP_REPLACE(link.url, r'^https?://(www\.)?', '')) AS linkDomainWithPath,
    REGEXP_REPLACE(LOWER(link.url), r'^https?://(www\.)?|/.*$', '') AS linkDomain,
    link.url as linkUrl,
    coalesce(link.trackinglink,missing.evergreenUrlOverride) as linkBuilderUrl,
    CASE 
        -- For encoded homepage URLs (e.g., Poshmark, The RealReal)
        WHEN link.trackinglink LIKE CONCAT('%', REPLACE(REPLACE(link.url, ':', '%3A'), '/', '%2F'), '%') 
            THEN REPLACE(REPLACE(link.url, ':', '%3A'), '/', '%2F')
        -- For non-encoded homepage URLs (e.g., eBay)
        ELSE link.url
    END AS encodedUrl,
    CASE 
        -- For encoded homepage URLs (e.g., Poshmark, The RealReal)
        WHEN link.trackinglink LIKE CONCAT('%', REPLACE(REPLACE(REGEXP_REPLACE(link.url, r'^https?://(www\.)?', ''), ':', '%3A'), '/', '%2F'), '%') 
            THEN REPLACE(REPLACE(REGEXP_REPLACE(link.url, r'^https?://(www\.)?', ''), ':', '%3A'), '/', '%2F')
        -- For non-encoded homepage URLs (e.g., eBay)
        ELSE REGEXP_REPLACE(link.url, r'^https?://(www\.)?', '')
    END AS encodedDomain,
    case when program.status_id = 'joined' then
    REPLACE(
            link.trackinglink, 
            -- Dynamically identify whether the homepage URL is encoded or not
            CASE 
                -- For encoded homepage URLs (e.g., Poshmark, The RealReal)
                WHEN link.trackinglink LIKE CONCAT('%', REPLACE(REPLACE(link.url, ':', '%3A'), '/', '%2F'), '%') THEN REPLACE(REPLACE(link.url, ':', '%3A'), '/', '%2F')
                -- For non-encoded homepage URLs (e.g., eBay)
                ELSE link.url
            END, 
            '{{params.encoded_url}}'
        )
    when link.advertiser_id = 'eEJnPPBE' then null
    else null end as evergreenUrl,
    coalesce(REGEXP_REPLACE(ascend.trackinglink, r'(url=)[^&]*', 'url={{params.encoded_url}}'),cj.evergreenUrlOverride) AS evergreenUrlOverride,
    cookie,
    -- For commissionRateMax, assume AOV of 100 for fixed fee
    GREATEST(
      -- advertiser-level %, or 0 if missing
      COALESCE(commission.ratio.max, 0),
      -- advertiser-level fixed ÷ AOV, or 0 if missing * 100
      COALESCE(SAFE_DIVIDE(commission.fixed.max, 100), 0) * 100,
      -- program-level %, or 0 if missing
      COALESCE(program.commissions.ratio.max, 0),
      -- program-level fixed ÷ AOV, or 0 if missing * 100
      COALESCE(SAFE_DIVIDE(program.commissions.fixed.max, 100), 0) * 100
    ) AS commissionRateMax,
    networkRank,
    'BOTH' as device
    from `strackr.advertisers` advertisers
    left join `strackr.linkbuilder` link
    on advertisers.id = link.advertiser_id, unnest(programs) as program
    left join `ops_apps_script.ascend_linkbuilder` ascend
    on advertisers.source_id = ascend.advertiser_id
    and advertisers.network_name = 'Ascend by Partnerize'
    and ascend.trackinglink != 'Link not found'
    left join `affiliate.airflow_cj_text_links` cj
    on advertisers.source_id = cj.advertiserId
    and advertisers.network_name = 'CJ Affiliate'
    left join `affiliate.airflow_missing_affiliate_links` missing
    on advertisers.source_id = missing.advertiser_id
    and advertisers.network_name = missing.network_name 
    left join ranked_networks net
    on advertisers.network_name = net.network_name
    where (link.channel_name is null or link.channel_name != 'Phia.co Instagram')
)

select
distinct
networkName,
id as AdvertiserId,
name,
website,
domainWithPath as domain,
commissionRateMax,
evergreenUrl,
evergreenUrlOverride,
cookie as cookieDurationHours,
cast(null as timestamp) as evergreenUrlLastTestedAt,
cast(null as string) as evergreenUrlTestStatus,
networkRank,
device
from linkbuilder
where 1=1
and programStatus = 'joined'
and (linkUrl is null or domainWithPath = encodedDomain)
and coalesce(evergreenUrl,evergreenUrlOverride) is not null
union all
select networkName, AdvertiserId, name, website, domain, commissionRateMax, evergreenUrl, evergreenUrlOverride, CAST(cookieDurationHours as INT64) as cookieDurationHours, CAST(evergreenUrlLastTestedAt as TIMESTAMP) as evergreenUrlLastTestedAt, evergreenUrlTestStatus, networkRank, 'BOTH' as device
from `{{ params.shopmy_table_id }}`
union all
select networkName, AdvertiserId, name, website, domain, commissionRateMax, evergreenUrl, evergreenUrlOverride, CAST(cookieDurationHours as INT64) as cookieDurationHours, CAST(evergreenUrlLastTestedAt as TIMESTAMP) as evergreenUrlLastTestedAt, evergreenUrlTestStatus, networkRank, device
from `{{ params.wildfire_table_id }}`
union all
select 
    networkName,
    CONCAT('LT_', TO_HEX(MD5(domain))) as AdvertiserId,
    name,
    website,
    domain,
    commissionRateMax,
    evergreenUrl,
    CAST(null as STRING) as evergreenUrlOverride,
    CAST(null as INT64) as cookieDurationHours,
    CAST(null as TIMESTAMP) as evergreenUrlLastTestedAt,
    CAST(null as STRING) as evergreenUrlTestStatus,
    11 as networkRank,
    'BOTH' as device
from `affiliate.airflow_linktree_links`
qualify row_number() over (partition by domain order by commissionRateMax desc nulls last, networkRank) = 1 