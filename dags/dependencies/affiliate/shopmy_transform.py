import pandas as pd
from google.cloud import storage
import uuid
import logging
from .common import (
    SHOPMY_NETWORK_RANK, 
    SHOPMY_NETWORK_NAME,
    DEFAULT_COOKIE_DURATION,
    DEVICE_BOTH
)

logger = logging.getLogger(__name__)

SHOPMY_BUCKET_NAME = 'shopmy-affiliate-rates'
SHOPMY_EXCLUSIONS = ['poshmark', 'chanel', 'clubmonaco']

def calculate_commission_rate(row: pd.Series) -> float:
    full_payout: float = float(row['fullPayout'])
    is_on_shopmy: bool = row['On ShopMy?'] == 'Yes'
    rate_type: str = row['rateType']
    
    if rate_type.lower() != 'percentage':
        # Assuming average order value of $100
        avg_order_value: float = 100.0
        commission_percentage: float = (full_payout / avg_order_value) * 100
    else:
        commission_percentage: float = full_payout
    
    # Apply 82% reduction if not on ShopMy
    if not is_on_shopmy:
        commission_percentage *= 0.82
        
    return commission_percentage

def get_latest_file_from_gcs(bucket_name: str) -> storage.Blob:
    """Get the latest file from GCS bucket."""
    logger.info(f"Fetching latest file from bucket: {bucket_name}")
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blobs = list(bucket.list_blobs())
    
    if not blobs:
        raise Exception(f"No files found in bucket {bucket_name}")
    
    latest_blob = max(blobs, key=lambda x: x.time_created)
    logger.info(f"Latest file: {latest_blob.name}")
    return latest_blob

def load_shopmy_affiliates(**kwargs) -> pd.DataFrame:
    try:
        logger.info("Starting ShopMy affiliate data load")
        latest_blob = get_latest_file_from_gcs(SHOPMY_BUCKET_NAME)
        content = latest_blob.download_as_string()
        raw_df = pd.read_csv(pd.io.common.BytesIO(content))
        logger.info(f"Loaded CSV data, shape: {raw_df.shape}")

        raw_df = raw_df[~raw_df['domain'].str.contains('|'.join(SHOPMY_EXCLUSIONS))]

        transformed_df = pd.DataFrame({
            'networkRank': [SHOPMY_NETWORK_RANK] * len(raw_df),
            'networkName': [SHOPMY_NETWORK_NAME] * len(raw_df),
            'AdvertiserId': ['shopmy_' + str(uuid.uuid4()) for _ in range(len(raw_df))],
            'Name': raw_df['name'],
            'Website': raw_df['domain'],
            'domain': raw_df['domain'],
            'commissionRateMax': raw_df.apply(calculate_commission_rate, axis=1),
            'evergreenUrl': ['https://go.shopmy.us/ap/phiaco?url={{encoded_url}}&ref={{phia_merchant_product_url_id}}'] * len(raw_df),
            'evergreenUrlOverride': [None] * len(raw_df),
            'cookieDurationHours': [DEFAULT_COOKIE_DURATION] * len(raw_df),
            'evergreenUrlLastTestedAt': [None] * len(raw_df),
            'evergreenUrlTestStatus': [None] * len(raw_df),
            'device': [DEVICE_BOTH] * len(raw_df)
        })
        
        logger.info(f"Transformed data, shape: {transformed_df.shape}")
        return transformed_df
        
    except Exception as e:
        logger.error(f"Error processing ShopMy affiliate rates: {str(e)}")
        raise 