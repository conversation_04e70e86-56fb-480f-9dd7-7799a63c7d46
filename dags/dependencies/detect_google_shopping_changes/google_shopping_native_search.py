import aiohttp
import requests
from bs4 import BeautifulSoup
import json
from decimal import Decimal
import logging
import base64
import re
from typing import List, Dict
from urllib.parse import unquote

logger = logging.getLogger(__name__)

class GoogleShoppingNativeSearch:
    ZYTE_API_ENDPOINT = "https://api.zyte.com/v1/extract"
    ZYTE_API_KEY = "7156bdf7b6254a7199ae30844cfa8313"

    @staticmethod
    def base_url(query: str) -> str:
        return f"https://www.google.com/search?tbm=shop&q={requests.utils.quote(query)}&hl=en&gl=US"

    @staticmethod
    async def fetch_html_from_zyte(url: str) -> str:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    GoogleShoppingNativeSearch.ZYTE_API_ENDPOINT,
                    json={"url": url, "browserHtml": False, "httpResponseBody": True},
                    auth=aiohttp.BasicAuth(GoogleShoppingNativeSearch.ZYTE_API_KEY, ""),
                    timeout=20,
                ) as response:
                    response.raise_for_status()
                    encoded_body = (await response.json()).get("httpResponseBody", "")
                    return base64.b64decode(encoded_body).decode("utf-8") if encoded_body else ""
        except aiohttp.ClientError as e:
            logger.error(f"Error fetching from Zyte: {e}")
            return ""

    @staticmethod
    def extract_google_ldi(html: str) -> dict:
        try:
            match = re.search(r"google\.ldi\s*=\s*(\{.*?\});", html, re.S)
            if not match:
                logger.error("google.ldi object not found in HTML.")
                return {}
            return json.loads(match.group(1).replace("\\u003d", "="))
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse google.ldi JSON: {e}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error extracting google.ldi: {e}")
            return {}

    @staticmethod
    def decode_ldi_urls(ldi_object: dict) -> dict:
        try:
            return {key: unquote(value) for key, value in ldi_object.items()}
        except Exception as e:
            logger.error(f"Error decoding LDI URLs: {e}")
            return {}

    @staticmethod
    def extract_and_map_dimg_images(html: str, decoded_ldi: Dict[str, str]) -> List[str]:
        html = re.sub(
            r'\\x([0-9A-Fa-f]{2})',
            lambda m: chr(int(m.group(1), 16)),
            html
        )
        
        pattern = re.compile(r'<img[^>]+id=["\'](dimg_[^"\']+)["\'][^>]*>', re.IGNORECASE)
        
        image_urls = []
        
        for match in pattern.finditer(html):            
            if "Product Image 1 of" in match.group(0):
                img_id = match.group(1)
                actual_url = decoded_ldi.get(img_id)
                if actual_url:
                    image_urls.append(actual_url)
        
        return image_urls


    @staticmethod
    def get_google_product_url(product_id: str, product_type: str) -> str:
        if product_type == "CID":
            return f"https://www.google.com/shopping/product/{product_id}?gl=us"
        elif product_type == "PID":
            return f"https://www.google.com/shopping/product/1?gl=us&prds=pid:{product_id}"
        return ""

    @staticmethod
    def product_selector(html: str) -> list:
        try:
            soup = BeautifulSoup(html, "html.parser")
            product_containers = soup.select('ul[class*="product-grid"] > li')
            ldi = GoogleShoppingNativeSearch.extract_google_ldi(html)
            decoded_ldi = GoogleShoppingNativeSearch.decode_ldi_urls(ldi)
            image_urls = GoogleShoppingNativeSearch.extract_and_map_dimg_images(html, decoded_ldi)
            
            products = []
            for i, container in enumerate(product_containers):
                data_cid = container.select_one("[data-cid]")["data-cid"] if container.select_one("[data-cid]") else ""
                data_pid = container.select_one("[data-pid]")["data-pid"] if container.select_one("[data-pid]") else ""
                name = container.select_one('div[style*="-webkit-line-clamp"]')
                price = container.select_one('span[aria-label^="Current Price"]')
                merchant = container.select_one('div[style*="flex-direction:row"] span:not([aria-hidden="true"])')
                img_url = image_urls[i] if i < len(image_urls) else ""
                
                product_id = data_cid or data_pid
                product_type = "CID" if data_cid else "PID"
                final_product_url = GoogleShoppingNativeSearch.get_google_product_url(product_id, product_type)
                products.append({
                    "id": f"google-{product_id}", "name": name.text.strip(), "priceUsd": Decimal(price.text.replace("$", "").replace(",", "") or "0"),
                    "productUrl": final_product_url, "imgUrl": img_url, "secondhandRetailerId": merchant.text.strip().lower()
                })
            return image_urls, products
        except Exception as e:
            logger.error(f"Error selecting products: {e}")
            return [], [], []

    async def search(self, query: str):
        try:
            url = self.base_url(query)
            html = await self.fetch_html_from_zyte(url)
            if not html:
                return url, "", [], []
            image_urls, products = self.product_selector(html)
            return url, html, image_urls, products
        except Exception as e:
            logger.error(f"Error searching Google Shopping: {e}")
            return "", "", [], []

    @staticmethod
    def google_product_selector(html_response: str, retailer_id: str) -> dict:
        extracted_data = {
            "name": "",
            "imgUrl": "",
            "priceUsd": Decimal(0),
            "productUrl": ""
        }
        try:
            if "ebay" in retailer_id.lower():
                retailer_id = retailer_id.split("-")[0]

            soup = BeautifulSoup(html_response, "html.parser")

            image_element = soup.select_one('[id*="__pdp-container"] img')
            extracted_data["imgUrl"] = image_element.get("src", "") if image_element else ""

            name_element = soup.select_one('span[role="heading"]')
            extracted_data["name"] = name_element.get_text(strip=True) if name_element else ""

            container = soup.select_one('[id*="online-sellers-cont"]')
            if container:
                rows = container.select("tr")
                for row in rows:
                    seller_anchor = row.select_one("td a")
                    if seller_anchor:
                        seller_name = seller_anchor.get_text().replace("Opens in a new window", "").strip()
                    else:
                        seller_name = ""

                    if retailer_id.lower() in seller_name.lower():
                        price_element = row.select_one("td > span")
                        if price_element:
                            price_text = price_element.get_text().replace("$", "").replace(",", "").strip()
                        else:
                            price_text = ""

                        price = Decimal(price_text) if price_text else extracted_data["priceUsd"]

                        visit_site_url = (seller_anchor.get("href", "").replace("/url?q=", "")
                                        if seller_anchor else "")

                        if price > 0:
                            extracted_data["productUrl"] = unquote(visit_site_url)
                            extracted_data["priceUsd"] = price
                            return extracted_data
            else:
                # This means the Google ProductUrl is broken or the UI is changed
                pass

        except Exception as error:
            logger.error("Error in Google product extraction:", error)
        return extracted_data
    
    async def get_google_product_by_id(self, retailer_id: str, product_url: str):
        try:
            html = await self.fetch_html_from_zyte(product_url)
            if not html:
                return "", {}
            return html, self.google_product_selector(html, retailer_id)
        except Exception as e:
            logger.error(f"Error fetching Google Product: {e}")
            return "", {}