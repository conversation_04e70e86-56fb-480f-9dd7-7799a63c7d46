import json
import openai
from dependencies.brand_resale_value.utils.constants import (
    OPENAI_API_KEY,
    IS_DEVELOPMENT,
    REFINED_SEARCH_QUERIES_BUCKET,
)
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
from dependencies.brand_resale_value.utils.logger import logger

"""
JSON input format:
{
    "batch_ids": ["batch_id1", "batch_id2"],
    "file_ids": ["file_id1", "file_id2"]
}

JSON output format:
{
    "refined_query": "query1",
    "secondhand_product": {
        "id": "sh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    }
}
"""

client = openai.Client(api_key=OPENAI_API_KEY)


def collect_refined_search_queries(input_gcs_path: str, gpt_gcs_path: str, output_gcs_path: str):
    logger.info("Collecting refined search queries from GPT based on the batch id")

    if check_cache_in_gcs(output_gcs_path):
        logger.info("Output already exists in the GCS bucket")
        return

    try:
        # Load input data
        input_data = load_jsonl_from_gcs(input_gcs_path)
        gpt_data = load_jsonl_from_gcs(gpt_gcs_path)
        batch_ids = gpt_data[0].get("batch_ids", [])

        gpt_outputs = []

        if IS_DEVELOPMENT:
            file_ids = gpt_data[0].get("file_ids", [])
            for file_id in file_ids:
                gpt_outputs.extend(download_batch_results(file_id))
        else:
            for batch_id in batch_ids:
                try:
                    response = client.batches.retrieve(batch_id)
                    results = download_batch_results(response.output_file_id)
                    gpt_outputs.extend(results)
                except Exception as e:
                    logger.error(f"Error retrieving batch ID {batch_id}: {e}")

        secondhand_to_query_mapping = {}
        for row in gpt_outputs:
            secondhand_id = row["custom_id"]
            if IS_DEVELOPMENT:
                raw_content = row["response"]["choices"][0]["message"]["content"]
            else:
                raw_content = row["response"]["body"]["choices"][0]["message"]["content"]
            if raw_content.startswith("```") and raw_content.endswith("```"):
                raw_content = "\n".join(raw_content.split("\n")[1:-1])
            try:
                parsed_response = json.loads(raw_content)
            except:
                logger.error(f"Error parsing JSON: {raw_content}")
                continue
            secondhand_to_query_mapping[secondhand_id] = parsed_response.get("query", "")

        refined_queries = []

        for row in input_data:
            brand = row["brand"]
            category = row["category"]
            secondhand_products = row["secondhand_products"]
            for secondhand_product in secondhand_products:
                secondhand_id = secondhand_product["id"]
                query = secondhand_to_query_mapping.get(secondhand_id, "")
                refined_queries.append(
                    {
                        "brand": brand,
                        "category": category,
                        "refined_query": query,
                        "secondhand_product": secondhand_product,
                    }
                )

        store_data_to_jsonl_in_gcs(output_gcs_path, refined_queries)
    except Exception as e:
        logger.error(f"Error in collect_refined_search_queries: {e}")


def download_batch_results(file_id: str):
    try:
        output_file = client.files.content(file_id=file_id)
        file_content = output_file.text
        data = [json.loads(line) for line in file_content.strip().split("\n")]
        return data
    except Exception as e:
        logger.error(f"Error downloading batch results for file ID {file_id}: {e}")
        return []
