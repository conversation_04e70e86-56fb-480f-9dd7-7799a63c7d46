import logging
from urllib.parse import urlparse
from google.cloud import storage

logger = logging.getLogger(__name__)

_cached_firsthand_set = None


def normalize_string(text) -> str:
    """
    Normalize a string by converting to lowercase, stripping spaces,
    and removing non-alphanumeric characters.
    """
    if not text:
        return ""
    return "".join(c for c in text.lower().strip() if c.isalnum())


def load_firsthand_set() -> set:
    """
    Downloads the CSV from GCS, processes each line, and returns a set of domains.
    """

    storage_client = storage.Client()

    bucket_name = "firsthand-websites"
    file_name = "firsthand_websites.csv"

    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(file_name)

    csv_data = blob.download_as_text()

    lines = [line.strip().lower() for line in csv_data.split("\n") if line.strip()]
    lines = set(lines)
    normalized_firsthand_set = {normalize_string(item) for item in lines}
    return normalized_firsthand_set


def get_firsthand_set() -> set:
    global _cached_firsthand_set
    if _cached_firsthand_set is None:
        _cached_firsthand_set = load_firsthand_set()
    return _cached_firsthand_set


# 3) Use the cached set to determine if a product is from a firsthand site
def is_firsthand_product(product_url: str, seller: str) -> bool:
    """
    Checks if the domain of product_url OR the seller matches any entry
    in the firsthand set.
    """
    domain_name = None
    second_level_domain = None

    blacklisted_domains = [
        "fashionphile",
        "walmart",
        "ruelala",
    ]

    if product_url:
        try:
            parsed_url = urlparse(product_url)
            domain_name = parsed_url.hostname  # e.g. "example.com"
            if domain_name:
                domain_segments = domain_name.split(".")
                if len(domain_segments) >= 2:
                    second_level_domain = domain_segments[-2]
        except Exception as error:
            logger.warning("Invalid productUrl: %s", error)

    seller_lower = seller.lower() if seller else ""

    # Retrieve the cached set
    firsthand_set = get_firsthand_set()

    # Check membership in the set
    return (
        seller_lower not in blacklisted_domains
        and second_level_domain not in blacklisted_domains
    ) and (
        (domain_name in firsthand_set)
        or (seller_lower in firsthand_set)
        or (second_level_domain in firsthand_set if second_level_domain else False)
    )
