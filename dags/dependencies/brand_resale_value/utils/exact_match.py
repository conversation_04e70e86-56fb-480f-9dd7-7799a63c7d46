import unicodedata
from fuzzywuzzy import fuzz
from dependencies.brand_resale_value.utils.constants import ALLOWED_CATEGORIES


def normalize(text):
    """
    Normalize a string by removing accents, converting to lowercase,
    and keeping only alphanumeric characters and spaces.
    """
    text = unicodedata.normalize("NFD", text.lower())
    text = "".join(c for c in text if unicodedata.category(c) != "Mn")
    return "".join(c for c in text if c.isalnum() or c.isspace())


def get_words(text):
    """
    Split normalized text into words.
    """
    return list(filter(lambda w: len(w) > 0, text.split()))


def calculate_word_match_percentage(original_words, target_words):
    """
    Calculate the percentage of words from the original product name that
    match the target product name.
    """
    if not original_words:
        return 0.0
    match_count = sum(1 for word in original_words if word in target_words)
    return (match_count / len(original_words)) * 100


def fuzzy_match(str1: str, str2: str, threshold: int = 80) -> bool:
    """
    Perform fuzzy matching between two strings with a specified threshold
    """
    str1_clean = str1.strip().lower()
    str2_clean = str2.strip().lower()
    ratio = fuzz.ratio(str1_clean, str2_clean)
    return ratio >= threshold


def fuzzy_details_match(original_details: dict, target_details: dict) -> bool:
    """
    Perform fuzzy matching for category, color, and size between two detail dictionaries.
    """

    cat_match = fuzzy_match(
        original_details["category"], target_details["category"], threshold=100
    )

    color_match = (
        original_details["color"] == ""
        or target_details["color"] == ""
        or fuzzy_match(original_details["color"], target_details["color"], threshold=70)
    )

    return cat_match and color_match


def coarse_text_match(title1: str, title2: str, threshold_percent=60) -> bool:
    """
    Perform a coarse text match between two product titles.
    This function checks if the titles share a significant number of common words.
    """
    words1 = get_words(normalize(title1))
    words2 = get_words(normalize(title2))
    match_percent = calculate_word_match_percentage(words1, words2)
    return match_percent >= threshold_percent


def build_exact_match_prompt(original_product, target_product) -> list:
    """
    Build a prompt for GPT to:
      - Extract a refined product name
      - Identify the product's primary color
      - Classify the product's category from ALLOWED_CATEGORIES
      - Determine if the two product descriptions represent the same product
        (including consideration of style/variant distinctions).
    """
    system_prompt = f"""
Compare two fashion products and determine if they're identical. Return a match score (0 or 1) and product category.
Rules:
1. Ignore condition differences (new, pre-owned, etc.)
2. Ignore marketing words (authentic, exclusive, sale, etc.)
3. Match must have the same:
   - **Brand**: Accept acronyms and accent differences (e.g., ZARA/Zara, Maison Margiela/MM, Chloé/Chloe)
   - **Subcategory/Style**: Must be identical (e.g., Boots vs. Sneakers)
   - **Pattern/Texture**: Must be identical. If one product specifies a pattern/texture, the other must specify the same.
4. Ignore clothing/shoe sizes unless specified:
   - For **accessories/bags**, sizes must match.
5. All required fields must be present and match exactly as per the above criteria. Missing or unspecified required fields in either product will result in a non-match.
   
Input Format:
**Product1**:
    Title:
    Link:
    Brand:
**Product2**:
    Title:
    Link:
    Brand:

Output Format:
{{
"is_exact_match": true/false,
"category": string
}}
"""

    user_prompt = f"""
INPUT:
    **Original Product**:
        Title: "{original_product['name']}"
        Link: "{original_product['link']}"
        Query Brand: "{original_product['query_brand']}"
        Query Category: "{original_product['query_category']}"

    **Target Product**: 
        Title: "{target_product['name']}"
        Link: "{target_product['link']}"
        Query Brand: "{target_product['query_brand']}"
        Query Category: "{target_product['query_category']}"
    """

    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]
