from google.cloud import storage
import json
from dependencies.brand_resale_value.utils.constants import (
    FIRSTHAND_PRODUCTS_OF_SECONDHAND_BUCKET,
    SECONDHAND_PRODUCTS_OF_FIRSTHAND_BUCKET,
)

test_id = "man_test_9"
FILE_NAME = f"/{test_id}.jsonl"


def load_jsonl_from_gcs(gcs_path):
    """Loads a JSONL file from Google Cloud Storage."""
    try:
        client = storage.Client()
        bucket_name, blob_name = gcs_path.replace("gs://", "").split("/", 1)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        data = blob.download_as_string().decode("utf-8")
        return [json.loads(line) for line in data.split("\n") if line]
    except Exception as e:
        print(f"An error occurred while loading JSONL from GCS: {e}")
        return None


def store_data_to_jsonl_in_gcs(gcs_path, data):
    """Stores data to a JSONL file in Google Cloud Storage."""
    try:
        client = storage.Client()
        bucket_name, blob_name = gcs_path.replace("gs://", "").split("/", 1)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        blob.upload_from_string("\n".join(json.dumps(line) for line in data))
    except Exception as e:
        print(f"An error occurred while storing data to JSONL in GCS: {e}")


def check_cache_in_gcs(gcs_path):
    """Check if the output already exists in the Google Cloud Storage bucket."""
    try:
        client = storage.Client()
        bucket_name, blob_name = gcs_path.replace("gs://", "").split("/", 1)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        return blob.exists()
    except Exception as e:
        print(f"An error occurred while checking cache in GCS: {e}")
        return False


def get_firsthand_product_by_id(product_id: str) -> dict:
    """
    Retrieve a firsthand product by its ID from a JSON file in GCS
    """
    firsthand_data_path = FIRSTHAND_PRODUCTS_OF_SECONDHAND_BUCKET + FILE_NAME
    try:
        firsthand_data = load_jsonl_from_gcs(firsthand_data_path)
        for item in firsthand_data:
            for product in item.get("firsthand_products", []):
                if product.get("firsthand_product_id") == product_id:
                    return product
        return {}
    except Exception as e:
        print(f"Error retrieving firsthand product: {e}")
        return {}


# Below corresponds to the product which was fetched from firsthand_product but not from brand-category pair
def get_secondhand_product_by_id(product_id: str) -> dict:
    """
    Retrieve a secondhand product by its ID from a JSON file in GCS
    """
    secondhand_data_path = SECONDHAND_PRODUCTS_OF_FIRSTHAND_BUCKET + FILE_NAME
    try:
        secondhand_data = load_jsonl_from_gcs(secondhand_data_path)
        for item in secondhand_data:
            for product in item.get("secondhand_products", []):
                if product.get("secondhand_product_id") == product_id:
                    return product
        return {}
    except Exception as e:
        print(f"Error retrieving secondhand product: {e}")
        return {}
