import json
import os
from airflow.decorators import task, task_group
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from typing import Callable

# from airflow.providers.google.cloud.operators


#
# 1) STREAM & SPLIT all files in an input folder -> chunk files in GCS
#
@task
def split_input_folder_into_chunks(
    input_folder: str, temp_folder: str, batch_size: int
) -> list[str]:
    """
    1. Lists all objects (files) under `input_folder` in GCS.
    2. Reads them line-by-line.
    3. Buff<PERSON> lines until `batch_size` lines, then writes out a chunk file to `temp_folder`.
    4. Returns a list of chunk-file GCS paths.

    -> No big data in memory or XCom, just small buffers plus a list of chunk paths.
    """
    gcs_hook = GCSHook()
    chunk_file_paths = []

    current_chunk_index = 0
    buffer = []
    b_name, input_folder = _split_gcs_path(input_folder)
    object_names = gcs_hook.list(bucket_name=b_name, prefix=input_folder)

    for object_name in object_names:
        # skip "folder placeholders"
        if object_name.endswith("/"):
            continue

        file_data = gcs_hook.download(bucket_name=b_name, object_name=object_name)
        lines = file_data.decode("utf-8").splitlines()

        for line in lines:
            buffer.append(line)
            if len(buffer) >= batch_size:
                chunk_path = f"{temp_folder}/chunk_{current_chunk_index}.jsonl"
                _write_chunk_to_gcs(gcs_hook, chunk_path, buffer)
                chunk_file_paths.append(chunk_path)

                buffer = []
                current_chunk_index += 1

    if buffer:
        chunk_path = f"{temp_folder}/chunk_{current_chunk_index}.jsonl"
        _write_chunk_to_gcs(gcs_hook, chunk_path, buffer)
        chunk_file_paths.append(chunk_path)

    return chunk_file_paths


def _write_chunk_to_gcs(gcs_hook: GCSHook, chunk_path: str, lines: list[str]):
    """
    Writes the buffered lines into a chunk file in GCS.
    """
    bucket_name, object_name = _split_gcs_path(chunk_path)
    gcs_hook.upload(
        bucket_name=bucket_name, object_name=object_name, data="\n".join(lines)
    )


#
# 2) PROCESS CHUNK
#
@task
def process_chunk_file(
    chunk_file_path: str,
    output_folder: str,
    process_func: Callable[[list[dict]], list[dict]],
) -> str:
    """
    Reads each line as JSON, applies process_func,
    writes processed rows to an output chunk file in `output_folder`,
    and returns the processed chunk path.
    """
    gcs_hook = GCSHook()
    processed_rows = []

    bucket_name, object_name = _split_gcs_path(chunk_file_path)
    chunk_file = gcs_hook.download(bucket_name=bucket_name, object_name=object_name)
    chunk_data = []
    chunk_lines = chunk_file.decode("utf-8").splitlines()
    for line in chunk_lines:
        row = json.loads(line)
        chunk_data.append(row)
    print(chunk_data)
    processed_rows = process_func(chunk_data)

    json_rows = []
    try:
        for row in processed_rows:
            json_rows.append(json.dumps(row))
    except Exception as e:
        print(e)
        print(row)

    chunk_name = os.path.basename(object_name)  # "chunk_0.jsonl"
    processed_path = f"{output_folder}/processed_{chunk_name}"
    b_out, o_out = _split_gcs_path(processed_path)

    gcs_hook.upload(bucket_name=b_out, object_name=o_out, data="\n".join(json_rows))

    return processed_path


#
# 3) CLEAN UP TEMP FOLDER
#
@task
def cleanup_gcs_prefix(prefix: str):
    """
    Deletes all objects under `prefix` in GCS.
    E.g. if prefix='tmp/my_task/2023-01-01',
    it will remove all chunk files under that path.
    """
    gcs_hook = GCSHook()
    bucket_name, prefix_obj = _split_gcs_path(prefix)

    objects_to_delete = gcs_hook.list(bucket_name=bucket_name, prefix=prefix_obj)
    for obj in objects_to_delete:
        gcs_hook.delete(bucket_name=bucket_name, object_name=obj)


def _split_gcs_path(gcs_path: str):
    """
    Simplistic splitting: always uses 'your-bucket'.
    Adjust if you embed the bucket in the path or have multiple buckets.
    """
    path_without_protocol = gcs_path.split("://")[1]
    bucket_name = path_without_protocol.split("/")[0]
    object_name = path_without_protocol.split("/", 1)[1]
    return bucket_name, object_name


#
# 4) TASKGROUP: read folder -> chunk -> parallel process -> cleanup temp
#
def create_batch_process_task_group(
    task_name: str,
    input_folder: str,
    batch_size: int,
    temp_folder: str,
    output_folder: str,
    process_func: Callable[[list], list],
):

    @task_group(group_id=task_name)
    def _batch_process_task_group(
        input_folder: str,  # folder containing JSONL files
        batch_size: int,
        temp_folder: str,  # e.g. "tmp/task_name/{{ run_id }}"
        output_folder: str,  # e.g. "output/task_name/{{ run_id }}"
        process_func: Callable[[list], list],
    ):
        """
        1) Streams all files under `input_folder` -> chunk files in `temp_folder`.
        2) Dynamically maps process_chunk_file over each chunk, storing results in `output_folder`.
        3) Cleans up `temp_folder`.
        4) Returns `output_folder` as the final "folder" location of processed chunks.
        """
        # Step 1: Create chunk files
        chunk_files = split_input_folder_into_chunks(
            input_folder=input_folder, temp_folder=temp_folder, batch_size=batch_size
        )

        # Step 2: Process each chunk in parallel
        processed_chunks = process_chunk_file.partial(
            output_folder=output_folder, process_func=process_func
        ).expand(chunk_file_path=chunk_files)

        # Step 3: Cleanup the temp folder after processing
        clean_temp = cleanup_gcs_prefix(temp_folder)
        processed_chunks >> clean_temp

        # # Return the output folder as the "final result" for chaining
        # return output_folder

    return _batch_process_task_group(
        input_folder=input_folder,
        batch_size=batch_size,
        temp_folder=temp_folder,
        output_folder=output_folder,
        process_func=process_func,
    )
