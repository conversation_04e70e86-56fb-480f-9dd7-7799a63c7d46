import os

OPENAI_API_KEY = "**********************************************************************************************************************"
OPENAI_API_ORIGIN = "https://api.openai.com"
OPENAI_BATCH_ENDPOINT = "/v1/batches"


IS_DEVELOPMENT = True

ALLOWED_CATEGORIES = [
    "clothing",
    "Tops",
    "Pants",
    "Shoes",
    "Outerwear",
    "Dresses",
    "handbags, wallets & cases",
    "jewelry",
    "shoes",
    "bags",
    "fragrances",
    "watches",
    "rings",
    "necklaces",
    "earrings",
    "bracelets",
    "sunglasses",
    "belts",
    "hats",
    "others",
]

BRAND_RESALE_VALUE_BUCKET = "gs://collect-test/brand_resale_value"  # Main bucket for the brand resale value pipeline
BRANDS_AND_CATEGORIES_FILE = f"{BRAND_RESALE_VALUE_BUCKET}/brands_categories.jsonl"  # Source file for the brands and categories
SECONDHAND_PRODUCTS_OF_BRANDS_BUCKET = (
    f"{BRAND_RESALE_VALUE_BUCKET}/secondhand_from_brands"
)
REFINED_QUERY_BATCH_CHECK_IDS_BUCKET = (
    f"{BRAND_RESALE_VALUE_BUCKET}/refined_query_batch_check_ids"
)
FIRSTHAND_PRODUCTS_OF_SECONDHAND_BUCKET = (
    f"{BRAND_RESALE_VALUE_BUCKET}/firsthand_from_secondhand"
)
FIRSTHAND_BATCH_CHECK_IDS_BUCKET = (
    f"{BRAND_RESALE_VALUE_BUCKET}/firsthand_batch_check_ids"
)
EXACT_FIRSTHAND_PRODUCTS_BUCKET = f"{BRAND_RESALE_VALUE_BUCKET}/firsthand_products"
SECONDHAND_PRODUCTS_OF_FIRSTHAND_BUCKET = (
    f"{BRAND_RESALE_VALUE_BUCKET}/secondhand_from_firsthand"
)
SECONDHAND_BATCH_CHECK_IDS_BUCKET = (
    f"{BRAND_RESALE_VALUE_BUCKET}/secondhand_batch_check_ids"
)
EXACT_SECONDHAND_PRODUCTS_BUCKET = (
    f"{BRAND_RESALE_VALUE_BUCKET}/exact_secondhand_from_firsthand"
)
BRAND_RESALE_VALUE_CALCULATION_BUCKET = (
    f"{BRAND_RESALE_VALUE_BUCKET}/brand_resale_value"
)
REFINED_SEARCH_QUERIES_BUCKET = f"{BRAND_RESALE_VALUE_BUCKET}/refined_search_queries"
