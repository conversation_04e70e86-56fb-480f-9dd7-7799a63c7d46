import requests
from bs4 import BeautifulSoup
from urllib.parse import quote
import re
from typing import List, Dict, Any


def fetch_html(url: str) -> str:
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        return ""


def parse_ebay_search_results(html: str) -> List[Dict[str, Any]]:

    soup = BeautifulSoup(html, "html.parser")
    product_containers = soup.select("li.s-item")

    products = []
    for container in product_containers:
        title_element = container.select_one(".s-item__title")
        price_element = container.select_one(".s-item__price")
        subtitle_element = container.select_one(".s-item__subtitle")
        link_element = container.select_one("a.s-item__link")
        image_element = container.select_one(".s-item__image img")

        name = title_element.get_text(strip=True) if title_element else ""
        product_url = link_element["href"] if link_element else ""
        price_text = (
            price_element.get_text(separator=" ", strip=True) if price_element else "0"
        )
        price_text = price_text.split(" to ")[0]
        price_usd = float(re.sub(r"[^0-9.]", "", price_text) or 0)
        retailer_info = (
            subtitle_element.get_text(strip=True) if subtitle_element else ""
        )
        is_secondhand = "Pre-Owned" in retailer_info
        img_url = image_element["src"] if image_element else ""
        secondhand_retailer_id = "eBay"

        if name and price_usd and img_url and product_url and is_secondhand:
            products.append(
                {
                    "image_url": img_url,
                    "name": name,
                    "price": price_usd,
                    "link": product_url,
                    "source_id": secondhand_retailer_id,
                    "source": "EbayShopping",
                }
            )

    return products
