import json
import re
from typing import Dict, List
from urllib.parse import quote, unquote
from bs4 import BeautifulSoup


def extract_google_ldi(html: str) -> Dict[str, str]:
    match = re.search(r"google\.ldi\s*=\s*(\{.*?\});", html, re.DOTALL)
    if not match:
        raise ValueError("google.ldi object not found in HTML.")

    ldi_json_str = match.group(1).replace(r"\u003d", "=")

    try:
        ldi_object = json.loads(ldi_json_str)
    except json.JSONDecodeError as e:
        raise ValueError(f"Unable to parse: {e}")

    return ldi_object


def decode_ldi_urls(ldi_object: Dict[str, str]) -> Dict[str, str]:
    return {key: unquote(val) for key, val in ldi_object.items()}


def extract_and_map_dimg_images(html: str, decoded_ldi: Dict[str, str]) -> List[str]:
    def _replace_hex(match):
        hex_value = match.group(1)
        return chr(int(hex_value, 16))

    html = re.sub(r"\\x([0-9A-Fa-f]{2})", _replace_hex, html)

    dimg_regex = re.compile(r'<img[^>]+id=["\'](dimg_[^"\']+)["\'][^>]*>')
    image_urls = []

    for match in dimg_regex.finditer(html):
        img_id = match.group(1)
        actual_url = decoded_ldi.get(img_id)
        if actual_url:
            image_urls.append(actual_url)

    return image_urls


def parse_google_shopping_results(html: str):
    soup = BeautifulSoup(html, "html.parser")
    product_containers = soup.select('ul[class*="product-grid"] > li')
    ldi_object = extract_google_ldi(html)
    decoded_ldi = decode_ldi_urls(ldi_object)
    image_urls = extract_and_map_dimg_images(html, decoded_ldi)
    products = []
    for idx, container in enumerate(product_containers):
        data_cid = container.select_one("[data-cid]").get("data-cid")
        data_pid = container.select_one("[data-pid]").get("data-pid")
        if data_cid:
            product_url = f"https://www.google.com/shopping/product/{data_cid}?gl=us"
        elif data_pid:
            product_url = (
                f"https://www.google.com/shopping/product/1?gl=us&prds=pid:{data_pid}"
            )
        else:
            anchor = container.select_one("a[href]")
            product_url = anchor.get("href") if anchor else ""

        title_element = container.select_one('div[style*="-webkit-line-clamp"]')
        name = title_element.get_text(strip=True) if title_element else ""

        price_element = container.select_one('span[aria-label^="Current Price"]')
        raw_price = price_element.get_text(strip=True) if price_element else "0"
        price_usd = float(re.sub(r"[^0-9.]", "", raw_price) or 0)
        merchant_element = container.select_one(
            'div[style*="flex-direction:row"] span:not([aria-hidden="true"])'
        )
        secondhand_retailer_id = (
            merchant_element.get_text(strip=True) if merchant_element else ""
        )

        img_url = image_urls[idx] if idx < len(image_urls) else ""

        if name and price_usd and img_url and product_url and secondhand_retailer_id:
            products.append(
                {
                    "image_url": img_url,
                    "name": name,
                    "price": price_usd,
                    "link": product_url,
                    "source_id": secondhand_retailer_id,
                    "source": "GoogleShopping",
                }
            )
    return products
