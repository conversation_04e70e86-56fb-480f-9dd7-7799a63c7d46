import concurrent.futures
from typing import Callable, List, Any, Optional, Union, Dict, Tuple
import functools
import asyncio


def run_in_parallel(
    func: Callable[..., Any],
    tasks: List[Dict[str, Any]],
    batch_size: int,
    executor_type: str = "thread",
    return_results: bool = True,
    timeout: Optional[float] = None,
    show_progress: bool = False,
) -> Union[List[Any], None]:
    """
    Executes a function in parallel across a list of tasks with controlled concurrency.

    Each task is a dictionary containing:
        - 'args': A tuple of positional arguments for the function.
        - 'kwargs': A dictionary of keyword arguments for the function.

    Parameters:
        func (Callable[..., Any]): The function to execute. It should accept any number of arguments.
        tasks (List[Dict[str, Any]]): A list of tasks, each a dict with 'args' and 'kwargs'.
            Example:
                [
                    {'args': (arg1, arg2), 'kwargs': {'kwarg1': val1}},
                    {'args': (arg3,), 'kwargs': {}},
                    ...
                ]
        batch_size (int): The maximum number of concurrent executions.
        executor_type (str): The type of executor to use ('thread' or 'process'). Defaults to 'thread'.
        return_results (bool): Whether to collect and return the results. Defaults to True.
        timeout (Optional[float]): The maximum number of seconds to wait for each function execution. Defaults to None.
        show_progress (bool): Whether to display a progress bar. Requires `tqdm` if set to True. Defaults to False.

    Returns:
        Union[List[Any], None]: A list of results if `return_results` is True; otherwise, None.

    Raises:
        ValueError: If an invalid executor_type is provided.
    """
    if executor_type not in ("thread", "process"):
        raise ValueError("executor_type must be either 'thread' or 'process'.")

    # Choose the appropriate executor
    Executor = (
        concurrent.futures.ThreadPoolExecutor
        if executor_type == "thread"
        else concurrent.futures.ProcessPoolExecutor
    )

    results = []  # To store results if required

    # Optional: Import tqdm if progress bar is enabled
    if show_progress:
        try:
            from tqdm import tqdm

            progress = tqdm(total=len(tasks), desc="Processing", unit="task")
        except ImportError:
            raise ImportError(
                "tqdm is required for show_progress=True. Install it via `pip install tqdm`."
            )
    else:
        progress = None

    with Executor(max_workers=batch_size) as executor:
        # Submit all tasks to the executor
        future_to_task = {}
        for task in tasks:
            args = task.get("args", ())
            kwargs = task.get("kwargs", {})
            future = executor.submit(func, *args, **kwargs)
            future_to_task[future] = task

        # As each task completes, process the result
        for future in concurrent.futures.as_completed(future_to_task):
            task = future_to_task[future]
            try:
                result = future.result(timeout=timeout) if timeout else future.result()
                if return_results:
                    results.append(result)
            except concurrent.futures.TimeoutError:
                print(f"Function execution timed out for task: {task}")
            except Exception as exc:
                print(f"Function raised an exception for task {task}: {exc}")
            finally:
                if progress:
                    progress.update(1)

    if progress:
        progress.close()

    if return_results:
        return results
    return None


async def run_in_parallel_async(
    func: Callable[..., Any],
    tasks: List[Dict[str, Any]],
    batch_size: int,
    return_results: bool = True,
    timeout: Optional[float] = None,
    show_progress: bool = False,
) -> Optional[List[Any]]:
    """
    Asynchronously executes a function in parallel across a list of tasks with controlled concurrency.

    Each task is a dictionary containing:
        - 'args': A tuple of positional arguments for the function.
        - 'kwargs': A dictionary of keyword arguments for the function.

    Parameters:
        func (Callable[..., Any]): The async function to execute. It should accept any number of arguments.
        tasks (List[Dict[str, Any]]): A list of tasks, each a dict with 'args' and 'kwargs'.
            Example:
                [
                    {'args': (arg1, arg2), 'kwargs': {'kwarg1': val1}},
                    {'args': (arg3,), 'kwargs': {}},
                    ...
                ]
        batch_size (int): The maximum number of concurrent executions.
        return_results (bool): Whether to collect and return the results. Defaults to True.
        timeout (Optional[float]): The maximum number of seconds to wait for each function execution. Defaults to None.
        show_progress (bool): Whether to display a progress bar. Requires `tqdm` if set to True. Defaults to False.

    Returns:
        Optional[List[Any]]: A list of results if `return_results` is True; otherwise, None.
    """
    results = []
    semaphore = asyncio.Semaphore(batch_size)

    if show_progress:
        try:
            from tqdm.asyncio import tqdm

            progress = tqdm(total=len(tasks), desc="Processing", unit="task")
        except ImportError:
            raise ImportError(
                "tqdm is required for show_progress=True. Install it via `pip install tqdm`."
            )
    else:
        progress = None

    async def sem_task(task):
        async with semaphore:
            try:
                args = task.get("args", ())
                kwargs = task.get("kwargs", {})
                if timeout:
                    return await asyncio.wait_for(
                        func(*args, **kwargs), timeout=timeout
                    )
                else:
                    return await func(*args, **kwargs)
            except asyncio.TimeoutError:
                print(f"Function execution timed out for task: {task}")
            except Exception as exc:
                print(f"Function raised an exception for task {task}: {exc}")
            finally:
                if progress:
                    progress.update(1)

    # Create all tasks
    tasks_coroutines = [sem_task(task) for task in tasks]
    # Execute tasks concurrently
    completed_tasks = await asyncio.gather(*tasks_coroutines, return_exceptions=True)

    if show_progress:
        progress.close()

    if return_results:
        # Filter out None results due to exceptions or timeouts
        for task_result in completed_tasks:
            if isinstance(task_result, Exception):
                continue
            results.append(task_result)
        return results
    return None


def prepare_tasks(
    args_list: List[Tuple[Any, ...]], kwargs_list: Optional[List[Dict[str, Any]]] = None
) -> List[Dict[str, Any]]:
    """
    Prepares a list of task dictionaries from lists of args and kwargs.

    Parameters:
        args_list (List[Tuple[Any, ...]]): A list where each element is a tuple of positional arguments.
        kwargs_list (Optional[List[Dict[str, Any]]]): A list where each element is a dict of keyword arguments.
            Must be the same length as args_list if provided.

    Returns:
        List[Dict[str, Any]]: A list of task dictionaries with 'args' and 'kwargs'.
    """
    if kwargs_list:
        if len(args_list) != len(kwargs_list):
            raise ValueError("args_list and kwargs_list must have the same length.")
        return [
            {"args": args, "kwargs": kwargs}
            for args, kwargs in zip(args_list, kwargs_list)
        ]
    else:
        return [{"args": args, "kwargs": {}} for args in args_list]
