from flask.scaffold import F
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from gspread_formatting import *


credentials_path = "/home/<USER>/.config/gcloud/application_default_credentials.json"


def create_google_sheet(json_data, spreadsheet_name):
    # Define the scope
    scope = [
        "https://spreadsheets.google.com/feeds",
        "https://www.googleapis.com/auth/drive",
    ]

    # Add credentials to the account
    creds = ServiceAccountCredentials.from_json_keyfile_name(credentials_path, scope)

    # Authorize the clientsheet
    client = gspread.authorize(creds)

    # check if the spreadsheet already exists
    try:
        spreadsheet = client.open(spreadsheet_name)
        client.del_spreadsheet(spreadsheet.id)
    except gspread.SpreadsheetNotFound:
        pass

    spreadsheet = client.create(spreadsheet_name)

    # Share the <NAME_EMAIL>
    spreadsheet.share("<EMAIL>", perm_type="user", role="writer", notify=False)

    # Optionally, make the spreadsheet accessible to everyone with the link
    spreadsheet.share(None, perm_type="anyone", role="reader")

    print(f"Spreadsheet created: {spreadsheet.url}")

    for brand_data in json_data:
        print(brand_data)
        brand_name = brand_data["brand"]
        worksheet = spreadsheet.add_worksheet(
            title=brand_name, rows="100000", cols="30"
        )

        # format_cell_range(worksheet, "A1:Z1000", CellFormat(wrapStrategy="CLIP"))

        # Collect rows to batch update
        rows = []

        # Write brand level data
        rows.append(["Brand Information"])
        rows.append(["Brand", brand_data["brand"]])
        rows.append(["Brand Resale Value", brand_data["brand_resale_value"]])

        # Write category level data
        for category in brand_data["categories"]:

            rows.append(["---------Category Information---------"])
            rows.append(["Category", category["category"]])
            rows.append(["Brand Category Value", category["brand_category_value"]])
            rows.append(["Product Count", category["product_count"]])

            # Write product data
            for product in category["product_data"]:
                rows.append(["---Firsthand Product---"])
                rows.append(["Name", product["firsthand_product"]["name"]])
                rows.append(["Link", product["firsthand_product"]["link"]])
                rows.append(["Price", product["firsthand_product"]["price"]])
                rows.append(
                    ["Image URL", product["firsthand_product"]["image_urls"][0]]
                )
                rows.append(["---Secondhand Original Product---"])
                rows.append(["Name", product["secondhand_original"]["name"]])
                rows.append(["Link", product["secondhand_original"]["link"]])
                rows.append(["Price", product["secondhand_original"]["price"]])
                rows.append(
                    ["Image URL", product["secondhand_original"]["image_urls"][0]]
                )
                rows.append(["Avg resale", product["average_resale_value"]])
                rows.append(["Secondhand Count", product["secondhand_count"]])

                # Write secondhand products data
                rows.append(["Secondhand Products"])
                rows.append(
                    [
                        "ID",
                        "Name",
                        "Link",
                        "Price",
                        "Image URLs",
                        "Query Brand",
                        "Query Category",
                    ]
                )

                for sh_product in product["secondhand_products"][:10]:
                    rows.append(
                        [
                            sh_product["id"],
                            sh_product["name"],
                            sh_product["link"],
                            sh_product["price"],
                            ", ".join(sh_product["image_urls"]),
                            sh_product["query_brand"],
                            sh_product["query_category"],
                        ]
                    )
                rows.append([])
                rows.append([])
            rows.append(["---------------------------"])
            rows.append([])
        # Update the worksheet with the collected rows
        worksheet.append_rows(rows)

        # Apply bold formatting to the headers
        format_cell_range(
            worksheet, "A1:B1", CellFormat(textFormat=TextFormat(bold=True))
        )
        format_cell_range(
            worksheet, "A2:B2", CellFormat(textFormat=TextFormat(bold=True))
        )
        format_cell_range(
            worksheet, "A3:B3", CellFormat(textFormat=TextFormat(bold=True))
        )

    return spreadsheet.url
