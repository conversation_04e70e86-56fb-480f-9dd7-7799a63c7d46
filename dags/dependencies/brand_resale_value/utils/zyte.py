ZYTE_ENDPOINT = "https://api.zyte.com/v1/extract"
ZYTE_API_KEY = "7156bdf7b6254a7199ae30844cfa8313"

from base64 import b64decode
import aiohttp

from dependencies.brand_resale_value.utils.logger import logger


async def zyte_extract(
    session, url: str, browserHtml: bool = False, additional_queries: dict = {}
):
    """
    Extract data from a given URL using Zyte API
    """
    try:
        json_body = {"httpResponseBody": True}

        if browserHtml:
            json_body = {
                "browserHtml": True,
            }
        async with session.post(
            ZYTE_ENDPOINT,
            auth=aiohttp.BasicAuth(ZYTE_API_KEY, ""),
            json={
                "url": url,
                **json_body,
                **additional_queries,
            },
        ) as response:
            response.raise_for_status()
            res_json = await response.json()
            if "httpResponseBody" in res_json:
                res_json["httpResponseBody"] = b64decode(res_json["httpResponseBody"])
            return res_json
    except Exception as e:
        logger.error(f"An error occurred while fetching from Zyte: {e}", exc_info=True)
        return None
