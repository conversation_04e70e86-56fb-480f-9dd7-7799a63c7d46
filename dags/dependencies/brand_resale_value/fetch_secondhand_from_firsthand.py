from typing import Dict, List, Any
from urllib.parse import quote
import uuid
import aiohttp
import asyncio
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
from dependencies.brand_resale_value.utils.scrapers.google_shopping import (
    parse_google_shopping_results,
)
from dependencies.brand_resale_value.utils.scrapers.ebay_shopping import (
    parse_ebay_search_results,
)
from dependencies.brand_resale_value.utils.zyte import ZYTE_API_KEY, zyte_extract
from dependencies.brand_resale_value.utils.firsthand_sites import (
    normalize_string,
    is_firsthand_product,
)
from dependencies.brand_resale_value.utils.batch_proces import (
    prepare_tasks,
    run_in_parallel,
)
from dependencies.brand_resale_value.utils.logger import logger
from airflow.decorators import task, task_group
from airflow.operators.python import ShortCircuitOperator
from zyte_api import AsyncZyteAPI
from tqdm.asyncio import tqdm

"""
JSON input format:
{
    "secondhand_product": {
        "id": "sh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
        "query_brand": "brand1",
        "query_category": "category1"
    },
    "firsthand_product": {
        "id": "fh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"],
        "query_brand": "brand1",
        "query_category": "category1"
    },
    "gpt_response": {
        "refined_product_name": "product1",
        "category": "category1",
        "color": "color1",
        "unmatched_confidence": 0
    }
}

JSON output format: 
{
    "brand": "brand1",
    "category": "category1",
    "firsthand_product": {
        "id": "fh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    },
    "secondhand_options": [
        {
            "id": "sh_uuid",
            "source": "source1",
            "source_id": "source_id1",
            "brand": "brand1",
            "name": "product1",
            "description": "description1",
            "link": "link1",
            "price": 0,
            "image_urls": ["image_url1"]
        }
    ]
}
"""


async def get_secondhand_products_from_google(
    session, brand: str, title: str, category: str, product_price: str
) -> List[Dict[str, Any]]:
    try:
        queries = [
            f"Used {title}",
            f"Used The Real Real {title}",
            f"Used Mercari {title}",
        ]
        html_responses = []
        for query in queries:
            encoded_query = quote(query)
            url = (
                f"https://www.google.com/search?tbm=shop&q={encoded_query}&hl=en&gl=US"
            )

            res = await session.get(
                {
                    "url": url,
                    "browserHtml": True,
                }
            )
            html_responses.append(res)

        html_responses = [res["browserHtml"] for res in html_responses]

        products = []
        for html_content in html_responses:
            try:
                products.extend(parse_google_shopping_results(html_content))
            except Exception as e:
                logger.error(f"Failed to parse Google Shopping HTML content: {e}")

        filtered_products = [
            prod
            for prod in products
            if prod.get("price")
            and float(prod["price"]) < product_price
            and not is_firsthand_product("", normalize_string(prod.get("source", "")))
        ]

        return [
            {
                "id": "sh_" + str(uuid.uuid4()),
                "source": prod.get("source", ""),
                "source_id": prod.get("source_id", ""),
                "brand": prod.get("brand", ""),
                "name": prod.get("name", ""),
                "description": prod.get("description", ""),
                "link": prod.get("link", ""),
                "price": prod.get("price"),
                "image_urls": (
                    [prod.get("image_url", "")] if prod.get("image_url") else []
                ),
                "query_brand": brand,
                "query_category": category,
            }
            for prod in filtered_products
        ]
    except Exception as e:
        logger.error(f"Error in get_secondhand_products_from_google: {e}")
        return []


async def get_secondhand_products_from_ebay(
    session, brand: str, title: str, category: str, product_price: str
) -> List[Dict[str, Any]]:
    try:
        query = f"Used {title}"
        encoded_query = quote(query)
        url = f"https://www.ebay.com/sch/i.html?_nkw={encoded_query}&GLOBAL-ID=EBAY-US"

        try:
            res = await session.get(
                {
                    "url": url,
                    "httpResponseBody": True,
                }
            )
            rendered_html = res.get("httpResponseBody", "")
        except Exception as e:
            logger.error(f"Failed to fetch eBay search results: {e}")
            return []

        try:
            products = parse_ebay_search_results(rendered_html)
        except Exception as e:
            logger.error(f"Failed to parse eBay HTML content: {e}")
            return []

        filtered_products = [
            prod
            for prod in products
            if prod.get("price")
            and float(prod["price"]) < product_price
            and not is_firsthand_product("", normalize_string(prod.get("source", "")))
        ]

        return [
            {
                "id": "sh_" + str(uuid.uuid4()),
                "source": prod.get("source", ""),
                "source_id": prod.get("source_id", ""),
                "brand": prod.get("brand", ""),
                "name": prod.get("name", ""),
                "description": prod.get("description", ""),
                "link": prod.get("link", ""),
                "price": prod.get("price"),
                "image_urls": (
                    [prod.get("image_url", "")] if prod.get("image_url") else []
                ),
                "query_brand": brand,
                "query_category": category,
            }
            for prod in filtered_products
        ]
    except Exception as e:
        logger.error(f"Error in get_secondhand_products_from_ebay: {e}")
        return []


async def find_secondhand_options(
    session, brand, category, fh_product, original_sh_product
):
    product_name = fh_product.get("name")
    product_price = fh_product.get("price")

    google_sh_products = await get_secondhand_products_from_google(
        session, brand, product_name, category, product_price
    )
    ebay_sh_products = await get_secondhand_products_from_ebay(
        session, brand, product_name, category, product_price
    )
    data_streams = [
        google_sh_products,
        ebay_sh_products,
    ]
    results = [item for sublist in data_streams for item in sublist]

    logger.info(
        f"Found {len(results)} secondhand products for {brand} {product_name} in {category}"
    )

    return {
        "brand": brand,
        "category": category,
        "secondhand_original": original_sh_product,
        "firsthand_product": fh_product,
        "query": "",
        "secondhand_options": results,
    }


@task_group
def fetch_secondhand_from_firsthand_task_group(
    input_gcs_path: str, output_gcs_path: str, batch_size: int
):
    def not_in_cache(gcs_path):
        return not check_cache_in_gcs(gcs_path)

    check_cache = ShortCircuitOperator(
        task_id="check_cache_in_group",
        python_callable=not_in_cache,
        op_args=[output_gcs_path],
        ignore_downstream_trigger_rules=False,
    )

    @task
    def fetch_secondhand_from_firsthand(
        input_gcs_path: str, output_gcs_path: str, batch_size: int
    ):
        logger.info("Starting collection of secondhand products")
        exact_match_firsthand_products = load_jsonl_from_gcs(input_gcs_path)
        logger.info(input_gcs_path)
        # Split the data into batches
        batches = [
            exact_match_firsthand_products[i : i + batch_size]
            for i in range(0, len(exact_match_firsthand_products), batch_size)
        ]

        return batches

    @task
    def process_batch(batch):
        async def gather_tasks(batch):
            tasks = []
            zyte_client = AsyncZyteAPI(api_key=ZYTE_API_KEY, n_conn=20)
            async with zyte_client.session() as session:
                for item in batch:
                    original_sh_product = item.get("secondhand_product")
                    query_brand = original_sh_product.get("query_brand")
                    query_category = original_sh_product.get("query_category")
                    fh_product = item.get("firsthand_product")
                    tasks.append(
                        find_secondhand_options(
                            session,
                            query_brand,
                            query_category,
                            fh_product,
                            original_sh_product,
                        )
                    )
                results = []
                for task in tqdm(
                    asyncio.as_completed(tasks),
                    total=len(tasks),
                ):
                    results.append(await task)
                return results

        output_data = []
        for i in range(0, len(batch), 100):
            sub_batch = batch[i : i + 100]
            output_data.extend(asyncio.run(gather_tasks(sub_batch)))
            logger.info(f"Processed {len(output_data)} / {len(batch)} records")
        logger.info(f"Found secondhand options for {len(output_data)} products")
        random_gcs_path = (
            output_gcs_path.split(".jsonl")[0] + f"/random_{uuid.uuid4()}.jsonl"
        )
        store_data_to_jsonl_in_gcs(random_gcs_path, output_data)
        return random_gcs_path

    @task
    def store_output_data(output_data_files, output_gcs_path):
        output_data = []
        for file in output_data_files:
            data = load_jsonl_from_gcs(file)
            output_data.extend(data)
        # Store the output data to the GCS bucket
        store_data_to_jsonl_in_gcs(output_gcs_path, output_data)
        logger.info(f"Uploaded {len(output_data)} records to {output_gcs_path}")

    batches = check_cache >> fetch_secondhand_from_firsthand(
        input_gcs_path, output_gcs_path, batch_size
    )

    output_data_files = process_batch.expand(batch=batches)

    store_output_data(output_data_files, output_gcs_path)
