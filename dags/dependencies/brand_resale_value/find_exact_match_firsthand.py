import io
import json
import openai
import concurrent.futures
from dependencies.brand_resale_value.utils.constants import (
    OPENAI_API_KEY,
    IS_DEVELOPMENT,
)
from dependencies.brand_resale_value.utils.exact_match import (
    calculate_word_match_percentage,
    get_words,
    build_exact_match_prompt,
    normalize,
    coarse_text_match,
)
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
import asyncio

"""
JSON input format:
{
    "brand": "brand1",
    "category": "category1",
    "secondhand_product": {
        "id": "sh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    },
    "firsthand_options": [
        {
            "id": "fh_uuid",
            "brand": "brand1",
            "name": "product1",
            "description": "description1",
            "link": "link1",
            "price": 0,
            "image_urls": ["image_url1"]
        }
    ]
}

JSON output format:
{
    "batch_ids": ["batch_id1", "batch_id2"],
    "file_ids": ["file_id1", "file_id2"]
}
"""

client = openai.AsyncOpenAI(api_key=OPENAI_API_KEY)
sync_client = openai.Client(api_key=OPENAI_API_KEY)


def find_exact_match_firsthand_products(input_gcs_path: str, output_gcs_path: str):
    if check_cache_in_gcs(output_gcs_path):
        print("Output already exists in the GCS bucket")
        if IS_DEVELOPMENT:
            return []
        else:
            batch_ids = load_jsonl_from_gcs(output_gcs_path)[0]["batch_ids"]
            return batch_ids

    secondhand_product_data = load_jsonl_from_gcs(input_gcs_path)

    batch_inputs = []
    for row in secondhand_product_data:
        row_inputs = []
        secondhand_product = row["secondhand_product"]
        firsthand_options = row["firsthand_options"]
        for firsthand_product in firsthand_options:
            if coarse_text_match(firsthand_product["name"], secondhand_product["name"]):
                messages = build_exact_match_prompt(
                    secondhand_product, firsthand_product
                )
                row_inputs.append(
                    {
                        "custom_id": f"{secondhand_product['id']}__{firsthand_product['id']}",
                        "method": "POST",
                        "url": "/v1/chat/completions",
                        "body": {
                            "model": "gpt-4o-mini-2024-07-18",
                            "messages": messages,
                            "response_format": {"type": "json_object"},
                            "max_tokens": 1000,
                        },
                    }
                )
        print(
            f"{firsthand_product['name']} has {len(row_inputs)} exact matches options, {len(firsthand_options)} total"
        )
        batch_inputs.extend(row_inputs[:10])  # Limit to 10 per secondhand product

    print(f"Creating exact match prompts for {len(batch_inputs)} pairs")

    if IS_DEVELOPMENT:
        file_id = execute_gpt_prompts_in_dev(batch_inputs)
        store_data_to_jsonl_in_gcs(
            output_gcs_path, [{"batch_ids": [], "file_ids": [file_id]}]
        )
        return []
    else:
        batch_ids = upload_prompts_to_gpt(batch_inputs)
        store_data_to_jsonl_in_gcs(output_gcs_path, [{"batch_ids": batch_ids}])
        return batch_ids


def upload_prompts_to_gpt(prompts: list[dict]):
    """
    Production: Upload the prompts to GPT using the file API + batch creation
    """
    batch_size = 50000
    batches = [prompts[i : i + batch_size] for i in range(0, len(prompts), batch_size)]
    batch_ids = []
    for i, batch in enumerate(batches, start=1):
        print(f"Uploading batch {i} of {len(batches)} prompts to GPT...")
        jsonl_content = "\n".join([json.dumps(prompt) for prompt in batch])
        jsonl_file_data = io.BytesIO(jsonl_content.encode("utf-8"))

        uploaded_file = sync_client.files.create(file=jsonl_file_data, purpose="batch")

        print(f"Batch {i} uploaded successfully with file ID: {uploaded_file.id}")
        batch_res = sync_client.batches.create(
            input_file_id=uploaded_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h",
        )

        print(f"Batch {i} submitted successfully with batch ID: {batch_res.id}")
        batch_ids.append(batch_res.id)
    return batch_ids


def execute_gpt_prompts_in_dev(prompts: list[dict]) -> str:
    print(f"Executing GPT prompts in parallel... {len(prompts)} prompts")
    """
    Development: Execute each prompt directly in parallel using asyncio,
    gather results into a JSONL string, upload to OpenAI Files, and return the file ID.
    """

    async def call_openai_api(prompt: dict) -> dict:
        model = prompt["body"].get("model")
        messages = prompt["body"]["messages"]
        max_tokens = prompt["body"].get("max_tokens", 1000)

        try:
            response = await client.chat.completions.create(
                model=model, messages=messages, max_tokens=max_tokens
            )
            # Save the entire response or just the content
            return {
                "custom_id": prompt["custom_id"],
                "method": prompt["method"],
                "url": prompt["url"],
                "body": prompt["body"],
                # Convert to dict for easier JSON serialization
                "response": response.to_dict(),
            }
        except Exception as e:
            # Return a record with error info if desired
            return {
                "custom_id": prompt["custom_id"],
                "error": str(e),
            }

    async def gather_results():
        results = []
        batch_size = 500
        batches = [
            prompts[i : i + batch_size] for i in range(0, len(prompts), batch_size)
        ]

        for batch in batches:
            tasks = [call_openai_api(p) for p in batch]
            results.extend(await asyncio.gather(*tasks))
            print(f"Processed batch: {len(results) } / {len(prompts)}")

        return results

    results = asyncio.run(gather_results())

    # Prepare JSONL content that mimics a batch output
    jsonl_content = "\n".join(json.dumps(item) for item in results)
    jsonl_file_data = io.BytesIO(jsonl_content.encode("utf-8"))

    # Upload that file to OpenAI
    uploaded_file = sync_client.files.create(file=jsonl_file_data, purpose="batch")

    print(f"Uploaded direct-execution results file with ID: {uploaded_file.id}")

    return uploaded_file.id
