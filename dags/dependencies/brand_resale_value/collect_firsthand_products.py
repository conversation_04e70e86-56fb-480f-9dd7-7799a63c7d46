import json
import openai
from dependencies.brand_resale_value.utils.constants import (
    FIRSTHAND_PRODUCTS_OF_SECONDHAND_BUCKET,
    OPENAI_API_KEY,
    IS_DEVELOPMENT,
)
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
from dependencies.brand_resale_value.utils.logger import logger

"""
JSON input format:
{
    "batch_ids": ["batch_id1", "batch_id2"],
    "file_ids": ["file_id1", "file_id2"]
}

JSON output format:
{
    "brand": "brand1",
    "category": "category1",
    "secondhand_product": {
        "id": "sh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    },
    "firsthand_product": {
        "id": "fh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    },
    "gpt_response": {
        "exact_match": true,
        "reason": "Optional explanation."
    }
}
"""

client = openai.Client(api_key=OPENAI_API_KEY)


def collect_exact_match_firsthand_products(input_gcs_path: str, output_gcs_path: str):
    logger.info("Collecting first hand products from GPT based on the batch id")

    if check_cache_in_gcs(output_gcs_path):
        logger.info("Output already exists in the GCS bucket")
        return

    # Load the jsonl file from the bucket
    jsonl_res = load_jsonl_from_gcs(input_gcs_path)
    batch_ids = jsonl_res[0]["batch_ids"]

    # Collect the results from GPT
    gpt_outputs = []

    if IS_DEVELOPMENT:
        file_ids = jsonl_res[0]["file_ids"]
        for file_id in file_ids:
            gpt_outputs.extend(download_batch_results(file_id))
    else:
        for batch_id in batch_ids:
            response = client.batches.retrieve(batch_id)
            results = download_batch_results(response.output_file_id)
            gpt_outputs.extend(results)

    secondhand_to_firsthand_mapping = {}
    for row in gpt_outputs:
        secondhand_id, firsthand_id = row["custom_id"].split("__")
        if IS_DEVELOPMENT:
            raw_content = row["response"]["choices"][0]["message"]["content"]
        else:
            raw_content = row["response"]["body"]["choices"][0]["message"]["content"]
        if raw_content.startswith("```") and raw_content.endswith("```"):
            raw_content = "\n".join(raw_content.split("\n")[1:-1])
        try:
            parsed_response = json.loads(raw_content)
        except:
            logger.error(f"Error parsing JSON: {raw_content}")
            continue
        if secondhand_id not in secondhand_to_firsthand_mapping:
            secondhand_to_firsthand_mapping[secondhand_id] = {
                firsthand_id: parsed_response
            }
        else:
            secondhand_to_firsthand_mapping[secondhand_id][
                firsthand_id
            ] = parsed_response

    # Fetch secondhand products
    file_name = output_gcs_path.split("/")[-1]
    secondhand_firsthand_mapping = load_jsonl_from_gcs(
        f"{FIRSTHAND_PRODUCTS_OF_SECONDHAND_BUCKET}/{file_name}"
    )
    logger.info(f"Loaded {len(secondhand_firsthand_mapping)} rows")

    exact_matches = []
    no_matches = 0
    for row in secondhand_firsthand_mapping:
        secondhand_product = row["secondhand_product"]
        firsthand_options = row["firsthand_options"]
        secondhand_id = secondhand_product["id"]
        for firsthand_product in firsthand_options:
            firsthand_id = firsthand_product["id"]
            gpt_response = secondhand_to_firsthand_mapping.get(secondhand_id, {}).get(
                firsthand_id, {}
            )
            if not gpt_response:
                # logger.info(f"No GPT response for {secondhand_id} and {firsthand_id}")
                no_matches += 1
                continue
            is_exact_match = gpt_response.get("is_exact_match", False)
            if is_exact_match:
                exact_matches.append(
                    {
                        "brand": row["brand"],
                        "category": row["category"],
                        "firsthand_product": firsthand_product,
                        "secondhand_product": secondhand_product,
                        "gpt_response": gpt_response,
                    }
                )
                break
    print(f"{len(exact_matches)} exact matches found")
    print(f"{no_matches} no matches found")
    print(exact_matches)
    store_data_to_jsonl_in_gcs(output_gcs_path, exact_matches)


def download_batch_results(file_id: str):
    """
    Download the batch results from GPT
    """
    output_file = client.files.content(file_id=file_id)
    file_content = output_file.text
    data = [json.loads(line) for line in file_content.strip().split("\n")]
    return data
