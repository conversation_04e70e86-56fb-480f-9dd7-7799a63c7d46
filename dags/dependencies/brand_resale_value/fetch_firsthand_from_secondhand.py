import json
from urllib.parse import quote
import uuid
import aiohttp
import asyncio

import requests
from dependencies.brand_resale_value.utils.constants import (
    IS_DEVELOPMENT,
    OPENAI_API_KEY,
)
from dependencies.brand_resale_value.utils.scrapers.google_shopping import (
    parse_google_shopping_results,
)
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
from dependencies.brand_resale_value.utils.zyte import (
    ZYTE_API_KEY,
    zyte_extract
)
from dependencies.brand_resale_value.utils.logger import logger
from dependencies.brand_resale_value.utils.firsthand_sites import (
    is_firsthand_product,
    normalize_string,
)
import openai
from airflow.decorators import task, task_group
from airflow.operators.python import ShortCircuitOperator
from tqdm.asyncio import tqdm
from zyte_api import AsyncZyteAPI


"""
JSONL input file format
{
    "brand": "brand1",
    "category": "category1",
    "secondhand_products": [
        {
            "name": "product1",
            "price": 100,
            "condition": "good"
        }
    ]
}

JSON ouptput format:
{
    "brand": "brand1",
    "category": "category1",
    "secondhand_product": {
        "id": "sh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    },
    "firsthand_options": [
        {
            "id": "fh_uuid",
            "brand": "brand1",
            "name": "product1",
            "description": "description1",
            "link": "link1",
            "price": 0,
            "image_urls": ["image_url1"]
        }
    ]
}
"""

client = openai.Client(api_key=OPENAI_API_KEY)


@task_group
def fetch_firsthand_from_secondhand_task_group(
    input_gcs_path: str, output_gcs_path: str, batch_size: int
):
    def not_in_cache(gcs_path):
        return not check_cache_in_gcs(gcs_path)

    check_cache = ShortCircuitOperator(
        task_id="check_cache_in_group",
        python_callable=not_in_cache,
        op_args=[output_gcs_path],
        ignore_downstream_trigger_rules=False,
    )

    @task
    def fetch_firsthand_from_secondhand(input_gcs_path: str, batch_size: int):
        """
        Collect firsthand products from secondhand products
        The output will be stored in GCS.
        Note that, at this step, we don't need to figure out the exact match;
        The next step will figure it out through either the batch GPT or additional GPU service
        """

        # Load the refined queries file from the bucket
        refined_queries_data = load_jsonl_from_gcs(input_gcs_path)

        logger.info("Collecting firsthand products from secondhand products")

        # Split the data into batches
        batches = [
            refined_queries_data[i : i + batch_size]
            for i in range(0, len(refined_queries_data), batch_size)
        ]

        return batches

    @task
    def process_batch(batch):
        logger.info(f"Processing batch of {len(batch)} products")

        async def gather_output_data():
            output_data = []
            zyte_client = AsyncZyteAPI(api_key=ZYTE_API_KEY, n_conn=40)
            async with zyte_client.session() as session:
                tasks = []
                for row in batch:
                    brand = row["brand"] or ""
                    category = row["category"] or ""
                    query = row["refined_query"] or ""
                    secondhand_product = row["secondhand_product"]
                    if query == "":
                        continue
                    tasks.append(
                        find_firsthand_options(
                            session, brand, category, secondhand_product, query
                        )
                    )

                for task in tqdm(
                    asyncio.as_completed(tasks),
                    total=len(tasks),
                ):
                    output_data.append(await task)
            return output_data

        output_data = asyncio.run(gather_output_data())
        logger.info(f"Found firsthand options for {len(output_data)} products")
        random_gcs_path = (
            output_gcs_path.split(".jsonl")[0] + f"/random_{uuid.uuid4()}.jsonl"
        )
        store_data_to_jsonl_in_gcs(random_gcs_path, output_data)
        return random_gcs_path

    @task
    def store_output_data(output_data_files, output_gcs_path):
        output_data = []
        for file in output_data_files:
            data = load_jsonl_from_gcs(file)
            output_data.extend(data)
        # Store the output data to the GCS bucket
        store_data_to_jsonl_in_gcs(output_gcs_path, output_data)

    batches = check_cache >> fetch_firsthand_from_secondhand(input_gcs_path, batch_size)

    # Accessing individual components

    # Use them in your downstream task
    output_data_files = process_batch.expand(
        batch=batches,
    )

    store_output_data(output_data_files, output_gcs_path)


async def find_firsthand_options(session, brand, category, secondhand_product, query):
    """
    Find firsthand options for a secondhand product
    """
    firsthand_options = await find_firsthand_options_using_google_zyte(
        session, query, brand, category
    )

    return {
        "brand": brand,
        "category": category,
        "secondhand_product": secondhand_product,
        "query": query,
        "firsthand_options": firsthand_options,
    }


async def find_firsthand_options_using_google_zyte(
    session, query: str, brand: str, category: str
):
    try:
        encoded_query = quote(query)
        url = f"https://www.google.com/search?tbm=shop&q={encoded_query}&hl=en&gl=US"
        res = await session.get(
            {
                "url": url,
                "browserHtml": True,
            }
        )
        html_content = res.get("browserHtml", "")

        if not html_content:
            logger.error("No HTML content received from Zyte")
            return []

        products = []

        try:
            products.extend(parse_google_shopping_results(html_content))
        except Exception as e:
            logger.error(f"Failed to parse Google Shopping HTML content: {e}")

        filtered_products = [
            prod
            for prod in products
            if prod.get("price")
            and not is_firsthand_product("", normalize_string(prod.get("source", "")))
        ]

        return [
            {
                "id": "fh_" + str(uuid.uuid4()),
                "source": prod.get("source", ""),
                "source_id": prod.get("source_id", ""),
                "brand": prod.get("brand", ""),
                "name": prod.get("name", ""),
                "description": prod.get("description", ""),
                "link": prod.get("link", ""),
                "price": prod.get("price"),
                "image_urls": (
                    [prod.get("image_url", "")] if prod.get("image_url") else []
                ),
                "query_brand": brand,
                "query_category": category,
            }
            for prod in filtered_products
        ]
    except Exception as e:
        logger.error(f"Failed to extract data from Zyte: {e}")
        return []


async def find_firsthand_options_using_google_serp(
    session, query: str, brand: str, category: str
):
    """
    Find firsthand options for a secondhand product using SERP
    """
    query = quote(query)
    url = "https://serpapi.com/search"
    base_params = {
        "q": query,
        "api_key": "b3214bc481d7e6a06dacd02b462e38833b33429ad5ff0b1a9fccc0569afe4b12",
        "engine": "google_shopping",
        "direct_link": "true",
        "num": 100,
    }
    params = {**base_params}
    async with session.get(url, params=params) as response:
        data = await response.json()
        data = data["shopping_results"]

    return list(
        map(
            lambda x: {
                "id": "fh_" + str(uuid.uuid4()),
                "source": x.get("source", "google_serp"),
                "source_id": x.get("product_id", ""),
                "brand": x.get("brand", ""),
                "name": x.get("title", ""),
                "description": x.get("description", ""),
                "link": x.get("link", ""),
                "price": x.get("extracted_price"),
                "image_urls": x.get("thumbnails", []),
                "query_brand": brand,
                "query_category": category,
            },
            data,
        )
    )


async def scrape_product_link(session, link: str, brand: str, category: str) -> dict:
    """
    Scrape the product link using Zyte
    """
    data = await zyte_extract(
        session,
        link,
        additional_queries={
            "product": True,
            "productOptions": {"extractFrom": "httpResponseBody"},
        },
    )
    product = data["product"]
    return {
        "id": "fh_" + str(uuid.uuid4()),
        "source": "google",
        "source_id": product.get("sku", ""),
        "brand": product.get("brand", {}).get("name", ""),
        "name": product.get("name", ""),
        "description": product.get("description", ""),
        "link": link,
        "price": product.get("price"),
        "image_urls": (
            [product.get("mainImage", {}).get("url", "")]
            if product.get("mainImage")
            else []
        ),
        "query_brand": brand,
        "query_category": category,
    }


def download_batch_results(file_id: str):
    """
    Download the batch results from GPT
    """
    output_file = client.files.content(file_id=file_id)
    file_content = output_file.text
    data = [json.loads(line) for line in file_content.strip().split("\n")]
    return data
