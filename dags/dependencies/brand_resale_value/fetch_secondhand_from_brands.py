import os
from urllib.parse import quote, unquote
from dependencies.brand_resale_value.utils.constants import IS_DEVELOPMENT
from dependencies.brand_resale_value.utils.scrapers.ebay_shopping import (
    parse_ebay_search_results,
)
from dependencies.brand_resale_value.utils.logger import logger
from dependencies.brand_resale_value.utils.zyte import zyte_extract
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
import requests
import uuid
import aiohttp
import asyncio
from airflow.decorators import task, task_group
from airflow.exceptions import AirflowSkipException
from airflow.operators.python import ShortCircuitOperator

"""
JSONL input file format
{
    name: "brand1",
    categories: [
        "category1",
        "category2"
    ]
}

JSONL output file format
{
    "brand": "brand1",
    "category": "category1",
    "secondhand_products": [
        {
            "id": "sh_uuid",
            "source": "vestiaire",
            "source_id": "123",
            "brand": "brand1",
            "name": "product1",
            "description": "description1",
            "price": 100,
            "link": "https://www.vestiairecollective.com/product1",
            "imageUrls": [
                "https://images.vestiairecollective.com/images/resized/w=256,q=100/1.jpg"
            ],
        }
    ]
}
"""


def fetch_secondhand_from_brands_new(brands):
    """
    Collect secondhand products from a list of brands
    The output will be stored in GCS.
    """

    brand_categories = asyncio.run(find_categories_for_brands(brands))
    print(brand_categories)
    # save the new categories to GCS

    async def fetch_secondhand_products():
        output_data = []
        async with aiohttp.ClientSession() as session:
            tasks = []
            for brand in brand_categories:
                brand_name = brand["brand"]
                categories = brand["categories"]
                logger.info(
                    f"Processing brand: {brand_name} with categories: {categories}"
                )
                for category in categories:
                    tasks.append(
                        fetch_secondhand_products_for_category(
                            session, brand_name, category
                        )
                    )

            results = await asyncio.gather(*tasks)
            output_data.extend(results)
        return output_data

    output_data = asyncio.run(fetch_secondhand_products())
    return output_data


@task_group
def fetch_secondhand_from_brands_task_group(
    input_gcs_path: str, output_gcs_path: str, batch_size: int
):
    def not_in_cache(gcs_path):
        return not check_cache_in_gcs(gcs_path)

    check_cache = ShortCircuitOperator(
        task_id="check_cache_in_group",
        python_callable=not_in_cache,
        op_args=[output_gcs_path],
        ignore_downstream_trigger_rules=False,
        # If python_callable returns False, it will skip the downstream tasks in the group
    )

    @task
    def fetch_secondhand_from_brands(
        input_gcs_path: str, output_gcs_path: str, batch_size: int
    ):
        """
        Collect secondhand products from a list of brands
        The output will be stored in GCS.
        """
        logger.info("Collecting secondhand from brands:")
        logger.info(f"input_gcs_path: {input_gcs_path}")
        logger.info(f"output_gcs_path: {output_gcs_path}")

        # Load the jsonl file from the bucket
        brands = load_jsonl_from_gcs(input_gcs_path)
        brands = brands[:1]
        brand_categories = asyncio.run(find_categories_for_brands(brands))
        print(brand_categories)
        # save the new categories to GCS
        store_data_to_jsonl_in_gcs(input_gcs_path, brand_categories)

        # Split the data into batches
        batches = [
            brand_categories[i : i + batch_size]
            for i in range(0, len(brand_categories), batch_size)
        ]

        return batches

    @task
    def process_batch(batch):
        async def fetch_secondhand_products():
            output_data = []
            async with aiohttp.ClientSession() as session:
                tasks = []
                for brand in batch:
                    brand_name = brand["brand"]
                    categories = brand["categories"]
                    logger.info(
                        f"Processing brand: {brand_name} with categories: {categories}"
                    )
                    for category in categories:
                        tasks.append(
                            fetch_secondhand_products_for_category(
                                session, brand_name, category
                            )
                        )

                results = await asyncio.gather(*tasks)
                output_data.extend(results)
            return output_data

        output_data = asyncio.run(fetch_secondhand_products())
        return output_data

    @task
    def store_output_data(output_data, output_gcs_path):
        output_data = [item for sublist in output_data for item in sublist]
        # Store the output data to the GCS bucket
        store_data_to_jsonl_in_gcs(output_gcs_path, output_data)
        logger.info("Data successfully stored in GCS")

    batches = check_cache >> fetch_secondhand_from_brands(
        input_gcs_path, output_gcs_path, batch_size
    )

    output_data = process_batch.expand(batch=batches)

    store_output_data(output_data, output_gcs_path)


async def fetch_secondhand_products_for_category(
    session, brand_name: str, category: str
):
    """
    Fetch secondhand products given a brand and category
    """
    products = []
    query = brand_name + " " + category

    vestiaire_products = fetch_from_vestiaire(session, query, brand_name, category)
    ebay_products = fetch_from_ebay(session, query, brand_name, category)

    data_streams = [vestiaire_products, ebay_products]

    results = await asyncio.gather(*data_streams)

    # NOTE: Limit to 100 items for now
    for result in results:
        products.extend(result[:100])

    logger.info(
        f"{brand_name} in {category} has {len(products)} products: {len(results[0])} vestiaire, {len(results[1])} ebay"
    )

    return {
        "brand": brand_name,
        "category": category,
        "secondhand_products": products,
    }


async def fetch_from_vestiaire(
    session, query: str, brand_name: str, category: str
) -> list[dict]:
    url = "https://search.vestiairecollective.com/v1/product/search"
    IMAGE_ENDPOINT = (
        "https://images.vestiairecollective.com/images/resized/w=256,q=100,"
    )
    URL_ENDPOINT = "https://us.vestiairecollective.com"
    all_items = []
    offset = 0
    limit = 100
    MAX_PAGES = 2  # Limit to 200 items for now

    while True:
        body = {
            "pagination": {"offset": 0, "limit": 100},
            "fields": [
                "name",
                "description",
                "brand",
                "model",
                "price",
                "discount",
                "link",
                "sold",
                "local",
                "pictures",
                "colors",
                "size",
                "stock",
            ],
            "facets": {
                "fields": [
                    "brand",
                    "universe",
                    "stock",
                    "color",
                    "categoryLvl0",
                    "priceRange",
                    "price",
                    "condition",
                    "editorPicks",
                    "watchMechanism",
                    "discount",
                    "sold",
                    "sellerBadge",
                    "materialLvl0",
                ],
                "stats": ["price"],
            },
            "q": query,
            "sortBy": "relevance",
            "locale": {
                "country": "US",
                "currency": "USD",
                "language": "us",
                "sizeType": "US",
            },
            "options": {"innerFeedContext": "genericPLP"},
        }
        async with session.post(
            url,
            json=body,
            headers={
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
            },
        ) as resp:
            resp.raise_for_status()
            data = await resp.json()
            all_items.extend(data.get("items", []))
        pagination = data.get("paginationStats", {})
        total_hits = pagination.get("totalHits", 0)
        offset += limit
        MAX_PAGES -= 1
        if offset >= total_hits or MAX_PAGES <= 0:
            break
    if not all_items:
        logger.warning(f"vestiaire: No products found for {unquote(query)}")
        return []
    return list(
        map(
            lambda item: {
                "id": "sh_" + str(uuid.uuid4()),
                "source": "vestiaire",
                "source_id": item["id"],
                "brand": item.get("brand", {}).get("name"),
                "name": item.get("name"),
                "description": item.get("description"),
                "link": (URL_ENDPOINT + item.get("link") if item.get("link") else None),
                "price": (
                    item.get("price", {}).get("cents", 0) / 100
                    if item.get("price")
                    else None
                ),
                "image_urls": list(
                    map(lambda x: IMAGE_ENDPOINT + x, item.get("pictures", []))
                ),
                "query_brand": brand_name,
                "query_category": category,
            },
            all_items,
        )
    )


async def fetch_from_ebay(
    session, query: str, brand_name: str, category: str
) -> list[dict]:
    query = query.replace(" ", "+")
    query = quote(query)
    productList = []
    page = 1
    while True:
        search_url = f"https://www.ebay.com/sch/i.html?_nkw={query}&_pgn={page}&GLOBAL-ID=EBAY-US"
        zyte_data = await zyte_extract(session, search_url)
        ebay_html = zyte_data["httpResponseBody"]
        page_products = parse_ebay_search_results(ebay_html)

        if not page_products or len(page_products) < 30:
            break

        productList.extend(page_products)
        page += 1

        if page > 4:
            break

    if not productList:
        logger.warning(f"ebay: No products found for {unquote(query)}")
        return []

    return list(
        map(
            lambda prod: {
                "id": "sh_" + str(uuid.uuid4()),
                "source": prod.get("source", "ebay"),
                "source_id": prod.get("source_id"),
                "brand": prod.get("brand"),  # This is not available in the response
                "name": prod.get("name"),
                "description": prod.get(
                    "description"
                ),  # This is not available in the response
                "link": prod.get("link"),
                "price": prod.get("price"),
                "image_urls": (
                    [prod.get("image_url")] if prod.get("image_url") else []
                ),
                "query_brand": brand_name,
                "query_category": category,
            },
            productList,
        )
    )


async def find_categories_for_brands(brands: list[dict]):
    async def fetch_categories(session, brand):
        print(brand, brand.get("brand"), brand.get("categories"))

        query = quote(brand.get("brand"))
        url = "https://serpapi.com/search"
        base_params = {
            "q": query,
            "api_key": "b3214bc481d7e6a06dacd02b462e38833b33429ad5ff0b1a9fccc0569afe4b12",
            "engine": "google_shopping",
            "direct_link": "true",
            "num": 100,
        }
        params = {**base_params}

        async with session.get(url, params=params) as response:
            response.raise_for_status()
            data = await response.json()
            categories = set(brand.get("categories", []))
            filters = data.get("filters", [])
            for filter in filters:
                if filter.get("type") == "Category":
                    print(filter)
                    categories.update(
                        [category["text"].lower() for category in filter["options"]]
                    )
            return {"brand": brand["brand"], "categories": list(categories)}

    async with aiohttp.ClientSession() as session:
        tasks = [fetch_categories(session, brand) for brand in brands]
        results = await asyncio.gather(*tasks)
    return results
