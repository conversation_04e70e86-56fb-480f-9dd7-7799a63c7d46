def secondhand_exact_match_batch_response_check(response, **kwargs):
    """
    Check if the secondhand options batch process is completed.
    """
    ti = kwargs["ti"]
    json_res = response.json()
    batch_ids = ti.xcom_pull(task_ids="find_exact_match_secondhand_products")
    batches = json_res.get("data", [])
    completed_status = "completed"
    all_batches_completed = True
    for batch_id in batch_ids:
        batch = next((b for b in batches if b["id"] == batch_id), None)
        if not batch:
            raise ValueError(f"Batch ID {batch_id} not found in the response")

        if batch["status"] != completed_status:
            all_batches_completed = False
            break
    return all_batches_completed
