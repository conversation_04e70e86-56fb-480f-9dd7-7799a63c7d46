def refined_query_batch_response_check(response, **kwargs):
    """
    Check if the firsthand options batch process is completed.
    """
    ti = kwargs["ti"]
    json_res = response.json()
    batch_ids = ti.xcom_pull(task_ids="find_refined_search_queries")
    batches = json_res.get("data", [])
    completed_status = "completed"
    all_batches_completed = True
    for batch_id in batch_ids:
        batch = next((b for b in batches if b["id"] == batch_id), None)
        if not batch:
            raise Exception(f"Batch ID {batch_id} not found in the OpenAI response")
        if batch["status"] != completed_status:
            all_batches_completed = False
            break
    return all_batches_completed
