import io
import json
import openai
import concurrent.futures
from dependencies.brand_resale_value.utils.constants import (
    OPENAI_API_KEY,
    IS_DEVELOPMENT,
)
from dependencies.brand_resale_value.utils.exact_match import (
    get_words,
    build_exact_match_prompt,
    normalize,
    coarse_text_match,  # Import coarse_text_match
)
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
from dependencies.brand_resale_value.utils.logger import logger
import asyncio

client = openai.AsyncOpenAI(api_key=OPENAI_API_KEY)
sync_client = openai.Client(api_key=OPENAI_API_KEY)

"""
JSON input format:
{
    "brand": "brand1",
    "category": "category1",
    "firsthand_product": {
        "id": "fh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    },
    "secondhand_options": [
        {
            "id": "sh_uuid",
            "source": "source1",
            "source_id": "source_id1",
            "brand": "brand1",
            "name": "product1",
            "description": "description1",
            "link": "link1",
            "price": 0,
            "image_urls": ["image_url1"]
        }
    ]
}

JSON output format:
{
    "batch_ids": ["batch_id1", "batch_id2"],
    "file_ids": ["file_id1", "file_id2"]
}
"""


def find_exact_match_secondhand_products(input_gcs_path: str, output_gcs_path: str):
    try:
        if check_cache_in_gcs(output_gcs_path):
            logger.info("Output already exists in the GCS bucket")
            if IS_DEVELOPMENT:
                return []
            else:
                batch_ids = load_jsonl_from_gcs(output_gcs_path)[0]["batch_ids"]
                return batch_ids

        firsthand_product_data = load_jsonl_from_gcs(input_gcs_path)

        batch_inputs = []
        for row in firsthand_product_data:
            row_inputs = []
            firsthand_product = row.get("firsthand_product")
            if not firsthand_product:
                logger.warning(
                    f"Skipping entry due to missing firsthand product: {row}"
                )
                continue

            for secondhand_product in row.get("secondhand_options", []):
                if coarse_text_match(
                    firsthand_product["name"], secondhand_product["name"], 50
                ):
                    messages = build_exact_match_prompt(
                        firsthand_product, secondhand_product
                    )
                    row_inputs.append(
                        {
                            "custom_id": f"{firsthand_product['id']}__{secondhand_product['id']}",
                            "method": "POST",
                            "url": "/v1/chat/completions",
                            "body": {
                                "model": "gpt-4o-mini-2024-07-18",
                                "messages": messages,
                                "response_format": {"type": "json_object"},
                                "max_tokens": 1000,
                            },
                        }
                    )
            logger.info(
                f"{firsthand_product['name']} has {len(row_inputs)} exact matches options, {len(row.get('secondhand_options', []))} total"
            )
            batch_inputs.extend(row_inputs[:20])  # Limit to 20 per firsthand product

        logger.info(f"Creating exact match prompts for {len(batch_inputs)} pairs")

        if IS_DEVELOPMENT:
            file_id = execute_gpt_prompts_in_dev(batch_inputs)
            store_data_to_jsonl_in_gcs(
                output_gcs_path, [{"batch_ids": [], "file_ids": [file_id]}]
            )
            return []
        else:
            batch_ids = upload_prompts_to_gpt(batch_inputs)
            store_data_to_jsonl_in_gcs(output_gcs_path, [{"batch_ids": batch_ids}])
            return batch_ids
    except Exception as e:
        logger.error(f"Error in find_exact_match_secondhand_products: {e}")
        return []


def upload_prompts_to_gpt(prompts: list[dict]):
    try:
        batch_size = 50000
        batches = [
            prompts[i : i + batch_size] for i in range(0, len(prompts), batch_size)
        ]
        batch_ids = []

        for i, batch in enumerate(batches, start=1):
            logger.info(f"Uploading batch {i} of {len(batches)} prompts to GPT...")
            jsonl_content = "\n".join(json.dumps(prompt) for prompt in batch)
            jsonl_file_data = io.BytesIO(jsonl_content.encode("utf-8"))

            uploaded_file = sync_client.files.create(
                file=jsonl_file_data, purpose="batch"
            )

            logger.info(
                f"Batch {i} uploaded successfully with file ID: {uploaded_file.id}"
            )

            batch_res = sync_client.batches.create(
                input_file_id=uploaded_file.id,
                endpoint="/v1/chat/completions",
                completion_window="24h",
            )

            logger.info(
                f"Batch {i} submitted successfully with batch ID: {batch_res.id}"
            )
            batch_ids.append(batch_res.id)

        return batch_ids
    except Exception as e:
        logger.error(f"Error in upload_prompts_to_gpt: {e}")
        return []


def execute_gpt_prompts_in_dev(prompts: list[dict]) -> str:
    async def call_openai_api(prompt: dict) -> dict:
        model = prompt["body"].get("model")
        messages = prompt["body"]["messages"]
        max_tokens = prompt["body"].get("max_tokens", 1000)

        try:
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
            )
            return {
                "custom_id": prompt["custom_id"],
                "method": prompt["method"],
                "url": prompt["url"],
                "body": prompt["body"],
                "response": response.to_dict(),
            }
        except Exception as e:
            return {
                "custom_id": prompt["custom_id"],
                "error": str(e),
            }

    async def gather_results(prompts: list[dict]):
        results = []
        batch_size = 500
        batches = [
            prompts[i : i + batch_size] for i in range(0, len(prompts), batch_size)
        ]

        for batch in batches:
            tasks = [call_openai_api(p) for p in batch]
            results.extend(await asyncio.gather(*tasks))
            print(f"Processed batch: {len(results) } / {len(prompts)}")

        return results

    results = asyncio.run(gather_results(prompts))

    jsonl_content = "\n".join(json.dumps(r) for r in results)
    jsonl_file_data = io.BytesIO(jsonl_content.encode("utf-8"))

    uploaded_file = sync_client.files.create(file=jsonl_file_data, purpose="batch")
    logger.info(f"Uploaded direct-execution results file with ID: {uploaded_file.id}")

    return uploaded_file.id
