from dependencies.brand_resale_value.utils.gsheet import create_google_sheet
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
from dependencies.brand_resale_value.utils.logger import logger

"""
JSON input format:
{
    "firsthand_product": {
        "id": "fh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"],
        "query_brand": "brand1",
        "query_category": "category1"
    },
    "secondhand_product": {
        "id": "sh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
        "query_brand": "brand1",
        "query_category": "category1"
    },
    "gpt_response": {
        "refined_product_name": "product1",
        "category": "category1",
        "color": "color1",
        "unmatched_confidence": 0
    }
}

JSON OUTPUT: 
{
    "brand": "brand1",
    "brand_resale_value": 0,
    "categories": [
        {
            "category": "category1",
            "brand_category_value": 0,
            "product_count": 0,
            "product_data": [
                {
                    "firsthand_product": {
                        "id": "fh_uuid",
                        "brand": "brand1",
                        "name": "product1",
                        "description": "description1",
                        "link": "link1",
                        "price": 0,
                        "image_urls": ["image_url1"],
                        "query_brand": "brand1",
                        "query_category": "category1"
                    },
                    "average_resale_value": 0,
                    "secondhand_count": 0,
                    "secondhand_products: [
                        {
                            "id": "sh_uuid",
                            "brand": "brand1",
                            "name": "product1",
                            "description": "description1",
                            "link": "link1",
                            "price": 0,
                            "image_urls": ["image_url1"]
                            "query_brand": "brand1",
                            "query_category": "category1"
                        },
                    ]
                }
            ]
        }
    ]
}
"""


def calculate_brand_resale_value(input_gcs_path: str, output_gcs_path: str):
    """
    Calculate the brand resale value based on product data
    Store the result in GCS
    """
    logger.info("Calculating brand resale value")

    if check_cache_in_gcs(output_gcs_path):
        logger.info("Output already exists in the GCS bucket")
        return

    try:
        exact_matches_data = load_jsonl_from_gcs(input_gcs_path)
    except Exception as e:
        logger.error(f"Error loading data from GCS: {e}")
        return "Error loading data"

    brand_resale_values = {}

    resale_price_aggregation = {}

    for item in exact_matches_data:
        firsthand_product = item.get("firsthand_product", {})
        secondhand_original = item.get("secondhand_original", {})
        secondhand_product = item.get("secondhand_product", {})
        firsthand_product_id = firsthand_product.get("id", "")

        try:
            firsthand_price = float(
                str(firsthand_product.get("price", "0"))
                .replace("$", "")
                .replace(",", "")
            )
        except ValueError:
            firsthand_price = 0

        try:
            secondhand_price = float(
                str(secondhand_product.get("price", "0"))
                .replace("$", "")
                .replace(",", "")
            )
        except ValueError:
            secondhand_price = 0

        if firsthand_product_id not in resale_price_aggregation:
            resale_price_aggregation[firsthand_product_id] = {
                "firsthand_product": firsthand_product,
                "secondhand_original": secondhand_original,
                "total_resale_price": 0,
                "secondhand_count": 0,
                "secondhand_products": [],
            }

        resale_price_aggregation[firsthand_product_id][
            "total_resale_price"
        ] += secondhand_price
        resale_price_aggregation[firsthand_product_id]["secondhand_count"] += 1
        resale_price_aggregation[firsthand_product_id]["secondhand_products"].append(
            secondhand_product
        )

    for product_id, data in resale_price_aggregation.items():
        firsthand_product = data["firsthand_product"]
        secondhand_original = data["secondhand_original"]
        total_resale_price = data["total_resale_price"]
        secondhand_count = data["secondhand_count"]
        secondhand_products = data["secondhand_products"]

        brand = firsthand_product.get("query_brand", "").lower()
        category = firsthand_product.get("query_category", "").lower()

        average_resale_value = (
            total_resale_price / secondhand_count if secondhand_count > 0 else 0
        )

        try:
            firsthand_price = float(
                str(firsthand_product.get("price", "0"))
                .replace("$", "")
                .replace(",", "")
            )
        except ValueError:
            firsthand_price = 0

        if firsthand_price > 0 and average_resale_value > 0:
            resale_percentage = (average_resale_value / firsthand_price) * 100
        else:
            resale_percentage = 0

        if brand not in brand_resale_values:
            brand_resale_values[brand] = {}

        if category not in brand_resale_values[brand]:
            brand_resale_values[brand][category] = {
                "total_resale_percentage": 0,
                "product_count": 0,
                "product_data": [],
            }

        brand_resale_values[brand][category][
            "total_resale_percentage"
        ] += resale_percentage
        brand_resale_values[brand][category]["product_count"] += 1
        brand_resale_values[brand][category]["product_data"].append(
            {
                "firsthand_product": firsthand_product,
                "secondhand_original": secondhand_original,
                "average_resale_value": average_resale_value,
                "secondhand_count": secondhand_count,
                "secondhand_products": secondhand_products,
            }
        )

    result = []
    for brand, categories in brand_resale_values.items():
        brand_entry = {"brand": brand, "brand_resale_value": 0, "categories": []}
        brand_category_values = []

        for category, data in categories.items():
            if data["product_count"] > 0:
                brand_category_value = (
                    data["total_resale_percentage"] / data["product_count"]
                )
                brand_entry["categories"].append(
                    {
                        "category": category,
                        "brand_category_value": brand_category_value,
                        "product_count": data["product_count"],
                        "product_data": data["product_data"],
                    }
                )
                brand_category_values.append(brand_category_value)

        if brand_category_values:
            brand_entry["brand_resale_value"] = sum(brand_category_values) / len(
                brand_category_values
            )

        result.append(brand_entry)

    try:
        store_data_to_jsonl_in_gcs(output_gcs_path, result)
        logger.info(f"Results successfully written to {output_gcs_path}")
        task_id = output_gcs_path.split("/")[-1].split(".")[0]
        sheet_url = create_google_sheet(result, f"resale_value_{task_id}")
        logger.info(f"Google Sheet created: {sheet_url}")
    except Exception as e:
        logger.error(f"Error writing results to GCS: {e}")
