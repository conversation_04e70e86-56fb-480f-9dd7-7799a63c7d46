import json
import openai
from dependencies.brand_resale_value.utils.constants import (
    OPENAI_API_KEY,
    IS_DEVELOPMENT,
    SECONDHAND_PRODUCTS_OF_FIRSTHAND_BUCKET,
)
from dependencies.brand_resale_value.utils.exact_match import (
    get_words,
    calculate_word_match_percentage,
    fuzzy_details_match,
    normalize,
)
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)
from dependencies.brand_resale_value.utils.logger import logger

"""
JSON input format:
{
    "batch_ids": ["batch_id1", "batch_id2"],
    "file_ids": ["file_id1", "file_id2"]
}

JSON output format:
{
    "brand": "brand1",
    "category": "category1",
    "firsthand_product": {
        "id": "fh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    },
    "secondhand_product": {
        "id": "sh_uuid",
        "brand": "brand1",
        "name": "product1",
        "description": "description1",
        "link": "link1",
        "price": 0,
        "image_urls": ["image_url1"]
    },
    "gpt_response": {
        "refined_product_name": "product1",
        "category": "category1",
        "color": "color1",
        "unmatched_confidence": 0
    }
}
"""

client = openai.Client(api_key=OPENAI_API_KEY)


def download_batch_results(file_id: str):
    try:
        output_file = client.files.content(file_id=file_id)
        file_content = output_file.text
        data = [json.loads(line) for line in file_content.strip().split("\n")]
        return data
    except Exception as e:
        logger.error(f"Error downloading batch results for file ID {file_id}: {e}")
        return []


def collect_exact_match_secondhand_products(input_gcs_path: str, output_gcs_path: str):
    logger.info("Collecting secondhand products from GPT based on the batch id")

    if check_cache_in_gcs(output_gcs_path):
        logger.info("Output already exists in the GCS bucket")
        return

    try:
        jsonl_res = load_jsonl_from_gcs(input_gcs_path)
        batch_ids = jsonl_res[0].get("batch_ids", [])

        gpt_outputs = []

        if IS_DEVELOPMENT:
            file_ids = jsonl_res[0].get("file_ids", [])
            for file_id in file_ids:
                gpt_outputs.extend(download_batch_results(file_id))
        else:
            for batch_id in batch_ids:
                try:
                    response = client.batches.retrieve(batch_id)
                    results = download_batch_results(response.output_file_id)
                    gpt_outputs.extend(results)
                except Exception as e:
                    logger.error(f"Error retrieving batch ID {batch_id}: {e}")

        logger.info(f"Collected {len(gpt_outputs)} GPT outputs")

        exact_matches = []
        firsthand_to_secondhand_mapping = {}

        for row in gpt_outputs:
            try:
                firsthand_id, secondhand_id = row["custom_id"].split("__")
                if IS_DEVELOPMENT:
                    raw_content = row["response"]["choices"][0]["message"]["content"]
                else:
                    raw_content = row["response"]["body"]["choices"][0]["message"][
                        "content"
                    ]

                if raw_content.startswith("```") and raw_content.endswith("```"):
                    raw_content = "\n".join(raw_content.split("\n")[1:-1])
                try:
                    gpt_response = json.loads(raw_content)
                except:
                    logger.warning(f"Error parsing JSON: {raw_content}")
                    gpt_response = {"is_exact_match": False}
                if firsthand_id not in firsthand_to_secondhand_mapping:
                    firsthand_to_secondhand_mapping[firsthand_id] = {
                        secondhand_id: gpt_response
                    }
                else:
                    firsthand_to_secondhand_mapping[firsthand_id][
                        secondhand_id
                    ] = gpt_response
            except Exception as e:
                logger.error(f"Error processing GPT output row: {e}")

        logger.info(f"Processed GPT outputs into mapping")

        file_name = output_gcs_path.split("/")[-1]
        firsthand_secondhand_mapping = load_jsonl_from_gcs(
            f"{SECONDHAND_PRODUCTS_OF_FIRSTHAND_BUCKET}/{file_name}"
        )

        logger.info(f"Loaded {len(firsthand_secondhand_mapping)} rows from GCS")

        exact_matches = []
        no_matches = 0
        for row in firsthand_secondhand_mapping:
            firsthand_product = row["firsthand_product"]
            secondhand_options = row["secondhand_options"]
            og_secondhand_product = row.get("secondhand_original")
            count = 0
            for secondhand_product in secondhand_options:
                firsthand_id = firsthand_product["id"]
                secondhand_id = secondhand_product["id"]
                gpt_response = firsthand_to_secondhand_mapping.get(
                    firsthand_id, {}
                ).get(secondhand_id, {})
                if not gpt_response:
                    # logger.info(
                    #     f"No GPT response for {firsthand_id} and {secondhand_id}"
                    # )
                    no_matches += 1
                    continue
                is_exact_match = gpt_response.get("is_exact_match", False)
                if is_exact_match:
                    count += 1
                    exact_matches.append(
                        {
                            "brand": row["brand"],
                            "category": row["category"],
                            "firsthand_product": firsthand_product,
                            "secondhand_product": secondhand_product,
                            "secondhand_original": og_secondhand_product,
                            "gpt_response": gpt_response,
                        }
                    )

            logger.info(
                f"Total matches: {count} for firsthand product ID: {firsthand_product['id']}"
            )

        logger.info(f"Storing {len(exact_matches)} exact matches to GCS")
        logger.info(f"{no_matches} no matches found")
        store_data_to_jsonl_in_gcs(output_gcs_path, exact_matches)
    except Exception as e:
        logger.error(f"Error in collect_exact_match_secondhand_products: {e}")
