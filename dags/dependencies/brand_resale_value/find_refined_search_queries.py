import io
import json
from openai import AsyncOpenAI
import openai
import concurrent.futures
import asyncio
from airflow.decorators import task_group, task
from dependencies.brand_resale_value.utils.constants import (
    OPENAI_API_KEY,
    IS_DEVELOPMENT,
)
from dependencies.brand_resale_value.utils.gcs import (
    check_cache_in_gcs,
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
)

client = AsyncOpenAI(api_key=OPENAI_API_KEY)
sync_client = openai.Client(api_key=OPENAI_API_KEY)


def create_refined_search_queries_dev(secondhand_product_data: list):
    batch_inputs = []
    for row in secondhand_product_data:
        for secondhand_product in row["secondhand_products"]:
            messages = build_refined_search_prompt(secondhand_product)
            batch_inputs.append(
                {
                    "custom_id": f"{secondhand_product['id']}",
                    "method": "POST",
                    "url": "/v1/chat/completions",
                    "body": {
                        "model": "gpt-4o-mini-2024-07-18",
                        "messages": messages,
                        "response_format": {"type": "json_object"},
                        "max_tokens": 1000,
                    },
                }
            )
    results = execute_gpt_prompts_in_dev(batch_inputs, max_workers=10)
    return results


def create_refined_search_queries_prod(secondhand_product_data: list):
    batch_inputs = []
    for row in secondhand_product_data:
        for secondhand_product in row["secondhand_products"]:
            messages = build_refined_search_prompt(secondhand_product)
            batch_inputs.append(
                {
                    "custom_id": f"{secondhand_product['id']}",
                    "method": "POST",
                    "url": "/v1/chat/completions",
                    "body": {
                        "model": "gpt-4o-mini-2024-07-18",
                        "messages": messages,
                        "response_format": {"type": "json_object"},
                        "max_tokens": 1000,
                    },
                }
            )
    results = upload_prompts_to_gpt(batch_inputs)
    return [
        {
            "batch_ids": results,
        }
    ]


def create_refined_search_queries(input_gcs_path: str, output_gcs_path: str):
    if check_cache_in_gcs(output_gcs_path):
        print("Output already exists in the GCS bucket")
        if IS_DEVELOPMENT:
            return []
        else:
            batch_ids = load_jsonl_from_gcs(output_gcs_path)[0]["batch_ids"]
            return batch_ids

    secondhand_product_data = load_jsonl_from_gcs(input_gcs_path)

    batch_inputs = []
    for row in secondhand_product_data:
        for secondhand_product in row["secondhand_products"]:
            messages = build_refined_search_prompt(secondhand_product)
            batch_inputs.append(
                {
                    "custom_id": f"{secondhand_product['id']}",
                    "method": "POST",
                    "url": "/v1/chat/completions",
                    "body": {
                        "model": "gpt-4o-mini-2024-07-18",
                        "messages": messages,
                        "response_format": {"type": "json_object"},
                        "max_tokens": 1000,
                    },
                }
            )
    if IS_DEVELOPMENT:
        file_id = execute_gpt_prompts_in_dev(batch_inputs)
        store_data_to_jsonl_in_gcs(
            output_gcs_path, [{"batch_ids": [], "file_ids": [file_id]}]
        )
        return []
    else:
        batch_ids = upload_prompts_to_gpt(batch_inputs)
        store_data_to_jsonl_in_gcs(output_gcs_path, [{"batch_ids": batch_ids}])
        return batch_ids


def upload_prompts_to_gpt(prompts: list[dict]):
    batch_size = 50000
    batches = [prompts[i : i + batch_size] for i in range(0, len(prompts), batch_size)]
    batch_ids = []
    for i, batch in enumerate(batches, start=1):
        print(f"Uploading batch {i} of {len(batches)} prompts to GPT...")
        jsonl_content = "\n".join([json.dumps(prompt) for prompt in batch])
        jsonl_file_data = io.BytesIO(jsonl_content.encode("utf-8"))

        uploaded_file = sync_client.files.create(file=jsonl_file_data, purpose="batch")

        print(f"Batch {i} uploaded successfully with file ID: {uploaded_file.id}")
        batch_res = sync_client.batches.create(
            input_file_id=uploaded_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h",
        )
        print(f"Batch {i} submitted successfully with batch ID: {batch_res.id}")
        batch_ids.append(batch_res.id)
    return batch_ids


def execute_gpt_prompts_in_dev(prompts: list[dict]):
    """
    Development: Execute each prompt directly in parallel using asyncio,
    gather results into a JSONL string, upload to OpenAI Files, and return the file ID.
    """

    async def call_openai_api(prompt: dict) -> dict:
        model = prompt["body"].get("model")
        messages = prompt["body"]["messages"]
        max_tokens = prompt["body"].get("max_tokens", 1000)

        try:
            response = await client.chat.completions.create(
                model=model, messages=messages, max_tokens=max_tokens
            )
            return {
                "custom_id": prompt["custom_id"],
                "method": prompt["method"],
                "url": prompt["url"],
                "body": prompt["body"],
                "response": response.to_dict(),
            }
        except Exception as e:
            return {
                "custom_id": prompt["custom_id"],
                "error": str(e),
            }

    async def gather_results():
        results = []
        batch_size = 500
        batches = [
            prompts[i : i + batch_size] for i in range(0, len(prompts), batch_size)
        ]

        for batch in batches:
            tasks = [call_openai_api(p) for p in batch]
            results.extend(await asyncio.gather(*tasks))
            print(f"Processed batch: {len(results) } / {len(prompts)}")

        return results

    results = asyncio.run(gather_results())
    print(results)

    # Prepare JSONL content that mimics a batch output
    jsonl_content = "\n".join(json.dumps(item) for item in results)
    jsonl_file_data = io.BytesIO(jsonl_content.encode("utf-8"))

    # Upload that file to OpenAI
    uploaded_file = sync_client.files.create(file=jsonl_file_data, purpose="batch")
    print(f"Uploaded direct-execution results file with ID: {uploaded_file.id}")

    return uploaded_file.id


def build_refined_search_prompt(secondhand_product):
    """
    Generate search query for the secondhand product
    """
    system_prompt = f"""
You are an AI that transforms secondhand product data into a refined search query for its first-hand (new/original) equivalent. 

**Your Output Requirements**:
1. Always return a JSON object with exactly one key: "query".
2. The value of "query" is a single string containing the refined search query.
{{
    "query": "transformed search query here"
}}

**Transformation Rules**:
- Parse the following text-based input, which may include:
  - title: <title>
  - description: <description> (optional)
  - link: <link> (optional)
  - brand: <brand> (optional)
- query_brand: <query_brand> (optional)
- Identify and remove/replace any secondhand indicators: 
  ("pre-owned", "pre owned", "used", "secondhand", "refurbished") 
  with descriptors like "new" or "original".
- Retain and/or extract key details from the title and description (brand, model, relevant specs).
- Exclude irrelevant or extraneous words (e.g., "minor scuffs", "excellent condition").
- Formulate a concise, natural-sounding query that reflects a brand-new item search.

Remember to output only the JSON object with the key "query" and no additional commentary or text.
    """

    user_prompt = f"""
Input: 
    title: {secondhand_product.get("name", "")}
    description: {secondhand_product.get("description", "")}
    link: {secondhand_product.get("link", "")}
    brand: {secondhand_product.get("brand", "")}
    query_brand: {secondhand_product.get("query_brand", "")}
    """
    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]
