from dependencies.resale_insights_stats.constants import (
    RAW_TABLE,
    CLEAN_TABLE,
    LATENCY_SUMMARY_TABLE,
)

def build_clean_resale_insights_table_query() -> str:
    return f"""
      INSERT INTO `{CLEAN_TABLE}`
      SELECT
        TIMESTAMP(cleaned.time) AS timestamp,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.scrapedColor')            AS scrapedColor,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.scrapedName')             AS scrapedName,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.scrapedPrice') AS FLOAT64)   AS scrapedPrice,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.scrapedBrand')            AS scrapedBrand,
        CASE JSON_EXTRACT_SCALAR(cleaned.payload, '$.dealType')
          WHEN '0' THEN 'BETTER_DEALS'
          WHEN '1' THEN 'GOOD_FIND'
          WHEN '2' THEN 'GREAT_DEAL'
          WHEN '3' THEN 'RARE_FIND'
          WHEN '4' THEN 'SIMILAR_DEALS'
          WHEN '5' THEN 'SUPERIOR_DEAL'
          ELSE 'UNSPECIFIED'
        END AS dealType,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.productUrl')              AS productUrl,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.platform')                AS platform,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.platformVersion')         AS platformVersion,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.userId')                  AS userId,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.resaleInsightsRequestId') AS resaleInsightsRequestId,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.runtimeTotal')  AS FLOAT64)   AS runtimeTotal,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.topProducts')              AS topProducts,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.expensiveBestMatchResults')AS expensiveBestMatchResults,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.similarStyleProducts')     AS similarStyleProducts,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.phiaId')                  AS phiaId,
        JSON_EXTRACT_ARRAY(cleaned.payload, '$.imageUrls')                AS imageUrls

      FROM (
        SELECT
          REGEXP_REPLACE(
            jsonPayload.message,
            r'^EVENT_TYPE_RESALE_INSIGHTS:',
            ''
          ) AS payload,
          jsonPayload.time AS time
        FROM `{RAW_TABLE}`
        WHERE
          jsonPayload.message LIKE 'EVENT_TYPE_RESALE_INSIGHTS:%'
          AND TIMESTAMP(jsonPayload.time)
              BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 15 MINUTE)
                  AND CURRENT_TIMESTAMP()
      ) AS cleaned
      """
    
def build_latency_summary_query() -> str:
    return f"""
      INSERT INTO `{LATENCY_SUMMARY_TABLE}`
      SELECT
        TIMESTAMP_TRUNC(timestamp, MINUTE) AS timestamp,
        APPROX_QUANTILES(runtimeTotal, 100)[OFFSET(89)] AS p90_latency
      FROM `{CLEAN_TABLE}`
      WHERE
        timestamp BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 15 MINUTE)
                      AND CURRENT_TIMESTAMP()
      GROUP BY timestamp
    """

# delete processed raw data older than 90 minutes (limit for streaming buffer for deletion)
def build_delete_raw_processed_query() -> str:
    return f"""
      DELETE FROM `{RAW_TABLE}`
        WHERE
          TIMESTAMP(jsonPayload.time)
            < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 MINUTE)
    """