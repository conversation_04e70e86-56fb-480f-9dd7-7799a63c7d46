def build_clean_resale_insights_table_schema() -> list:
    """
    Returns the schema for the clean resale insights table.
    """
    return [
        {"name": "timestamp",                 "type": "TIMESTAMP", "mode": "REQUIRED"},
        {"name": "scrapedColor",              "type": "STRING",    "mode": "NULLABLE"},
        {"name": "scrapedName",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "scrapedPrice",              "type": "FLOAT",     "mode": "NULLABLE"},
        {"name": "scrapedBrand",              "type": "STRING",    "mode": "NULLABLE"},
        {"name": "dealType",                  "type": "STRING",    "mode": "NULLABLE"},
        {"name": "productUrl",                "type": "STRING",    "mode": "NULLABLE"},
        {"name": "platform",                  "type": "STRING",    "mode": "NULL<PERSON><PERSON>"},
        {"name": "platformVersion",           "type": "STRING",    "mode": "NULLABLE"},
        {"name": "userId",                    "type": "STRING",    "mode": "NULLABLE"},
        {"name": "resaleInsightsRequestId",   "type": "STRING",    "mode": "NULLABLE"},
        {"name": "runtimeTotal",              "type": "FLOAT",     "mode": "NULLABLE"},
        {"name": "topProducts",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "expensiveBestMatchResults", "type": "STRING",    "mode": "NULLABLE"},
        {"name": "similarStyleProducts",      "type": "STRING",    "mode": "NULLABLE"},
        {"name": "phiaId",                    "type": "STRING",    "mode": "NULLABLE"},
        {"name": "imageUrls",                 "type": "STRING",    "mode": "REPEATED"},
    ]
    
def build_latency_summary_table_schema() -> list:
    """
    Returns the schema for the latency summary table.
    """
    return [
        {"name": "timestamp", "type": "TIMESTAMP", "mode": "REQUIRED"},
        {"name": "p90_latency", "type": "FLOAT", "mode": "REQUIRED"},
    ]