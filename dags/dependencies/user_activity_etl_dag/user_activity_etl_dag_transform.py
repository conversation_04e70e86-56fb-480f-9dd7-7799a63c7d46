import datetime
import logging
import sys

# Attempt to import protobuf libraries. These should be available due to sys.path modifications
# in the main DAG file (_ensure_schema_files_are_present).
try:
    from dependencies.generated_schema.python import log_data_stream_pb2
    # from dependencies.generated_schema.python import common_pb2 # If needed in the future
    from google.protobuf import text_format
    # Assuming PARQUET_SCHEMA and PROTOBUF_AVAILABLE will be passed from the DAG
except ImportError as e:
    logging.error(f"CRITICAL: transform_module.py could not import protobuf schemas or text_format: {e}")
    # This allows DAG to parse if imports fail here, but tasks will fail if they rely on these.
    # The main DAG already has robust checks for PROTOBUF_AVAILABLE.
    log_data_stream_pb2 = None 
    text_format = None

def _parse_proto_log_to_dict(log_content_str: str, is_protobuf_available: bool) -> dict | None:
    if not is_protobuf_available or not log_data_stream_pb2 or not text_format:
        logging.error("Error: Protobuf modules not available in _parse_proto_log_to_dict. Cannot parse log.")
        return None
    
    request_proto = log_data_stream_pb2.LogUserActivityRequest()
    try:
        text_format.Parse(log_content_str, request_proto)
        log_dict = {
            'timestamp': request_proto.timestamp.ToDatetime(tzinfo=datetime.timezone.utc) if request_proto.HasField('timestamp') else None,
            'os': request_proto.os,
            'device_type': request_proto.device_type,
            'phia_id': request_proto.phia_id,
            'ip': request_proto.ip if request_proto.HasField('ip') else None,
            'platform': request_proto.platform,
            'url': request_proto.url if request_proto.HasField('url') else None,
            'page_type': request_proto.page_type,
            'module': request_proto.module,
            'action': request_proto.action,
            'object_type': request_proto.object_type,
            'object_value': request_proto.object_value if request_proto.object_value else None,
            'action_id': request_proto.action_id
        }
        return log_dict
    except text_format.ParseError as e:
        logging.error(f"Error parsing protobuf text format: '{log_content_str[:100]}...'. Error: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error parsing proto log: '{log_content_str[:100]}...'. Error: {e}")
        return None

def _transform_raw_logs_for_parquet(raw_logs: list, parquet_schema_fields: list, is_protobuf_available: bool) -> list:
    transformed_batch = []
    if not raw_logs:
        logging.info("No raw logs to transform.")
        return transformed_batch

    for log_entry_str in raw_logs:
        if not log_entry_str: # Skip if the log string itself is empty/None
            continue
        parsed_log = _parse_proto_log_to_dict(log_entry_str, is_protobuf_available)
        if not parsed_log:
            logging.warning(f"Skipping log entry due to parsing error: {log_entry_str[:100]}...")
            continue

        record = []
        for field_name in parquet_schema_fields:
            value = parsed_log.get(field_name)
            # Basic type handling / default values for missing fields, consistent with original DAG
            if value is None:
                if field_name == 'timestamp': # Should not be None if parsed correctly
                    value = datetime.datetime.now(datetime.timezone.utc) # Fallback, though ideally log has it
                elif field_name in ['os', 'device_type', 'platform', 'page_type', 'module', 'action', 'object_type']:
                    value = 0 # Default for enum-like int fields
                # For string fields like phia_id, ip, url, object_value, action_id, None is acceptable or empty string
            record.append(value)
        transformed_batch.append(record)
    logging.info(f"Transformed {len(transformed_batch)} logs for Parquet writing.")
    return transformed_batch

def transform_logs_task(raw_logs: list, parquet_schema_fields: list, protobuf_available_flag: bool):
    """
    Airflow PythonOperator callable to transform raw logs into a Parquet-compatible structure.
    Requires PARQUET_SCHEMA (specifically, its field names) and PROTOBUF_AVAILABLE flag to be passed.
    """
    if not protobuf_available_flag:
        logging.error("PROTOBUF_AVAILABLE is false. Cannot proceed with log transformation.")
        # Depending on desired behavior, could return empty list or raise error
        # Raising an error might be more indicative of a prerequisite failure.
        raise ValueError("Protobuf modules are not available, cannot transform logs.")

    if not parquet_schema_fields:
        logging.error("PARQUET_SCHEMA fields not provided to transform_logs_task.")
        raise ValueError("Parquet schema fields are required for transformation.")

    logging.info(f"Starting transformation of {len(raw_logs)} logs.")
    transformed_data = _transform_raw_logs_for_parquet(
        raw_logs=raw_logs, 
        parquet_schema_fields=parquet_schema_fields,
        is_protobuf_available=protobuf_available_flag
    )
    logging.info(f"Successfully transformed {len(transformed_data)} logs.")
    return transformed_data
