import datetime
import logging
from google.cloud import logging_v2

def _fetch_logs_for_day_from_gcp(
    target_date: datetime.date,
    gcp_logging_client: logging_v2.Client,
    project_id: str,
    log_filter_prefix_value: str,
    configuration_name_filter: str = "jarvis-prod"  # Defaulted as per original DAG logic
):
    start_time = datetime.datetime.combine(target_date, datetime.time.min, tzinfo=datetime.timezone.utc)
    end_time = datetime.datetime.combine(target_date, datetime.time.max, tzinfo=datetime.timezone.utc)
    start_time_str = start_time.isoformat()
    end_time_str = end_time.isoformat()
    
    filter_parts = []
    if log_filter_prefix_value:
        filter_parts.append(f'SEARCH("`{log_filter_prefix_value}`")')
    if configuration_name_filter:
        filter_parts.append(f'resource.labels.configuration_name="{configuration_name_filter}"')
    
    filter_parts.append(f'timestamp >= "{start_time_str}"')
    filter_parts.append(f'timestamp <= "{end_time_str}"')
    
    filter_str = " AND ".join(filter_parts)
    
    logging.info(f"_fetch_logs_for_day_from_gcp: Using client: {gcp_logging_client}")
    if hasattr(gcp_logging_client, 'project'): # Check if client has project attribute
        logging.info(f"_fetch_logs_for_day_from_gcp: Client's configured project: {gcp_logging_client.project}")
    
    resource_names = [f"projects/{project_id}"]
    
    all_logs = []
    try:
        logging.info(f"Fetching logs with filter: {filter_str} for resource_names: {resource_names}")
        for entry in gcp_logging_client.list_entries(resource_names=resource_names, filter_=filter_str):
            payload_str = None
            # 1. Try to get text_payload (for TextEntry)
            if hasattr(entry, 'text_payload') and isinstance(entry.text_payload, str):
                payload_str = entry.text_payload
            # 2. Else, try json_payload (for JsonEntry, often a dict)
            elif hasattr(entry, 'json_payload'):
                if isinstance(entry.json_payload, dict) and 'message' in entry.json_payload and isinstance(entry.json_payload['message'], str):
                    payload_str = entry.json_payload['message']
                elif isinstance(entry.json_payload, str): # If json_payload itself is a string
                    payload_str = entry.json_payload
            # 3. Fallback to generic 'payload' (e.g. for StructEntry where 'payload' is the dict)
            elif hasattr(entry, 'payload'):
                if isinstance(entry.payload, str):
                    payload_str = entry.payload
                elif isinstance(entry.payload, dict) and 'message' in entry.payload and isinstance(entry.payload['message'], str):
                    payload_str = entry.payload['message']

            if payload_str is not None:
                # Strip the prefix if the payload_str starts with it
                # log_filter_prefix_value corresponds to the LOG_FILTER_PREFIX from the DAG
                if log_filter_prefix_value and payload_str.startswith(log_filter_prefix_value):
                    actual_log_data = payload_str[len(log_filter_prefix_value):].strip()
                    if actual_log_data: # Only append if there's content after stripping
                        all_logs.append(actual_log_data)
                    else:
                        logging.info(f"Log payload for {target_date.strftime('%Y-%m-%d')} became empty after stripping prefix '{log_filter_prefix_value}'. Original: {payload_str[:200]}...")
                else:
                    logging.warning(f"Log payload for {target_date.strftime('%Y-%m-%d')} does not start with expected prefix '{log_filter_prefix_value}'. Payload: {payload_str[:200]}...")
            else:
                logging.warning(f"Log entry for {target_date.strftime('%Y-%m-%d')} did not yield a usable string payload. Entry type: {type(entry)}, Raw Entry: {str(entry)[:200]}")

        logging.info(f"Fetched and prepared {len(all_logs)} log entries for {target_date.strftime('%Y-%m-%d')}")
    except Exception as e:
        logging.error(f"Error fetching logs for {target_date.strftime('%Y-%m-%d')}: {e}")
        raise
    return all_logs

def fetch_gcp_logs_task(
    target_date_str: str, 
    gcp_project_id: str, 
    gcp_log_filter_prefix: str
):
    """
    Airflow PythonOperator callable to fetch logs from GCP Cloud Logging.
    Initializes its own GCP Logging client.
    """
    try:
        # Initialize client here. Assumes standard authentication (e.g., GOOGLE_APPLICATION_CREDENTIALS)
        # or that the Airflow worker has appropriate permissions.
        client = logging_v2.Client()
        
        target_date = datetime.datetime.strptime(target_date_str, '%Y-%m-%d').date()
        logging.info(f"Fetching logs for date: {target_date_str} using project ID '{gcp_project_id}' and log prefix '{gcp_log_filter_prefix}'")

        raw_logs = _fetch_logs_for_day_from_gcp(
            target_date=target_date,
            gcp_logging_client=client,
            project_id=gcp_project_id, 
            log_filter_prefix_value=gcp_log_filter_prefix
            # configuration_name_filter can be added as a parameter to fetch_gcp_logs_task 
            # if it needs to be configured from the DAG's op_kwargs.
        )
        logging.info(f"Successfully fetched {len(raw_logs)} raw logs for {target_date_str}.")
        return raw_logs
    except Exception as e:
        logging.error(f"Error in fetch_gcp_logs_task for {target_date_str}: {e}")
        raise
