import datetime
import logging
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from pyarrow import fs

MAX_ROWS_PER_PARQUET_FILE = 100_000_000 # 100 million rows

def _write_to_parquet_gcs(
    data: list, 
    target_date: datetime.date, 
    gcs_bucket_name_param: str, 
    parquet_schema_object: pa.Schema
):
    if not data:
        logging.info("No data provided to _write_to_parquet_gcs.")
        # Still return the directory path as downstream tasks might expect it
        date_str = target_date.strftime('%Y-%m-%d')
        gcs_directory_path = f"gs://{gcs_bucket_name_param}/{date_str}/"
        return gcs_directory_path
    
    if not parquet_schema_object:
        logging.error("Parquet schema not provided to _write_to_parquet_gcs.")
        raise ValueError("Parquet schema is required to write Parquet file.")

    gcs_fs = fs.GcsFileSystem() # Assumes ADC or worker has GCS permissions
    date_str = target_date.strftime('%Y-%m-%d')
    gcs_base_object_path = f"{gcs_bucket_name_param}/{date_str}"
    gcs_directory_path = f"gs://{gcs_base_object_path}/"

    num_files_written = 0
    total_rows = len(data)

    for i, chunk_start in enumerate(range(0, total_rows, MAX_ROWS_PER_PARQUET_FILE)):
        chunk_end = min(chunk_start + MAX_ROWS_PER_PARQUET_FILE, total_rows)
        chunk_data = data[chunk_start:chunk_end]
        
        if not chunk_data:
            logging.info(f"Skipping empty chunk {i}.")
            continue

        df = pd.DataFrame(chunk_data, columns=parquet_schema_object.names)
        try:
            table = pa.Table.from_pandas(df, schema=parquet_schema_object, preserve_index=False)
        except Exception as e:
            logging.error(f"Error applying schema or converting DataFrame to Arrow Table for chunk {i}: {e}")
            raise # Re-raise to fail the task, as this is critical
        
        # Original single file name: user_activity.parquet
        # New partitioned file name: user_activity_part_000.parquet, user_activity_part_001.parquet, etc.
        gcs_file_name_part = f"user_activity_part_{i:03d}.parquet"
        gcs_object_name_for_pyarrow = f"{gcs_base_object_path}/{gcs_file_name_part}"
        gcs_full_file_path_for_logging = f"gs://{gcs_object_name_for_pyarrow}"

        try:
            pq.write_table(table, gcs_object_name_for_pyarrow, filesystem=gcs_fs)
            logging.info(f"Successfully wrote Parquet file part {i} to GCS: {gcs_full_file_path_for_logging} ({len(chunk_data)} rows)")
            num_files_written += 1
        except Exception as e:
            logging.error(f"Error writing Parquet file part {i} to GCS path {gcs_full_file_path_for_logging}: {e}")
            raise # Re-raise to fail the task

    if num_files_written > 0:
        logging.info(f"Successfully wrote {num_files_written} Parquet file parts to GCS directory: {gcs_directory_path}")
    else:
        logging.info(f"No Parquet file parts were written to GCS directory (total rows: {total_rows}): {gcs_directory_path}")
    
    return gcs_directory_path

def upload_parquet_to_gcs_task(
    transformed_logs: list, 
    target_date_str: str, 
    gcs_bucket_name_config: str, 
    parquet_schema_config: pa.Schema
):
    """
    Airflow PythonOperator callable to write transformed logs to Parquet files (partitioned by row count)
    and upload them to GCS.
    Requires GCS_BUCKET_NAME and PARQUET_SCHEMA to be passed.
    Returns the GCS directory path where parts are stored.
    """
    if not transformed_logs and transformed_logs != []: # Allow empty list to proceed to write 0 files
        logging.warning("Transformed logs is None. Skipping Parquet upload.")
        # To maintain consistency, we might still want to return a theoretical directory path
        # However, _write_to_parquet_gcs handles empty data list now.
        if not gcs_bucket_name_config or not target_date_str:
            raise ValueError("Cannot determine GCS directory path for None logs without bucket/date.")
        target_date_temp = datetime.datetime.strptime(target_date_str, '%Y-%m-%d').date()
        date_str_temp = target_date_temp.strftime('%Y-%m-%d')
        return f"gs://{gcs_bucket_name_config}/{date_str_temp}/"

    if not gcs_bucket_name_config:
        logging.error("GCS_BUCKET_NAME not configured for upload_parquet_to_gcs_task.")
        raise ValueError("GCS bucket name is required.")
    
    if not parquet_schema_config:
        logging.error("PARQUET_SCHEMA not configured for upload_parquet_to_gcs_task.")
        raise ValueError("Parquet schema is required.")

    target_date = datetime.datetime.strptime(target_date_str, '%Y-%m-%d').date()
    
    logging.info(f"Starting Parquet write/upload for {len(transformed_logs)} records for date {target_date_str}.")
    gcs_directory_path = _write_to_parquet_gcs(
        data=transformed_logs, 
        target_date=target_date, 
        gcs_bucket_name_param=gcs_bucket_name_config,
        parquet_schema_object=parquet_schema_config
    )
    
    if gcs_directory_path:
        logging.info(f"Successfully processed Parquet upload. Target GCS directory: {gcs_directory_path}.")
        return gcs_directory_path
    else:
        # This case should ideally not be hit if _write_to_parquet_gcs always returns a path or raises error
        logging.error("Failed to upload Parquet file(s). No GCS directory path returned.")
        raise RuntimeError("Parquet upload process failed to return a GCS directory path.")
