import logging
import pyarrow as pa
import pyarrow.parquet as pq
from pyarrow import fs

def verify_gcs_parquet_task(gcs_directory_path: str, parquet_schema_config: pa.Schema):
    """
    Airflow PythonOperator callable to read Parquet files from a GCS directory,
    verify their schema and content against PARQUET_SCHEMA.
    """
    if not gcs_directory_path:
        logging.error("No GCS directory path provided for verification.")
        raise ValueError("GCS directory path is required for verification.")

    if not parquet_schema_config:
        logging.error("PARQUET_SCHEMA not configured for verify_gcs_parquet_task.")
        raise ValueError("Parquet schema is required for verification.")

    logging.info(f"Starting verification for Parquet files in GCS directory: {gcs_directory_path}")

    try:
        gcs_fs = fs.GcsFileSystem() # Assumes ADC or worker has GCS permissions
        
        if not gcs_directory_path.startswith("gs://"):
            logging.error(f"Invalid GCS directory path format: {gcs_directory_path}. Expected 'gs://bucket/path/'.")
            raise ValueError(f"Invalid GCS directory path format: {gcs_directory_path}")
        
        # Ensure the path ends with a '/' for GcsFileSystem.get_file_info
        object_directory_path_for_pyarrow = gcs_directory_path[5:] # Remove "gs://"
        if not object_directory_path_for_pyarrow.endswith('/'):
            object_directory_path_for_pyarrow += '/'

        logging.info(f"Listing Parquet files in GCS directory: {object_directory_path_for_pyarrow}")
        file_selector = fs.FileSelector(object_directory_path_for_pyarrow, recursive=False) # No recursive, expect files directly in dir
        file_infos = gcs_fs.get_file_info(file_selector)
        
        parquet_files_to_verify = [fi.path for fi in file_infos if fi.type == fs.FileType.File and fi.path.endswith('.parquet')]

        if not parquet_files_to_verify:
            # This could be valid if the previous task produced no data (0 rows).
            # The upload_module now returns the directory even if no files are written.
            # Check if the DAG logic considers 0 rows an error or an acceptable state.
            # For now, we'll log it as info. If an error is expected, this logic might need adjustment.
            logging.info(f"No Parquet files found in directory {gcs_directory_path} for verification. This may be expected if no data was processed.")
            return True # Indicate success as there's nothing to fail verification on.

        logging.info(f"Found {len(parquet_files_to_verify)} Parquet files for verification in {gcs_directory_path}.")
        total_rows_verified = 0

        for i, file_object_path in enumerate(parquet_files_to_verify):
            full_gcs_file_path = f"gs://{file_object_path}"
            logging.info(f"Verifying file {i+1}/{len(parquet_files_to_verify)}: {full_gcs_file_path}")
            
            table = pq.read_table(file_object_path, filesystem=gcs_fs, schema=parquet_schema_config)
            
            logging.info(f"Successfully read Parquet file: {full_gcs_file_path}. Rows: {table.num_rows}, Columns: {table.num_columns}")
            
            if not table.schema.equals(parquet_schema_config):
                logging.error(f"Schema mismatch for file {full_gcs_file_path}! Expected: {parquet_schema_config}, Actual: {table.schema}")
                raise ValueError(f"Schema mismatch in {full_gcs_file_path}")
            else:
                logging.info(f"Schema matches for {full_gcs_file_path}.")

            if table.num_rows == 0:
                # While a part file could be empty if total rows < MAX_ROWS_PER_FILE, an entirely empty dataset 
                # might be an issue. This depends on requirements.
                logging.warning(f"Warning: Parquet file {full_gcs_file_path} is empty after reading.")
            
            total_rows_verified += table.num_rows

        logging.info(f"Verification successful for all {len(parquet_files_to_verify)} Parquet files in {gcs_directory_path}. Total rows verified: {total_rows_verified}.")
        return True # Indicates success

    except Exception as e:
        logging.error(f"Error during Parquet file verification process for directory {gcs_directory_path}: {e}")
        raise
