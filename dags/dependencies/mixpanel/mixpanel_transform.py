"""
Mixpanel data transformation functions.

This module handles:
- Converting raw Mixpanel data to normalized Supabase schemas
- Data validation and cleaning
- Schema mapping and type conversions for all required metrics
"""

import logging
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import json

logger = logging.getLogger(__name__)

# ========================================
# SUPABASE SCHEMA DEFINITIONS
# ========================================


def get_user_profiles_schema() -> Dict[str, str]:
    """
    Schema for user_profiles table in Supabase.

    Stores user data + params and chat data from Mixpanel /engage endpoint.
    Only includes fields that are actually populated by Mixpanel.
    """
    return {
        "user_id": "TEXT PRIMARY KEY",
        "distinct_id": "TEXT UNIQUE NOT NULL",
        "email": "TEXT",
        "created_at": "TIMESTAMPTZ",
        "onboarded_at": "TIMESTAMPTZ",
        "last_seen": "TIMESTAMPTZ",
        "is_private": "BOOLEAN",
        "chat_sessions_count": "INTEGER DEFAULT 0",
        "support_tickets_count": "INTEGER DEFAULT 0",
        "raw_properties": "JSONB",
        "updated_at": "TIMESTAMPTZ DEFAULT NOW()",
    }


def get_daily_metrics_schema() -> Dict[str, str]:
    """
    Schema for daily_metrics table in Supabase.

    Stores core extension metrics, DAU/MAU data aggregated by day.
    """
    return {
        "id": "SERIAL PRIMARY KEY",
        "date": "DATE NOT NULL",
        "event_name": "TEXT NOT NULL",
        "total_events": "BIGINT DEFAULT 0",
        "unique_users": "INTEGER DEFAULT 0",
        "metric_type": "TEXT",  # 'heartbeat', 'extension_page_view', 'page_view'
        "created_at": "TIMESTAMPTZ DEFAULT NOW()",
        "_unique_constraint": "UNIQUE(date, event_name)",
    }


def get_site_activation_schema() -> Dict[str, str]:
    """
    Schema for site_activation table in Supabase.

    Stores activation rates broken down by site/domain.
    """
    return {
        "id": "SERIAL PRIMARY KEY",
        "date": "DATE NOT NULL",
        "site_domain": "TEXT NOT NULL",
        "total_users": "INTEGER DEFAULT 0",
        "activated_users": "INTEGER DEFAULT 0",
        "activation_rate": "DECIMAL(5,2)",  # Percentage
        "created_at": "TIMESTAMPTZ DEFAULT NOW()",
        "_unique_constraint": "UNIQUE(date, site_domain)",
    }


def get_onboarding_funnel_schema() -> Dict[str, str]:
    """
    Schema for onboarding_funnel table in Supabase.

    Stores onboarding conversion metrics and funnel data.
    """
    return {
        "id": "SERIAL PRIMARY KEY",
        "date": "DATE NOT NULL",
        "funnel_step": "TEXT NOT NULL",  # 'extension_installed', 'first_launch', etc.
        "step_order": "INTEGER",
        "users_count": "INTEGER DEFAULT 0",
        "conversion_rate": "DECIMAL(5,2)",  # Percentage from previous step
        "created_at": "TIMESTAMPTZ DEFAULT NOW()",
        "_unique_constraint": "UNIQUE(date, funnel_step)",
    }


def get_cohort_retention_schema() -> Dict[str, str]:
    """
    Schema for cohort_retention table in Supabase.

    Stores weekly retention data filtered by cohort.
    """
    return {
        "id": "SERIAL PRIMARY KEY",
        "cohort_start_date": "DATE NOT NULL",
        "cohort_id": "INTEGER",
        "cohort_name": "TEXT",
        "cohort_size": "INTEGER",
        "week_number": "INTEGER",  # 1, 2, 3, etc.
        "active_users": "INTEGER DEFAULT 0",
        "retention_rate": "DECIMAL(5,2)",  # Percentage
        "week_start_date": "DATE",
        "week_end_date": "DATE",
        "created_at": "TIMESTAMPTZ DEFAULT NOW()",
        "_unique_constraint": "UNIQUE(cohort_start_date, cohort_id, week_number)",
    }


def get_safari_extension_metrics_schema() -> Dict[str, str]:
    """
    Schema for safari_extension_metrics table in Supabase.

    Stores Safari extension specific metrics filtered by platform.
    """
    return {
        "id": "SERIAL PRIMARY KEY",
        "date": "DATE NOT NULL",
        "metric_name": "TEXT NOT NULL",  # 'safari_users_since_launch', 'weekly_active_safari_users'
        "event_name": "TEXT NOT NULL",  # 'heartbeat', 'phia_clicked', etc.
        "total_events": "BIGINT DEFAULT 0",
        "unique_users": "INTEGER DEFAULT 0",
        "platform": "TEXT DEFAULT 'IOS_SAFARI_EXTENSION'",
        "created_at": "TIMESTAMPTZ DEFAULT NOW()",
        "_unique_constraint": "UNIQUE(date, metric_name, event_name)",
    }


def get_dashboard_components_schema() -> Dict[str, str]:
    """
    Schema for dashboard_components table in Supabase.

    Stores aggregated data for all 5 dashboard components.
    """
    return {
        "id": "SERIAL PRIMARY KEY",
        "date": "DATE NOT NULL",
        "component_name": "TEXT NOT NULL",  # 'week_9_retention', 'safari_users_since_launch', etc.
        "component_data": "JSONB NOT NULL",  # Flexible JSON storage
        "last_updated": "TIMESTAMPTZ DEFAULT NOW()",
        "created_at": "TIMESTAMPTZ DEFAULT NOW()",
        "_unique_constraint": "UNIQUE(date, component_name)",
    }


# ========================================
# DATA TRANSFORMATION FUNCTIONS
# ========================================


def transform_user_profiles(
    raw_user_data: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    """
    Transform raw Mixpanel user profiles to normalized Supabase schema.

    Args:
        raw_user_data: List of user profile dictionaries from Mixpanel /engage

    Returns:
        List of normalized user profile dictionaries for Supabase
    """
    logger.info(f"Transforming {len(raw_user_data)} user profiles")

    normalized_users = []

    for user in raw_user_data:
        try:
            distinct_id = user.get("$distinct_id")
            properties = user.get("$properties", {})

            # Extract and normalize user data
            normalized_user = {
                "user_id": properties.get("$user_id", distinct_id),
                "distinct_id": distinct_id,
                "email": properties.get("$email"),
                "first_name": properties.get("$first_name"),
                "last_name": properties.get("$last_name"),
                "username": properties.get("$username"),
                "created_at": _parse_datetime(properties.get("$created_at")),
                "onboarded_at": _parse_datetime(properties.get("$onboarded_at")),
                "last_seen": _parse_datetime(properties.get("$last_seen")),
                "city": properties.get("$city"),
                "country_code": properties.get("$country_code"),
                "region": properties.get("$region"),
                "timezone": properties.get("$timezone"),
                "browser": properties.get("$browser"),
                "os": properties.get("$os"),
                "device_model": properties.get("$device_model"),
                "platform": properties.get("platform"),
                "app_version": properties.get("app_version"),
                "initial_utm_source": properties.get("initial_utm_source"),
                "initial_utm_campaign": properties.get("initial_utm_campaign"),
                "avatar_url": properties.get("$avatar"),
                "phone": properties.get("$phone"),
                "is_private": properties.get("$is_private", False),
                "chat_sessions_count": properties.get("chat_sessions_count", 0),
                "support_tickets_count": properties.get("support_tickets_count", 0),
                "raw_properties": json.dumps(properties),
                "updated_at": datetime.now().isoformat(),
            }

            normalized_users.append(normalized_user)

        except Exception as e:
            logger.warning(f"Failed to transform user {distinct_id}: {e}")
            continue

    logger.info(f"Successfully transformed {len(normalized_users)} user profiles")
    return normalized_users


def transform_daily_metrics(
    total_events_data: Dict[str, Any],
    unique_users_data: Dict[str, Any],
    target_date: str,
) -> List[Dict[str, Any]]:
    """
    Transform raw Mixpanel events data to daily metrics for Supabase.

    Args:
        total_events_data: Response from Mixpanel /events endpoint (type=general)
        unique_users_data: Response from Mixpanel /events endpoint (type=unique)
        target_date: Date string in YYYY-MM-DD format

    Returns:
        List of daily metrics dictionaries for Supabase
    """
    logger.info(f"Transforming daily metrics for {target_date}")

    daily_metrics = []

    # Process total events
    if total_events_data and total_events_data.get("data", {}).get("values"):
        total_values = total_events_data["data"]["values"]

        for event_name, daily_data in total_values.items():
            total_events = daily_data.get(target_date, 0)

            # Get corresponding unique users
            unique_users = 0
            if unique_users_data and unique_users_data.get("data", {}).get("values"):
                unique_values = unique_users_data["data"]["values"]
                if event_name in unique_values:
                    unique_users = unique_values[event_name].get(target_date, 0)

            metric = {
                "date": target_date,
                "event_name": event_name,
                "total_events": total_events,
                "unique_users": unique_users,
                "metric_type": _get_metric_type(event_name),
                "created_at": datetime.now().isoformat(),
            }

            daily_metrics.append(metric)

    logger.info(f"Transformed {len(daily_metrics)} daily metrics")
    return daily_metrics


def transform_site_activation(
    segmentation_data: Dict[str, Any], target_date: str
) -> List[Dict[str, Any]]:
    """
    Transform Mixpanel segmentation data to site activation metrics.

    Args:
        segmentation_data: Response from Mixpanel /segmentation endpoint
        target_date: Date string in YYYY-MM-DD format

    Returns:
        List of site activation dictionaries for Supabase
    """
    logger.info(f"Transforming site activation data for {target_date}")

    site_metrics = []

    if segmentation_data and segmentation_data.get("data", {}).get("values"):
        values = segmentation_data["data"]["values"]

        for site_domain, daily_data in values.items():
            if isinstance(daily_data, dict):
                users_count = daily_data.get(target_date, 0)

                # For now, assume all users are "activated" - this can be refined
                # based on specific activation criteria
                activation_rate = 100.0 if users_count > 0 else 0.0

                metric = {
                    "date": target_date,
                    "site_domain": site_domain,
                    "total_users": users_count,
                    "activated_users": users_count,  # Simplified for now
                    "activation_rate": activation_rate,
                    "created_at": datetime.now().isoformat(),
                }

                site_metrics.append(metric)

    logger.info(f"Transformed {len(site_metrics)} site activation metrics")
    return site_metrics


def transform_browser_segmentation(
    segmentation_data: Dict[str, Any], target_date: str, segment_type: str = "browser"
) -> List[Dict[str, Any]]:
    """
    Transform Mixpanel segmentation data to browser/device segmentation.

    Args:
        segmentation_data: Response from Mixpanel /segmentation endpoint
        target_date: Date string in YYYY-MM-DD format
        segment_type: Type of segmentation ('browser', 'os', 'device')

    Returns:
        List of segmentation dictionaries for Supabase
    """
    logger.info(f"Transforming {segment_type} segmentation for {target_date}")

    segmentation_metrics = []

    if segmentation_data and segmentation_data.get("data", {}).get("values"):
        values = segmentation_data["data"]["values"]

        for segment_value, daily_data in values.items():
            if isinstance(daily_data, dict):
                unique_users = daily_data.get(target_date, 0)

                metric = {
                    "date": target_date,
                    "segment_type": segment_type,
                    "segment_value": segment_value,
                    "unique_users": unique_users,
                    "total_events": 0,  # Not available in segmentation data
                    "created_at": datetime.now().isoformat(),
                }

                segmentation_metrics.append(metric)

    logger.info(
        f"Transformed {len(segmentation_metrics)} {segment_type} segmentation metrics"
    )
    return segmentation_metrics


# ========================================
# UTILITY FUNCTIONS
# ========================================


def _parse_datetime(datetime_str: Optional[str]) -> Optional[str]:
    """
    Parse Mixpanel datetime string to ISO format.

    Args:
        datetime_str: Datetime string from Mixpanel

    Returns:
        ISO formatted datetime string or None
    """
    if not datetime_str:
        return None

    try:
        # Mixpanel typically uses ISO format already
        if isinstance(datetime_str, str):
            # Try parsing and reformatting to ensure consistency
            dt = datetime.fromisoformat(datetime_str.replace("Z", "+00:00"))
            return dt.isoformat()
        return None
    except (ValueError, AttributeError):
        logger.warning(f"Failed to parse datetime: {datetime_str}")
        return None


def _get_metric_type(event_name: str) -> str:
    """
    Categorize event names into metric types.

    Args:
        event_name: Name of the Mixpanel event

    Returns:
        Metric type category
    """
    if "heartbeat" in event_name.lower():
        return "heartbeat"
    elif "extension_page_view" in event_name.lower():
        return "extension_activity"
    elif "page_view" in event_name.lower():
        return "page_activity"
    else:
        return "other"


def transform_site_activation_rates(
    site_activation_data: Dict[str, Any], target_date: str
) -> List[Dict[str, Any]]:
    """
    Transform site activation rates data for Supabase storage.

    Args:
        site_activation_data: Raw site activation data from Mixpanel
        target_date: Date string (YYYY-MM-DD)

    Returns:
        List of records for site_activation table
    """
    logger.info("Transforming site activation rates data")

    transformed_data = []

    try:
        site_rates = site_activation_data.get("site_activation_rates", {})

        for hostname, metrics in site_rates.items():
            record = {
                "date": target_date,
                "hostname": hostname,
                "site_domain": hostname,  # Keep for backward compatibility
                "phia_shown_count": metrics.get("shown", 0),
                "phia_clicked_count": metrics.get("clicked", 0),
                "total_users": metrics.get("shown", 0),  # Total users who saw Phia
                "activated_users": metrics.get("clicked", 0),  # Users who clicked
                "activation_rate": metrics.get("activation_rate", 0.0),
                "platform": "IOS_SAFARI_EXTENSION",
            }
            transformed_data.append(record)

        logger.info(f"Transformed {len(transformed_data)} site activation records")

    except Exception as e:
        logger.error(f"Error transforming site activation data: {e}")

    return transformed_data


def transform_onboarding_funnel(
    funnel_data: Dict[str, Any], target_date: str
) -> List[Dict[str, Any]]:
    """
    Transform onboarding funnel data for Supabase storage.

    Args:
        funnel_data: Raw funnel data from Mixpanel
        target_date: Date string (YYYY-MM-DD)

    Returns:
        List of records for onboarding_funnel table
    """
    logger.info("Transforming onboarding funnel data")

    transformed_data = []

    try:
        funnel_analysis = funnel_data.get("funnel_analysis", {})

        # Overall funnel record
        overall_record = {
            "date": target_date,
            "funnel_step": "enabled_permissions_first_time",
            "step_order": 1,
            "step1_users": funnel_analysis.get("step1_total", 0),
            "step2_users": funnel_analysis.get("step2_total", 0),
            "users_count": funnel_analysis.get("step2_total", 0),  # Final step count
            "conversion_rate": funnel_analysis.get("overall_conversion_rate", 0.0),
            "overall_conversion_rate": funnel_analysis.get(
                "overall_conversion_rate", 0.0
            ),
        }
        transformed_data.append(overall_record)

        logger.info(f"Transformed {len(transformed_data)} onboarding funnel records")

    except Exception as e:
        logger.error(f"Error transforming onboarding funnel data: {e}")

    return transformed_data


def validate_transformed_data(
    data: List[Dict[str, Any]], schema_name: str
) -> Dict[str, Any]:
    """
    Validate transformed data quality and completeness.

    Args:
        data: List of transformed data dictionaries
        schema_name: Name of the schema being validated

    Returns:
        Validation results and statistics
    """
    logger.info(f"Validating {len(data)} records for {schema_name}")

    validation_results = {
        "total_records": len(data),
        "valid_records": 0,
        "invalid_records": 0,
        "errors": [],
        "warnings": [],
        "schema": schema_name,
    }

    for i, record in enumerate(data):
        try:
            # Basic validation - check for required fields
            if schema_name == "user_profiles":
                required_fields = ["user_id", "distinct_id"]
            elif schema_name == "daily_metrics":
                required_fields = ["date", "event_name"]
            elif schema_name == "site_activation":
                required_fields = ["date", "site_domain"]
            else:
                required_fields = ["date"]

            missing_fields = [
                field for field in required_fields if not record.get(field)
            ]

            if missing_fields:
                validation_results["invalid_records"] += 1
                validation_results["errors"].append(
                    f"Record {i}: Missing required fields: {missing_fields}"
                )
            else:
                validation_results["valid_records"] += 1

        except Exception as e:
            validation_results["invalid_records"] += 1
            validation_results["errors"].append(
                f"Record {i}: Validation error: {str(e)}"
            )

    # Calculate success rate
    if validation_results["total_records"] > 0:
        success_rate = (
            validation_results["valid_records"] / validation_results["total_records"]
        ) * 100
        validation_results["success_rate"] = round(success_rate, 2)
    else:
        validation_results["success_rate"] = 0.0

    logger.info(
        f"Validation complete: {validation_results['success_rate']}% success rate"
    )
    return validation_results
