"""
Airflow task functions for Mixpanel data pipeline.

This module contains the main task functions that will be called by the DAG.
Based on the working quick_mixpanel_test.py pattern.

Tasks:
- fetch_mixpanel_analytics_task: Fetch analytics data using working MixpanelService pattern
- transform_mixpanel_data_task: Transform data to Supabase schemas
- prepare_supabase_data_task: Prepare data for Supabase upload
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

from .mixpanel_service import MixpanelService

logger = logging.getLogger(__name__)


def fetch_mixpanel_analytics_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to fetch analytics data using the working MixpanelService pattern.

    Based on the working quick_mixpanel_test.py implementation.
    Fetches only the data that we know works reliably.

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with analytics data
    """
    logger.info(
        "Starting Mixpanel analytics fetch task (based on working test pattern)"
    )

    # Get execution date and calculate date ranges
    execution_date = kwargs.get("execution_date", datetime.now())
    target_date = execution_date - timedelta(days=1)
    date_str = target_date.strftime("%Y-%m-%d")

    # Use shorter date range like working test (last 3 days)
    from_date = (target_date - timedelta(days=3)).strftime("%Y-%m-%d")
    to_date = date_str

    logger.info(f"Fetching Mixpanel analytics from {from_date} to {to_date}")

    try:
        # Initialize MixpanelService exactly like working test
        credentials = {
            "project_id": os.getenv("MIXPANEL_PROJECT_ID"),
            "service_account_username": os.getenv("MIXPANEL_SERVICE_USERNAME"),
            "service_account_secret": os.getenv("MIXPANEL_SERVICE_SECRET"),
            "workspace_id": os.getenv("MIXPANEL_WORKSPACE_ID"),
        }

        logger.info(f"Project ID: {credentials['project_id']}")
        logger.info(f"Workspace ID: {credentials['workspace_id']}")

        # Initialize service WITHOUT rate limiting like working test
        service = MixpanelService(
            project_id=credentials["project_id"],
            service_account_username=credentials["service_account_username"],
            service_account_secret=credentials["service_account_secret"],
            workspace_id=credentials["workspace_id"],
            respect_rate_limits=False,  # Disable for reliability like working test
        )

        # Test connection like working test
        logger.info("Testing connection...")
        if service.test_connection():
            logger.info("✓ Connection successful!")
        else:
            logger.error("✗ Connection failed!")
            raise Exception("Connection test failed")

        # Test basic events like working test
        logger.info("Testing basic events...")
        events_data = service.client.get_events_data(
            events=["phia_clicked", "phia_shown", "heartbeat"],
            from_date=from_date,
            to_date=to_date,
            event_type="general",
            unit="day",
        )

        if not events_data or "data" not in events_data:
            raise Exception("Basic events test failed")

        logger.info("✓ Basic events test successful!")
        for event, daily_data in events_data["data"]["values"].items():
            total = sum(daily_data.values()) if daily_data else 0
            logger.info(f"  {event}: {total:,} total events")

        # Fetch all 5 key metrics from MixpanelService
        logger.info("Fetching all 5 key metrics...")

        # 1. Site activation metrics (already working)
        logger.info("1. Fetching site activation metrics...")
        site_metrics = service.get_site_activation_metrics(from_date, to_date, limit=10)
        logger.info(f"✓ Found {len(site_metrics)} sites with activation data")

        # 2. Permission funnel metrics
        logger.info("2. Fetching permission funnel metrics...")
        try:
            permission_funnel = service.get_permission_funnel_metrics(
                from_date, to_date
            )
            logger.info(
                f"✓ Permission funnel conversion: {permission_funnel.conversion_rate}%"
            )
        except Exception as e:
            logger.warning(f"Permission funnel metrics failed: {str(e)}")
            permission_funnel = None

        # 3. Safari extension metrics
        logger.info("3. Fetching Safari extension metrics...")
        try:
            safari_metrics = service.get_safari_extension_metrics(from_date, to_date)
            logger.info(f"✓ Safari extension users: {safari_metrics.active_users}")
        except Exception as e:
            logger.warning(f"Safari extension metrics failed: {str(e)}")
            safari_metrics = None

        # 4. Weekly active users metrics
        logger.info("4. Fetching weekly active users metrics...")
        try:
            weekly_users = service.get_weekly_active_users_metrics(from_date, to_date)
            logger.info(f"✓ Weekly active users: {weekly_users.total_active_users}")
        except Exception as e:
            logger.warning(f"Weekly active users metrics failed: {str(e)}")
            weekly_users = None

        # 5. Weekly retention metrics (use default cohort)
        logger.info("5. Fetching weekly retention metrics...")
        try:
            retention_metrics = service.get_weekly_retention_metrics(
                "2025-06-19", "2025-06-25", analysis_weeks=8
            )
            logger.info(
                f"✓ Retention cohort size: {retention_metrics.initial_cohort_size}"
            )
        except Exception as e:
            logger.warning(f"Weekly retention metrics failed: {str(e)}")
            retention_metrics = None

        # Create comprehensive analytics data structure
        analytics_data = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "date_range": {"from": from_date, "to": to_date},
            "data": {
                "basic_events": events_data,
                "site_activation_metrics": [
                    {
                        "site_domain": metric.site_domain,
                        "phia_shown_count": metric.phia_shown_count,
                        "phia_clicked_count": metric.phia_clicked_count,
                        "click_rate": metric.click_rate,
                        "combined_activation_rate": metric.combined_activation_rate,
                        "date_range": metric.date_range,
                    }
                    for metric in site_metrics
                ],
                "permission_funnel_metrics": (
                    {
                        "enable_phia_views": permission_funnel.enable_phia_views,
                        "almost_finished_views": permission_funnel.almost_finished_views,
                        "conversion_rate": permission_funnel.conversion_rate,
                        "date_range": permission_funnel.date_range,
                    }
                    if permission_funnel
                    else None
                ),
                "safari_extension_metrics": (
                    {
                        "active_users": safari_metrics.active_users,
                        "total_clicks": safari_metrics.total_clicks,
                        "date_range": safari_metrics.date_range,
                    }
                    if safari_metrics
                    else None
                ),
                "weekly_active_users_metrics": (
                    {
                        "total_active_users": weekly_users.total_active_users,
                        "heartbeat_events": weekly_users.heartbeat_events,
                        "os_breakdown": weekly_users.os_breakdown,
                        "date_range": weekly_users.date_range,
                    }
                    if weekly_users
                    else None
                ),
                "weekly_retention_metrics": (
                    {
                        "initial_cohort_size": retention_metrics.initial_cohort_size,
                        "cohort_start_date": retention_metrics.cohort_start_date,
                        "cohort_end_date": retention_metrics.cohort_end_date,
                        "weekly_retention_rates": retention_metrics.weekly_retention_rates,
                    }
                    if retention_metrics
                    else None
                ),
            },
        }

        logger.info("Successfully fetched analytics data using working pattern")
        return analytics_data

    except Exception as e:
        logger.error(f"Error in fetch task: {str(e)}")
        raise


def transform_mixpanel_data_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to transform analytics data to Supabase schemas.

    Based on the working data structure from fetch task.
    Transforms only the data we successfully fetched.

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with transformed data for Supabase tables
    """
    logger.info("Starting Mixpanel data transformation task")

    # Get analytics data from previous task
    ti = kwargs["ti"]
    analytics_data = ti.xcom_pull(task_ids="fetch_mixpanel_analytics")

    if not analytics_data or not analytics_data.get("success"):
        raise Exception("No valid analytics data received from fetch task")

    # Get target date
    execution_date = kwargs.get("execution_date", datetime.now())
    target_date = execution_date - timedelta(days=1)
    date_str = target_date.strftime("%Y-%m-%d")

    logger.info(f"Transforming analytics data for {date_str}")

    raw_data = analytics_data.get("data", {})
    transformed_data = {}

    try:
        # 1. Transform basic events to daily_metrics table
        if raw_data.get("basic_events"):
            logger.info("Transforming basic events to daily_metrics...")
            daily_metrics = []
            events_data = raw_data["basic_events"]

            if (
                events_data
                and "data" in events_data
                and "values" in events_data["data"]
            ):
                for event_name, daily_data in events_data["data"]["values"].items():
                    total_events = sum(daily_data.values()) if daily_data else 0

                    metric_record = {
                        "date": date_str,
                        "event_name": event_name,
                        "total_events": total_events,
                        "unique_users": 0,  # We don't have unique users data yet
                        "metric_type": "basic_events",
                        "created_at": datetime.now().isoformat(),
                    }
                    daily_metrics.append(metric_record)

            transformed_data["daily_metrics"] = daily_metrics
            logger.info(f"Transformed {len(daily_metrics)} daily metrics")

        # 2. Transform site activation metrics
        if raw_data.get("site_activation_metrics"):
            logger.info("Transforming site activation metrics...")
            site_metrics = []

            for metric in raw_data["site_activation_metrics"]:
                # Cap activation rate at 100% to prevent database precision errors
                activation_rate = min(metric["combined_activation_rate"], 100.0)

                site_record = {
                    "date": date_str,
                    "site_domain": metric["site_domain"],
                    "total_users": metric["phia_shown_count"],
                    "activated_users": metric["phia_clicked_count"],
                    "activation_rate": activation_rate,
                    "created_at": datetime.now().isoformat(),
                }
                site_metrics.append(site_record)

            transformed_data["site_activation"] = site_metrics
            logger.info(f"Transformed {len(site_metrics)} site activation metrics")

        # 3. Transform permission funnel metrics
        if raw_data.get("permission_funnel_metrics"):
            logger.info("Transforming permission funnel metrics...")
            funnel_data = raw_data["permission_funnel_metrics"]
            funnel_records = [
                {
                    "date": date_str,
                    "funnel_step": "enable_phia",
                    "step_order": 1,
                    "users_count": funnel_data["enable_phia_views"],
                    "conversion_rate": 100.0,  # First step is 100%
                    "created_at": datetime.now().isoformat(),
                },
                {
                    "date": date_str,
                    "funnel_step": "almost_finished",
                    "step_order": 2,
                    "users_count": funnel_data["almost_finished_views"],
                    "conversion_rate": funnel_data["conversion_rate"],
                    "created_at": datetime.now().isoformat(),
                },
            ]
            transformed_data["onboarding_funnel"] = funnel_records
            logger.info(f"Transformed {len(funnel_records)} funnel metrics")

        # 4. Transform Safari extension metrics
        if raw_data.get("safari_extension_metrics"):
            logger.info("Transforming Safari extension metrics...")
            safari_data = raw_data["safari_extension_metrics"]
            safari_records = [
                {
                    "date": date_str,
                    "metric_name": "weekly_active_safari_users",
                    "event_name": "phia_clicked",
                    "total_events": safari_data["total_clicks"],
                    "unique_users": safari_data["active_users"],
                    "platform": "IOS_SAFARI_EXTENSION",
                    "created_at": datetime.now().isoformat(),
                }
            ]
            transformed_data["safari_extension_metrics"] = safari_records
            logger.info(f"Transformed {len(safari_records)} Safari extension metrics")

        # 5. Transform weekly active users metrics
        if raw_data.get("weekly_active_users_metrics"):
            logger.info("Transforming weekly active users metrics...")
            weekly_data = raw_data["weekly_active_users_metrics"]
            # Add to daily_metrics (extend existing list)
            if "daily_metrics" not in transformed_data:
                transformed_data["daily_metrics"] = []

            weekly_record = {
                "date": date_str,
                "event_name": "heartbeat_weekly",
                "total_events": weekly_data["heartbeat_events"],
                "unique_users": weekly_data["total_active_users"],
                "metric_type": "weekly_active_users",
                "created_at": datetime.now().isoformat(),
            }
            transformed_data["daily_metrics"].append(weekly_record)
            logger.info("Added weekly active users to daily_metrics")

        # 6. Transform weekly retention metrics
        if raw_data.get("weekly_retention_metrics"):
            logger.info("Transforming weekly retention metrics...")
            retention_data = raw_data["weekly_retention_metrics"]
            cohort_records = []

            for week_key, week_data in retention_data["weekly_retention_rates"].items():
                week_number = (
                    int(week_key.split("_")[1]) + 1
                )  # week_0 -> 1, week_1 -> 2, etc.
                cohort_record = {
                    "cohort_start_date": retention_data["cohort_start_date"],
                    "cohort_id": 1,  # Default cohort ID
                    "cohort_name": f"Cohort {retention_data['cohort_start_date']}",
                    "cohort_size": retention_data["initial_cohort_size"],
                    "week_number": week_number,
                    "active_users": week_data["heartbeat_users"],
                    "retention_rate": week_data["heartbeat_retention_rate"],
                    "week_start_date": week_data["week_start"],
                    "week_end_date": week_data["week_end"],
                    "created_at": datetime.now().isoformat(),
                }
                cohort_records.append(cohort_record)

            transformed_data["cohort_retention"] = cohort_records
            logger.info(f"Transformed {len(cohort_records)} retention cohort records")

        # Calculate overall statistics
        total_records = sum(len(data) for data in transformed_data.values())

        results = {
            "success": True,
            "target_date": date_str,
            "transformed_data": transformed_data,
            "statistics": {
                "total_records": total_records,
                "tables_created": len(transformed_data),
                "processing_time": datetime.now().isoformat(),
            },
        }

        logger.info(
            f"Transformation complete: {total_records} records across {len(transformed_data)} tables"
        )
        return results

    except Exception as e:
        logger.error(f"Error in transform task: {str(e)}")
        raise


def prepare_supabase_data_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to prepare transformed data for Supabase upload.

    Simple preparation based on working data structure.

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with data prepared for Supabase upload
    """
    logger.info("Starting Supabase data preparation task")

    # Get transformed data from previous task
    ti = kwargs["ti"]
    transform_results = ti.xcom_pull(task_ids="transform_mixpanel_data")

    if not transform_results or not transform_results.get("success"):
        raise Exception("No valid transformed data received from transform task")

    transformed_data = transform_results.get("transformed_data", {})
    target_date = transform_results.get("target_date")

    logger.info(f"Preparing {len(transformed_data)} tables for Supabase upload")

    # Prepare data for each table (simple cleaning)
    supabase_ready_data = {}

    for table_name, records in transformed_data.items():
        if records:
            # Simple cleaning - remove None values
            valid_records = []
            for record in records:
                cleaned_record = {k: v for k, v in record.items() if v is not None}
                if cleaned_record:
                    valid_records.append(cleaned_record)

            supabase_ready_data[table_name] = valid_records
            logger.info(f"{table_name}: {len(valid_records)} records ready for upload")

    results = {
        "success": True,
        "target_date": target_date,
        "supabase_data": supabase_ready_data,
        "total_tables": len(supabase_ready_data),
        "total_records": sum(len(data) for data in supabase_ready_data.values()),
        "prepared_at": datetime.now().isoformat(),
    }

    logger.info(
        f"Supabase preparation complete: {results['total_records']} records across {results['total_tables']} tables"
    )
    return results
