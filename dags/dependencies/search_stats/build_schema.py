def build_search_clean_table_schema() -> list:
    """
    Schema for the clean search table, with all JSON blobs stored as STRINGs.
    """
    return [
        {"name": "timestamp",                        "type": "TIMESTAMP", "mode": "REQUIRED"},
        {"name": "search_results",                   "type": "STRING",    "mode": "NULLABLE"},

        # query:
        {"name": "query_imageUrls",                  "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_productUrl",                 "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_scrapedPrice",               "type": "FLOAT",     "mode": "NULLABLE"},
        {"name": "query_scrapedBrand",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_scrapedName",                "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_scrapedColor",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_scrapedDescription",         "type": "STRING",    "mode": "NULL<PERSON>LE"},
        {"name": "query_currencyCode",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_origin",                     "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_searchId",                   "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_merchantUrlId",              "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_platform",                   "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_phiaId",                     "type": "STRING",    "mode": "NULLABLE"},
        {"name": "query_platformVersion",            "type": "STRING",    "mode": "NULLABLE"},

        # options:
        {"name": "options_limit",                    "type": "INT64",     "mode": "NULLABLE"},
        {"name": "options_offset",                   "type": "INT64",     "mode": "NULLABLE"},
        {"name": "options_sortType",                 "type": "STRING",    "mode": "NULLABLE"},
        {"name": "options_searchType",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "options_usedOnly",                 "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "options_newOnly",                  "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "options_cacheOnly",                "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "options_currencyType",             "type": "STRING",    "mode": "NULLABLE"},

        # filters:
        {"name": "filters_priceRangeFilter",         "type": "STRING",    "mode": "NULLABLE"},
        {"name": "filters_conditions",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "filters_genders",                  "type": "STRING",    "mode": "NULLABLE"},
        {"name": "filters_isAuthenticated",          "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "filters_isFreeShipping",           "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "filters_isReturnable",             "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "filters_isVintage",                "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "filters_sizeLabels",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "filters_secondhandRetailerIds",    "type": "STRING",    "mode": "NULLABLE"},
        {"name": "filters_isBestMatch",              "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "filters_searchFilters",            "type": "STRING",    "mode": "NULLABLE"},

        # extractedProductMetadata:
        {"name": "extracted_brand",                  "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_color",                  "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_gender",                 "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_category",               "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_sellerName",             "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_isBrandWebsite",         "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "extracted_rawOutput",              "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_prompt",                 "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_cleanedProductName",     "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_style",                  "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_suggestedKeywords",      "type": "STRING",    "mode": "NULLABLE"},
        {"name": "extracted_level1Category",         "type": "STRING",    "mode": "NULLABLE"},

        # latencies and flags:
        {"name": "latency_search",                   "type": "FLOAT",     "mode": "NULLABLE"},
        {"name": "latency_request_understanding",    "type": "FLOAT",     "mode": "NULLABLE"},
        {"name": "latency_post_process",             "type": "FLOAT",     "mode": "NULLABLE"},
        {"name": "latency_reranking",                "type": "FLOAT",     "mode": "NULLABLE"},
        {"name": "latency_total",                    "type": "FLOAT",     "mode": "NULLABLE"},
        {"name": "isCached",                         "type": "BOOLEAN",   "mode": "NULLABLE"},
        {"name": "num_products",                     "type": "INT64",     "mode": "NULLABLE"},
        {"name": "isRankingEnabled",                 "type": "BOOLEAN",   "mode": "NULLABLE"},
    ]

def build_search_latency_table_schema() -> list:
    """
    Schema for the latency-summary table.
    """
    return [
        {"name": "timestamp",                     "type": "TIMESTAMP", "mode": "REQUIRED"},
        {"name": "p90_latency_search",            "type": "FLOAT",     "mode": "REQUIRED"},
        {"name": "p90_latency_request_understand","type": "FLOAT",     "mode": "REQUIRED"},
        {"name": "p90_latency_post_process",      "type": "FLOAT",     "mode": "REQUIRED"},
        {"name": "p90_latency_reranking",         "type": "FLOAT",     "mode": "REQUIRED"},
        {"name": "p90_latency_total",             "type": "FLOAT",     "mode": "REQUIRED"},
    ]
