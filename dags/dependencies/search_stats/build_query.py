from dependencies.search_stats.constants import (
    RAW_TABLE,
    CLEAN_TABLE,
    LATENCY_TABLE
)

def build_clean_search_table_query() -> str:
    return f"""
      INSERT INTO `{CLEAN_TABLE}` (
        timestamp,
        search_results,

        -- query
        query_imageUrls,
        query_productUrl,
        query_scrapedPrice,
        query_scrapedBrand,
        query_scrapedName,
        query_scrapedColor,
        query_scrapedDescription,
        query_currencyCode,
        query_origin,
        query_searchId,
        query_merchantUrlId,
        query_platform,
        query_phiaId,
        query_platformVersion,

        -- options
        options_limit,
        options_offset,
        options_sortType,
        options_searchType,
        options_usedOnly,
        options_newOnly,
        options_cacheOnly,
        options_currencyType,

        -- filters
        filters_priceRangeFilter,
        filters_conditions,
        filters_genders,
        filters_isAuthenticated,
        filters_isFreeShipping,
        filters_isReturnable,
        filters_isVintage,
        filters_sizeLabels,
        filters_secondhandRetailerIds,
        filters_isBestMatch,
        filters_searchFilters,

        -- extractedProductMetadata
        extracted_brand,
        extracted_color,
        extracted_gender,
        extracted_category,
        extracted_sellerName,
        extracted_isBrandWebsite,
        extracted_rawOutput,
        extracted_prompt,
        extracted_cleanedProductName,
        extracted_style,
        extracted_suggestedKeywords,
        extracted_level1Category,

        -- latencies and flags
        latency_search,
        latency_request_understanding,
        latency_post_process,
        latency_reranking,
        latency_total,
        isCached,
        num_products,
        isRankingEnabled
      )
      SELECT
        TIMESTAMP(cleaned.raw_ts) AS timestamp,
        JSON_EXTRACT(cleaned.payload, '$.search_results')                                    AS search_results,

        -- query fields
        JSON_EXTRACT(cleaned.payload, '$.query.image.urls')                                  AS query_imageUrls,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.productUrl')                           AS query_productUrl,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.scrapedPrice') AS FLOAT64)    AS query_scrapedPrice,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.scrapedBrand')                         AS query_scrapedBrand,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.scrapedName')                          AS query_scrapedName,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.scrapedColor')                         AS query_scrapedColor,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.scrapedDescription')                   AS query_scrapedDescription,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.currencyCode')                         AS query_currencyCode,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.origin')                               AS query_origin,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.searchId')                             AS query_searchId,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.merchantUrlId')                        AS query_merchantUrlId,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.platform')                             AS query_platform,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.phiaId')                               AS query_phiaId,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.query.platformVersion')                      AS query_platformVersion,

        -- options (escape reserved words)
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.options.limit') AS INT64)            AS options_limit,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.options.offset') AS INT64)           AS options_offset,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.options.sortType')                             AS options_sortType,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.options.searchType')                           AS options_searchType,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.options.usedOnly') AS BOOLEAN)       AS options_usedOnly,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.options.newOnly') AS BOOLEAN)        AS options_newOnly,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.options.cacheOnly') AS BOOLEAN)      AS options_cacheOnly,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.options.currencyType')                         AS options_currencyType,

        -- filters
        JSON_EXTRACT(cleaned.payload, '$.filters.priceRangeFilter')                             AS filters_priceRangeFilter,
        JSON_EXTRACT(cleaned.payload, '$.filters.conditions')                                   AS filters_conditions,
        JSON_EXTRACT(cleaned.payload, '$.filters.genders')                                      AS filters_genders,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.filters.isAuthenticated') AS BOOLEAN)  AS filters_isAuthenticated,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.filters.isFreeShipping') AS BOOLEAN)    AS filters_isFreeShipping,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.filters.isReturnable') AS BOOLEAN)      AS filters_isReturnable,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.filters.isVintage') AS BOOLEAN)         AS filters_isVintage,
        JSON_EXTRACT(cleaned.payload, '$.filters.sizeLabels')                                    AS filters_sizeLabels,
        JSON_EXTRACT(cleaned.payload, '$.filters.secondhandRetailerIds')                         AS filters_secondhandRetailerIds,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.filters.isBestMatch') AS BOOLEAN)       AS filters_isBestMatch,
        JSON_EXTRACT(cleaned.payload, '$.filters.searchFilters')                                 AS filters_searchFilters,

        -- extractedProductMetadata
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.brand')                 AS extracted_brand,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.color')                 AS extracted_color,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.gender')                AS extracted_gender,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.category')              AS extracted_category,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.sellerName')            AS extracted_sellerName,
        SAFE_CAST(
          JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.isBrandWebsite') AS BOOLEAN
        )                                                                                         AS extracted_isBrandWebsite,
        JSON_EXTRACT(cleaned.payload, '$.extractedProductMetadata.rawOutput')                    AS extracted_rawOutput,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.prompt')                 AS extracted_prompt,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.cleanedProductName')     AS extracted_cleanedProductName,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.style')                  AS extracted_style,
        JSON_EXTRACT(cleaned.payload, '$.extractedProductMetadata.suggestedKeywords')             AS extracted_suggestedKeywords,
        JSON_EXTRACT_SCALAR(cleaned.payload, '$.extractedProductMetadata.level1Category')         AS extracted_level1Category,

        -- latencies and flags
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.latency_search') AS FLOAT64)               AS latency_search,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.latency_request_understanding') AS FLOAT64) AS latency_request_understanding,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.latency_post_process') AS FLOAT64)         AS latency_post_process,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.latency_reranking') AS FLOAT64)            AS latency_reranking,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.latency_total') AS FLOAT64)                AS latency_total,

        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.isCached') AS BOOLEAN)             AS isCached,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.num_products') AS INT64)           AS num_products,
        SAFE_CAST(JSON_EXTRACT_SCALAR(cleaned.payload, '$.isRankingEnabled') AS BOOLEAN)     AS isRankingEnabled

      FROM (
        SELECT
          REGEXP_REPLACE(jsonPayload.message, r'^EVENT_TYPE_SEARCH:', '') AS payload,
          jsonPayload.timestamp AS raw_ts
        FROM `{RAW_TABLE}`
        WHERE
          jsonPayload.message LIKE 'EVENT_TYPE_SEARCH:%'
          AND TIMESTAMP(jsonPayload.timestamp)
              BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 15 MINUTE)
                  AND CURRENT_TIMESTAMP()
      ) AS cleaned;
    """


def build_latency_summary_query() -> str:
    return f"""
      INSERT INTO `{LATENCY_TABLE}`
      SELECT
        TIMESTAMP_TRUNC(timestamp, MINUTE)                                      AS timestamp,

        -- Compute P90 for each latency dimension via APPROX_QUANTILES
        APPROX_QUANTILES(latency_search,               100)[OFFSET(89)]         AS p90_latency_search,
        APPROX_QUANTILES(latency_request_understanding,100)[OFFSET(89)]         AS p90_latency_request_understand,
        APPROX_QUANTILES(latency_post_process,         100)[OFFSET(89)]         AS p90_latency_post_process,
        APPROX_QUANTILES(latency_reranking,            100)[OFFSET(89)]         AS p90_latency_reranking,
        APPROX_QUANTILES(latency_total,                100)[OFFSET(89)]         AS p90_latency_total

      FROM `{CLEAN_TABLE}`
      WHERE
        timestamp BETWEEN
          TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 15 MINUTE)
          AND CURRENT_TIMESTAMP()
      GROUP BY timestamp
      ORDER BY timestamp;
    """

# delete processed raw data older than 90 minutes (limit for streaming buffer for deletion)
def build_delete_raw_processed_query() -> str:
    return f"""
      DELETE FROM `{RAW_TABLE}`
      WHERE
        TIMESTAMP(jsonPayload.timestamp)
          < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 MINUTE);
    """
