"""
Simple data models for Strackr transactions using Pydantic.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, validator
import logging

logger = logging.getLogger(__name__)


class StrackrTransaction(BaseModel):
    """Model for Strackr transaction data matching normalized_transactions table schema."""

    # Core identifiers (required)
    transaction_id: str = Field(
        ..., description="Unique transaction ID (strackr_originalid)"
    )
    platform: str = Field(default="strackr", description="Platform identifier")
    source_transaction_id: str = Field(
        ..., description="Original Strackr transaction ID"
    )
    order_id: str = Field(default="", description="Order ID from merchant")

    # Dates (required)
    transaction_date: datetime = Field(..., description="When the transaction occurred")
    created_date: datetime = Field(..., description="When record was created")
    last_updated: datetime = Field(..., description="When record was last updated")

    # Financial data (required)
    currency: str = Field(default="USD", description="Currency code")
    order_amount: float = Field(..., description="Order amount")
    commission_amount: float = Field(..., description="Commission amount")

    # Merchant/Network info (required)
    network_name: str = Field(..., description="Affiliate network name")
    merchant_name: str = Field(..., description="Merchant name")

    # Status (required)
    status: str = Field(..., description="Transaction status")

    # Optional fields matching the exact schema
    final_order_amount: Optional[float] = None
    final_commission_amount: Optional[float] = None
    customer_id: Optional[str] = None
    merchant_id: Optional[str] = None
    connection_name: Optional[str] = None
    transaction_type: Optional[str] = None
    decline_reason: Optional[str] = None
    channel_name: Optional[str] = None  # Added to match schema
    custom_fields: Optional[Dict[str, Any]] = None
    comments: Optional[str] = None

    # Additional fields from schema (will be auto-populated by DB)
    # id: UUID (auto-generated by DB)
    # created_at: timestamp (auto-populated by DB)
    # updated_at: timestamp (auto-populated by DB)
    # upload_batch_id: Optional[UUID] = None
    # source_file: Optional[str] = None
    # csv_row_number: Optional[int] = None
    # transaction_fingerprint: Optional[str] = None

    @validator("order_amount", "commission_amount")
    def validate_amounts(cls, v):
        """Ensure amounts are non-negative."""
        if v < 0:
            raise ValueError("Amounts must be non-negative")
        return v

    @validator("currency")
    def validate_currency(cls, v):
        """Ensure currency is uppercase."""
        return v.upper()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database insertion."""
        data = self.dict()

        # Convert datetime objects to ISO strings for JSON serialization
        for field in ["transaction_date", "created_date", "last_updated"]:
            if data[field]:
                data[field] = data[field].isoformat()

        return data


def transform_strackr_data(
    raw_transaction: Dict[str, Any],
) -> Optional[StrackrTransaction]:
    """
    Transform raw Strackr API data to our model.

    Args:
        raw_transaction: Raw transaction data from Strackr API

    Returns:
        StrackrTransaction instance or None if transformation fails
    """
    try:
        # Extract core data
        source_id = str(raw_transaction["id"])
        transaction_id = f"strackr_{source_id}"

        # Parse dates
        transaction_date = _parse_datetime(raw_transaction["sold_at"])
        created_date = (
            _parse_datetime(raw_transaction.get("clicked_at")) or transaction_date
        )
        last_updated = (
            _parse_datetime(raw_transaction.get("status_updated_at")) or created_date
        )

        # Extract financial data
        order_amount = float(raw_transaction.get("order_amount", 0))
        commission_amount = float(raw_transaction.get("commission_amount", 0))

        # Create the transaction
        transaction = StrackrTransaction(
            transaction_id=transaction_id,
            source_transaction_id=source_id,
            order_id=raw_transaction.get("order_id") or "",
            transaction_date=transaction_date,
            created_date=created_date,
            last_updated=last_updated,
            currency=raw_transaction.get("currency", "USD"),
            order_amount=order_amount,
            commission_amount=commission_amount,
            network_name=raw_transaction.get("network_name", ""),
            merchant_name=raw_transaction.get("advertiser_name", "Unknown"),
            status=_normalize_status(raw_transaction.get("status_id", "")),
            final_order_amount=raw_transaction.get("final_order_amount"),
            final_commission_amount=raw_transaction.get("final_commission_amount"),
            customer_id=raw_transaction.get("customer_id"),
            merchant_id=raw_transaction.get("advertiser_id"),
            connection_name=raw_transaction.get("connection_name"),
            transaction_type=raw_transaction.get("type_id", "transaction"),
            decline_reason=_extract_decline_reason(raw_transaction),
            channel_name=raw_transaction.get("channel_name"),  # Added channel_name
            custom_fields=raw_transaction.get("custom_fields"),
            comments=raw_transaction.get("comments"),
        )

        return transaction

    except Exception as e:
        logger.error(
            f"Failed to transform transaction {raw_transaction.get('id', 'unknown')}: {e}"
        )
        return None


def _parse_datetime(date_str: Optional[str]) -> Optional[datetime]:
    """Parse datetime string from Strackr API."""
    if not date_str:
        return None

    try:
        # Handle different datetime formats from Strackr
        if "T" in date_str:
            if date_str.endswith("Z"):
                return datetime.fromisoformat(date_str.replace("Z", "+00:00"))
            else:
                return datetime.fromisoformat(date_str)
        else:
            return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
    except Exception as e:
        logger.warning(f"Failed to parse datetime '{date_str}': {e}")
        return None


def _normalize_status(status_id: Any) -> str:
    """Normalize Strackr status to standard values."""
    if not status_id:
        return "unknown"

    status_map = {
        1: "pending",
        2: "confirmed",
        3: "declined",
        "pending": "pending",
        "confirmed": "confirmed",
        "declined": "declined",
    }

    return status_map.get(status_id, str(status_id).lower())


def _extract_decline_reason(raw_transaction: Dict[str, Any]) -> Optional[str]:
    """Extract decline reason from raw transaction data."""
    reason = raw_transaction.get("reason")
    if reason and isinstance(reason, dict):
        return reason.get("name")
    return None
