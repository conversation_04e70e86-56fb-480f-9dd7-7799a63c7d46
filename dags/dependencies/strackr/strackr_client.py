"""
Simple Strackr API client for transaction data.
Handles authentication, API requests, and basic error handling.
"""

import os
import requests
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from google.cloud import secretmanager

logger = logging.getLogger(__name__)


class StrackrClient:
    """Simple client for Strackr transactions API."""
    
    def __init__(self):
        """Initialize the client with credentials from environment or Secret Manager."""
        self.base_url = "https://api.strackr.com/v4"
        self.session = requests.Session()
        self.session.timeout = 30
        
        # Get credentials
        self.api_id = self._get_credential("STRACKR_API_ID", "strackr-api-id")
        self.api_key = self._get_credential("STRACKR_API_KEY", "strackr-api-key")
        
        if not self.api_id or not self.api_key:
            raise ValueError("Strackr API credentials not found")
            
        logger.info("Strackr client initialized successfully")
    
    def _get_credential(self, env_var: str, secret_name: str) -> str:
        """Get credential from environment variable or Secret Manager."""
        # Try environment variable first
        value = os.getenv(env_var)
        if value:
            return value
            
        # Try Secret Manager
        try:
            client = secretmanager.SecretManagerServiceClient()
            project_id = "phia-prod-416420"
            secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
            response = client.access_secret_version(request={"name": secret_path})
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to get credential {secret_name}: {e}")
            return ""
    
    def get_transactions(
        self, 
        start_date: str, 
        end_date: str, 
        currency: str = "USD"
    ) -> List[Dict[str, Any]]:
        """
        Fetch transactions from Strackr API.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format  
            currency: Currency filter (default: USD)
            
        Returns:
            List of transaction dictionaries
        """
        logger.info(f"Fetching Strackr transactions from {start_date} to {end_date}")
        
        url = f"{self.base_url}/ad/reports/transactions"
        params = {
            "api_id": self.api_id,
            "api_key": self.api_key,
            "time_start": start_date,
            "time_end": end_date,
            "time_type": "sold_at",
            "currency": currency,
            "limit": 100,
            "page": 1,
            "expand": ["reason", "network_favicon", "advertiser_favicon", "basket", "event"]
        }
        
        all_transactions = []
        page = 1
        
        while True:
            params["page"] = page
            
            try:
                response = self.session.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                transactions = data.get("data", [])
                if not transactions:
                    break
                    
                all_transactions.extend(transactions)
                logger.info(f"Fetched page {page}: {len(transactions)} transactions")
                
                # Check if there are more pages
                if len(transactions) < params["limit"]:
                    break
                    
                page += 1
                
            except requests.exceptions.RequestException as e:
                logger.error(f"API request failed on page {page}: {e}")
                if page == 1:  # If first page fails, raise error
                    raise
                break  # If later page fails, return what we have
        
        logger.info(f"Total transactions fetched: {len(all_transactions)}")
        return all_transactions


def get_yesterday_date_range() -> tuple[str, str]:
    """Get yesterday's date range in YYYY-MM-DD format."""
    yesterday = datetime.now() - timedelta(days=1)
    date_str = yesterday.strftime("%Y-%m-%d")
    return date_str, date_str
