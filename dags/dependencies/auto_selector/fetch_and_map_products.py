import json
from datetime import datetime, timedelta
from urllib.parse import urlparse
from google.cloud import bigquery
from google.auth import default

from dependencies.auto_selector.utils.logger import logger
from dependencies.auto_selector.utils.gcs import (
    store_data_to_jsonl_in_gcs, 
    check_cache_in_gcs, 
    check_html_in_gcs,
)

def fetch_and_map_products(input_file: str, output_file: str, **kwargs):
    """
    Fetch data from bigtable,
    Map products by domain and platform,
    Filter in failed scrape attributes,
    Store the mapped data to GCS.
    """
    if check_cache_in_gcs(output_file):
        logger.info(f"Cache found at {output_file}. Skipping fetch_and_map_products.")
        return "Cache found, skipping computation."

    try:
        credentials, project_id = default()
        client = bigquery.Client(credentials=credentials, project=project_id)
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        query = f"""
            SELECT * 
            FROM `phia-prod-416420.mixpanel.meta_data_scrape_failed` 
            WHERE _PARTITIONTIME >= TIMESTAMP("{yesterday}")
              AND _PARTITIONTIME < CURRENT_TIMESTAMP()
            LIMIT 10000
        """

        query_job = client.query(query)
        results = query_job.result()

        domain_to_products = {}
        for row in results:
            row_dict = dict(row)
            scraped_query = json.loads(row.get("scraped_query", "{}"))
            for key, value in row_dict.items():
                if isinstance(value, datetime):
                    row_dict[key] = value.isoformat()
            # "website" is related to failures in linkpasting ( which are from zyte )
            if row_dict.get("platform") == "website":
                continue
            product_url = row_dict.get("product_url", "")
            if not product_url:
                continue

            domain = urlparse(product_url).netloc
            if domain not in domain_to_products:
                domain_to_products[domain] = {"ios_extension": [], "web_extension": []}
            
            imageUrls = scraped_query.get("imageUrls", [])
            imageUrl = ""
            if imageUrls and isinstance(imageUrls, list):
                imageUrl = imageUrls[0] if imageUrls[0] else ""
            else: 
                urls = scraped_query.get("image", {}).get("urls", [])
                if len(urls) > 0: 
                    imageUrl = urls[0]
            product = {
                "product_url": product_url,
                "platform": row_dict.get("platform", ""),
                "title": scraped_query.get("scrapedName", ""),
                "price": scraped_query.get("scrapedPrice", 0),
                "brand": scraped_query.get("scrapedBrand", ""),
                "imageUrl": imageUrl,
                "color": scraped_query.get("scrapedColor", "")
            }
            # Group by platform
            if row_dict.get("platform") == "extension":
                domain_to_products[domain]["web_extension"].append(product)
            else:
                domain_to_products[domain]["ios_extension"].append(product)
        # Grouping by domain and platform ends here
        
        filtered_domain_to_products = {}
        for domain, platforms in domain_to_products.items():

            filtered_platforms = {}
            for plat_key in ["ios_extension", "web_extension"]:
                # Dont process the domain+platform which do not have required html contents in GCS
                if not check_html_in_gcs(domain, plat_key):
                    logger.info(f"HTML not found in GCS for {domain} - {plat_key}. Skipping.")
                    continue
                prod_list = platforms.get(plat_key, [])
                if not prod_list:
                    continue
                # Please note that this failed_scrape_attributes is the one which maintains failures throughout the pipeline
                failed_scrape_attributes = set()
                
                # TODO: Below should also consider the wrong scraping cases
                # For now only consider empty attributes along with single worded titles
                required_attributes = {"title", "price", "brand", "color", "imageUrl"}
                attributes = {
                    attr
                    for product in prod_list
                    for attr in required_attributes
                    if (
                        (attr == "title" and product.get("title") and len(product["title"].split()) > 1)
                        or (attr != "title" and product.get(attr))
                    )
                }
                missing_attributes = required_attributes - attributes
                failed_scrape_attributes.update(missing_attributes)
                                
                # If title and ImageUrl is already in failed_scrape_attributes, then no need to go through below process 
                if "title" not in failed_scrape_attributes or "imageUrl" not in failed_scrape_attributes:
                    # In a single platform, if there are different productUrls with same title or imageUrl, then mark them as failed attributes   
                    # Because Logically, we can have same color, brand or price but not title or imageUrl                    
                    for product in prod_list:
                        for target_product in prod_list:
                            if target_product.get("product_url") != product.get("product_url"):
                                if (target_product.get("title") and product.get("title") and target_product.get("title", "") == product.get("title", "")):
                                    failed_scrape_attributes.add("title")
                                if (target_product.get("imageUrl") and product.get("imageUrl") and target_product.get("imageUrl", "") == product.get("imageUrl", "")):
                                    failed_scrape_attributes.add("imageUrl")

                        if "title" in failed_scrape_attributes and "imageUrl" in failed_scrape_attributes:
                            break
                # if there is atleast one failed attribute, then only store the required information for that domain+platform
                if failed_scrape_attributes:
                    logger.info(f"Failed scrape attributes for {domain} - {plat_key}: {failed_scrape_attributes}")
                    filtered_platforms[plat_key] = {
                        "failed_scrape_attributes": list(failed_scrape_attributes)  
                    } 
                    
            if filtered_platforms:
                filtered_domain_to_products[domain] = filtered_platforms
        
        logger.info(f"Mapped {len(filtered_domain_to_products)} domains from BigQuery data.")
        store_data_to_jsonl_in_gcs(output_file, [filtered_domain_to_products])
        return "Mapped product data by domain and stored to GCS."
    except Exception as e:
        logger.error(f"Error in fetch_and_map_products: {e}")
        raise
