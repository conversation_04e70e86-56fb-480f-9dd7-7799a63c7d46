from openai import OpenAI

from dependencies.auto_selector.utils.logger import logger
from dependencies.auto_selector.utils.constants import OPENAI_API_KEY, SELECTOR_REVAMP_PROMPT
from dependencies.auto_selector.utils.gcs import (
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
    check_cache_in_gcs,
)

client = OpenAI(api_key=OPENAI_API_KEY)

def process_unreadable_selector(selector: str) -> str:
    """
    Uses a GPT call to process and revamp the given selector. If the GPT call fails or returns
    an empty response, returns the original selector.
    """
    prompt = SELECTOR_REVAMP_PROMPT(selector)
    try:
        response = client.chat.completions.create(
            model="o3-mini",
            messages=[
                {"role": "system", "content": "You are an expert web scraping engineer."},
                {"role": "user", "content": prompt},
            ],
        )
        content = response.choices[0].message.content.strip()
        # Remove markdown formatting if present
        if content.startswith("```"):
            content = content.strip("`").strip()
            if content.lower().startswith("json"):
                content = content[4:].strip()
        if not content:
            logger.error(f"Empty GPT response for selector: {selector}")
            return selector
        return content
    except Exception as e:
        logger.error(f"Error processing selector '{selector}': {e}")
        return selector

def clean_selectors(input_file: str, output_file: str, **kwargs):
    if check_cache_in_gcs(output_file):
        logger.info(f"Cache found at {output_file}. Skipping process_selectors.")
        return "Cache found, skipping computation."
    
    try:
        # Load the input data from GCS
        input_data = load_jsonl_from_gcs(input_file)
        if not input_data or len(input_data) == 0:
            logger.error("No selectors data found in GCS.")
            return "No data found."

        # Iterate over each domain's selectors structure
        for domain_data in input_data:
            for domain, platforms in domain_data.items():
                for plat_key, details in platforms.items():
                    selectors = details.get("selectors", {})
                    logger.info(f"Processing selectors for domain '{domain}', platform '{plat_key}'.")
                    # Process each individual selector
                    for sel_key, sel_value in selectors.items():
                        detailSelector = sel_value.get("selector", "")
                        if isinstance(detailSelector, str) and detailSelector.strip():
                            logger.info(f"Selector before: {detailSelector}")
                            revamped = process_unreadable_selector(detailSelector)
                            logger.info(f"Selector after: {revamped}")
                            # directly update the domain_data with the revamped selector
                            domain_data[domain][plat_key]["selectors"][sel_key]["selector"] = revamped
                    logger.info(f"Processed selectors for domain '{domain}', platform '{plat_key}'.")
        
        # Write the updated selectors back to GCS
        store_data_to_jsonl_in_gcs(output_file, input_data)
        logger.info(f"Processed selectors stored in {output_file}.")
        return "Processed selectors successfully."
    
    except Exception as e:
        logger.error(f"Error in process_selectors: {e}")
        raise