import json
from openai import OpenAI

from dependencies.auto_selector.utils.constants import (
    OPENAI_API_KEY,
    START_WINDOW,
    END_WINDOW,
    SELECTOR_PROMPT
)
from dependencies.auto_selector.utils.logger import logger
from dependencies.auto_selector.utils.snippeter import get_best_html_snippet
from dependencies.auto_selector.utils.gcs import (
    load_jsonl_from_gcs, 
    store_data_to_jsonl_in_gcs, 
    check_cache_in_gcs,
    get_html_from_gcs
)

client = OpenAI(api_key=OPENAI_API_KEY)

def generate_selectors(input_file: str, output_file: str, **kwargs):
    """
    Retrieve Another HTML from GCS for each product and platform,
    Use Snippeting and Pruning to get the best HTML snippet,
    Generate selectors for each product detail using o3-mini,
    Store the generated selectors to GCS.
    """
    if check_cache_in_gcs(output_file):
        logger.info(f"Cache found at {output_file}. Skipping generate_selectors.")
        return "Cache found, skipping computation."
    
    try:
        input_data = load_jsonl_from_gcs(input_file)
        if not input_data or len(input_data) == 0:
            logger.error("No HTML snippets data found in GCS.")
            return "No data"

        html_snippets = input_data[0]
        generated_selectors = {}

        for domain, platforms in html_snippets.items():
            domain_selectors = {}

            for plat_key in ["ios_extension", "web_extension"]:
                platform_data = platforms.get(plat_key, {})
                failed_scrape_attributes = platform_data.get("failed_scrape_attributes", [])
                if len(failed_scrape_attributes) == 0:
                    continue
                gpt_4o_mini_result = platform_data.get("gpt_product", {})

                # get HTML from GCS for this domain + platform
                gcs_product, gcs_html = get_html_from_gcs(domain, plat_key, 0)
                if gcs_product:
                    scraped_query = gcs_product.get("scraped_query", {})
                    second_product = {
                        "product_url": gcs_product.get("product_url", ""),
                        "title": scraped_query.get("scrapedName", ""),
                        "price": scraped_query.get("scrapedPrice", ""),
                        "brand": scraped_query.get("scrapedBrand", ""),
                        # "imageUrl": (scraped_query.get("image", {}).get("urls", [])[0], ""),
                        "color": scraped_query.get("scrapedColor", "")
                    }
                    html_to_use = gcs_html
                    logger.info("HTML from GCS")
                else:
                    logger.info("No GCS product, Skipping")
                    continue

                if not html_to_use:
                    logger.info(f"Skipping {second_product.get('product_url')} (domain: {domain}, platform: {plat_key}): No HTML retrieved.")
                    continue

                best_snippet_html = get_best_html_snippet(domain, plat_key, second_product, html_to_use)
                # best_snippet_html is the longest snippet found after pruning which has the main product in it
                if not best_snippet_html:
                    logger.error(f"Unable to snippet/prune {second_product.get('product_url')} (domain: {domain}, platform: {plat_key}).")
                    continue
                flag = 0
                not_found = 0
                # Rare case of HTML exceeding huge amounts, so we truncate it to 500k characters
                prompt = SELECTOR_PROMPT(best_snippet_html[-450000:])
                # This loop runs atmost 2 times in worst case scenario, usually one
                while(flag == 0):
                    try:
                        response = client.chat.completions.create(
                            model="o3-mini",
                            messages=[
                                {"role": "system", "content": "You are an expert web scraping engineer."},
                                {"role": "user", "content": prompt}
                            ],
                        )
                        content = response.choices[0].message.content.strip()
                        if content.startswith("```"):
                            content = content.strip("`").strip()
                            if content.lower().startswith("json"):
                                content = content[4:].strip()
                        if not content:
                            logger.error(f"Empty GPT response for domain {domain} platform {plat_key}.")
                            flag = 1
                            selectors = {}
                        try:
                            selectors = json.loads(content)
                            print(domain, plat_key, selectors)
                            print(failed_scrape_attributes)
                        except Exception as e:
                            logger.error(f"Error parsing GPT response for domain {domain} platform {plat_key}: {e}. Response content: {content}")
                            selectors = {}
                    except Exception as e:
                        logger.error(f"Error calling OpenAI API for domain {domain} platform {plat_key}: {e}")
                        selectors = {}
                    
                    if not selectors or not any(value for subdict in selectors.values() for value in subdict.values()) or (not selectors.get("price") and "price" in failed_scrape_attributes):
                        logger.info(f"No selectors generated for domain, so, iterating again with sliced upper html {domain} platform {plat_key}.")
                        n = len(html_to_use)
                        start_index = int((START_WINDOW - START_WINDOW) * n)
                        end_index = int((END_WINDOW - START_WINDOW) * n)
                        sliced_html = html_to_use[start_index:end_index]
                        if sliced_html == best_snippet_html:
                            logger.error(f"Best snippet is same as sliced html, so skipping : {domain} platform {plat_key}")
                            not_found = 1
                            flag = 1
                        best_snippet_html = sliced_html
                        prompt = SELECTOR_PROMPT(best_snippet_html[:450000])
                    else: 
                        flag = 1
                        domain_selectors[plat_key] = {
                            "selectors": selectors,
                            "gpt_product": gpt_4o_mini_result,
                            "failed_scrape_attributes": failed_scrape_attributes
                        }
                if not_found == 1 and not any(value for subdict in selectors.values() for value in subdict.values()):
                    logger.error(f"Selectors not found for {domain} platform {plat_key}.")
                    continue
            if domain_selectors:
                generated_selectors[domain] = domain_selectors

        store_data_to_jsonl_in_gcs(output_file, [generated_selectors])
        logger.info(f"Generated selectors for domains: {list(generated_selectors.keys())}")
        return "Generated selectors using o3-mini and stored to GCS."
    except Exception as e:
        logger.error(f"Error in generate_selectors: {e}")
        raise
