import json
import uuid
from dependencies.auto_selector.utils.gcs import (
    load_jsonl_from_gcs,
    store_data_to_jsonl_in_gcs,
    list_files_in_gcs,
)
from dependencies.auto_selector.utils.big_table import get_phia_dict, persist_site_config
from dependencies.auto_selector.utils.constants import (
    BASE_BACK_UP_PATH,
    SITE_CONFIG_APPLICATION,
    DEFAULT_STRATEGIES
)
from dependencies.auto_selector.utils.logger import logger

def push_selectors_to_bigtable(input_file: str, output_file: str, **kwargs):
    """
    Only push the validated selectors to Bigtable.
    Used failed scrape attributes to find which attributes to update.
    Read bigtable to get the existing site config.
    Only update the required attributes in the site config.
    Backup the existing site config before updating.
    """
    try:
        validated_data_list = load_jsonl_from_gcs(input_file)
        if not validated_data_list or len(validated_data_list) == 0:
            logger.error("No validated selectors data found in GCS.")
            return "No data"
        validated_data = validated_data_list[0]

        table = get_phia_dict()
        if not table:
            logger.error("Error getting Bigtable instance.")
            return "Bigtable error"     

        for domain, domain_data in validated_data.items():

            merged_updates = {}

            for plat_key in ["ios_extension", "web_extension"]:
                platform_data = domain_data.get(plat_key, {})
                gpt_o3_validations = platform_data.get("gpt_o3", [])
                gpt_4o_validations = platform_data.get("gpt_4o", [])
                failed_scrape_attributes = platform_data.get("failed_scrape_attributes", [])
                # Determine the platform key for site config: "mobile" for ios_platform, "desktop" for extension
                platform_selector_key = "mobile" if plat_key == "ios_extension" else "desktop"
                
                # For each GPT o3 validation, check if corresponding GPT 4o validation passes
                for gpt_o3_item in gpt_o3_validations:
                    detail = gpt_o3_item.get("detail")
                    gpt_o3_pass = gpt_o3_item.get("pass", False)
                    gpt_4o_item = next((item for item in gpt_4o_validations if item.get("detail") == detail), None)
                    gpt_4o_pass = gpt_4o_item.get("pass", False) if gpt_4o_item and isinstance(gpt_4o_item, dict) else False
                    if gpt_o3_pass and gpt_4o_pass:
                        if detail in failed_scrape_attributes:
                            selector = gpt_o3_item.get("selector")
                            attribute = gpt_o3_item.get("attribute", "text")
                            if detail == "imageUrl" and (attribute.lower() != "src" or attribute.lower() != "srcset"): 
                                attribute = "src"
                            value = gpt_o3_item.get("expected", gpt_o3_item.get("actual", ""))
                            # replace single quotes with double quotes in selector for QueryParsing
                            selector = selector.replace('\'', '"')
                            supportedSelector = selector.replace('"', '\"')
                            if attribute.lower() == "text":
                                query_str = f"query('{supportedSelector}').getAttr('innerText')"
                            else:
                                query_str = f"query('{supportedSelector}').getAttr('{attribute}')"
                                if detail == "imageUrl":
                                    # Some cases where imageUrl is not correct Url, but part of Url
                                    logger.info(f"Updating imageUrl for {domain} with value: {value}")
                                    if value:
                                        if not value.startswith("https:"):
                                            if value.startswith("//"):
                                                query_str = f"query('{supportedSelector}').getAttr('{attribute}').toArray().unshift('https:').join()"
                                            elif value.startswith("/"):
                                                query_str = f"query('{supportedSelector}').getAttr('{attribute}').toArray().unshift('https://{domain}').join()"
                                            else: 
                                                query_str = f"query('{supportedSelector}').getAttr('{attribute}').toArray().unshift('https://{domain}/').join()"
                            # Update merged_updates for this detail
                            if detail not in merged_updates:
                                merged_updates[detail] = {}
                            merged_updates[detail][platform_selector_key] = {"query": query_str}
                            
            if not merged_updates:
                logger.info(f"No details to update for domain {domain}.")
                continue

            # Build the Bigtable row key
            row_key = f"siteConfig:https://{domain}"
            backup_file = f"{BASE_BACK_UP_PATH}/{domain.replace('.', '_')}.jsonl"
            logger.info(f"Backup file for {domain}: {backup_file}")
            existing_backups = list_files_in_gcs(backup_file)
            if not existing_backups:
                row = table.read_row(row_key)
                if row:
                    cells = row.cells.get("values", {}).get(SITE_CONFIG_APPLICATION.encode("utf-8"), [])
                    if cells:
                        existing_config_json = cells[0].value.decode("utf-8")
                        store_data_to_jsonl_in_gcs(backup_file, [existing_config_json])
                        logger.info(f"Backed up existing config for domain {domain} to {backup_file}")
            else:
                logger.info(f"Backup file {backup_file} already exists; skipping backup.")
            
            # Retrieve current site config from Bigtable
            row = table.read_row(row_key)
            if row:
                cells = row.cells.get("values", {}).get(SITE_CONFIG_APPLICATION.encode("utf-8"), [])
                if cells:
                    existing_config = json.loads(cells[0].value.decode("utf-8"))
                else:
                    existing_config = None
            else:
                existing_config = None

            # Use existing site config or create a new one
            if existing_config:
                site_config = existing_config
            else:
                site_config = {
                    "id": uuid.uuid4().hex[:25],
                    "hostname": f"https://{domain}",
                    "strategies": [],
                    "resaleConfig": None
                }
            # Get or create the SELECTOR strategy
            selector_strategy = next((s for s in site_config.get("strategies", []) if s.get("name") == "SELECTOR"), None)
            if not selector_strategy:
                # If no SELECTOR strategy exists, set strategies to the entire default list
                site_config["strategies"] = DEFAULT_STRATEGIES.copy()
                selector_strategy = site_config["strategies"][0]
            else:
                if selector_strategy.get("config") is None:
                    selector_strategy["config"] = {"timeout": 2000, "scrapeDefinitions": []}
                else:
                    selector_strategy["config"].setdefault("timeout", 2000)
                    selector_strategy["config"].setdefault("scrapeDefinitions", [])
            
            # Merge new updates with existing scrapeDefinitions
            existing_defs = selector_strategy["config"]["scrapeDefinitions"]
            existing_details = {}
            for d in existing_defs:
                attr_val = d.get("attribute")
                if attr_val:
                    existing_details.setdefault(attr_val, []).append(d)
            
            # For each detail in merged_updates, update or add definitions
            for detail, update_obj in merged_updates.items():
                # For each platform key (mobile/desktop) in update_obj
                for platform_key, upd in update_obj.items():
                    if detail in existing_details:
                        updated = False
                        for def_obj in existing_details[detail]:
                            # If the definition already has the platform key, update it
                            if platform_key in def_obj:
                                def_obj[platform_key]["query"] = upd["query"]
                                updated = True
                            else:
                                # Otherwise, add the platform key with the query
                                def_obj[platform_key] = {"query": upd["query"]}
                                updated = True
                            # Also ensure the alternate platform key exists
                            alternate_key = "mobile" if platform_key == "desktop" else "desktop"
                            if alternate_key not in def_obj:
                                def_obj[alternate_key] = {"query": upd["query"]}
                                updated = True
                            if updated:
                                break
                        if not updated:
                            # Append new definition if none were updated
                            alternate_key = "mobile" if platform_key == "desktop" else "desktop"
                            existing_details[detail].append({
                                "attribute": detail,
                                platform_key: {"query": upd["query"]},
                                alternate_key: {"query": upd["query"]}
                            })
                    else:
                        # No existing definition for this detail; create a new one
                        platform_key = list(update_obj.keys())[0]
                        alternate_key = "mobile" if platform_key == "desktop" else "desktop"
                        existing_details[detail] = [{
                            "attribute": detail,
                            platform_key: {"query": update_obj[platform_key]["query"]},
                            alternate_key: {"query": update_obj[platform_key]["query"]}
                        }]

            merged_defs = []
            for defs in existing_details.values():
                merged_defs.extend(defs)
            selector_strategy["config"]["scrapeDefinitions"] = merged_defs

            # Upsert updated site config into Bigtable
            site_config_json = json.dumps(site_config)
            persist_site_config(row_key, site_config_json)
            logger.info(f"Updated site config for domain {domain}, with details: {list(merged_updates.keys())}")
        
        logger.info("Successfully pushed updated selectors to Bigtable and backed up existing config.")
        return "Pushed selectors to Bigtable and backed up existing config."
    except Exception as e:
        logger.error(f"Error in push_selectors_to_bigtable: {e}", exc_info=True)
        raise
