import json
import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from dependencies.auto_selector.utils.gcs import load_jsonl_from_gcs, list_files_in_gcs
from dependencies.auto_selector.utils.big_table import persist_site_config
from dependencies.auto_selector.utils.constants import BASE_BACK_UP_PATH
from dependencies.auto_selector.utils.logger import logger

def restore_site_configs_from_backup(backup_folder: str = None):
    """
    Iterates over all backup files in the given GCS backup folder and upserts each site config into Bigtable.
    
    Each backup file is expected to contain one JSON object (in JSONL format) representing the site configuration.
    
    For each backup file:
      - The function checks if backup data exists in GCS.
      - It loads the site config.
      - It determines the hostname either from the site config (if present) or from the filename.
      - It then builds a row key as "siteConfig:{hostname}" and upserts the entire config into Bigtable.
    
    If backup_folder is not provided, it defaults to BASE_BACK_UP_PATH.
    """
    if backup_folder is None:
        backup_folder = BASE_BACK_UP_PATH
    
    logger.info(f"Restoring site configs from backup folder: {backup_folder}")
    backup_files = list_files_in_gcs(backup_folder)
    if not backup_files:
        logger.error("No backup files found in GCS.")
        return "No backup files found."
    
    try:
        for file_path in backup_files:
            logger.info(f"Processing backup file: {file_path}")
            backup_data_list = load_jsonl_from_gcs(file_path)
            if not backup_data_list or len(backup_data_list) == 0:
                logger.error(f"No data in backup file: {file_path}")
                continue
            
            site_config = json.loads(backup_data_list[0])
            
            if "hostname" in site_config:
                hostname = site_config["hostname"]
            else:
                filename = os.path.basename(file_path)
                hostname = filename.replace(".jsonl", "").replace("_", ".")
            
            site_config_json = json.dumps(site_config)
            row_key = f"siteConfig:{hostname}"
            persist_site_config(row_key, site_config_json)
            logger.info(f"Upserted site config for hostname: {hostname}")
        
        logger.info("Successfully restored all site configs from backup to Bigtable.")
        return "Restored site configs from backup to Bigtable."
    except Exception as e:
        logger.error(f"Error in restore_all_site_configs_from_backup: {e}", exc_info=True)
        raise

if __name__ == '__main__':
    restore_site_configs_from_backup()
