import re
import datetime

TEST_ID = datetime.datetime.now().strftime("%Y_%m_%d")
BACK_UP = datetime.datetime.now().strftime("%Y_%m_%d")

BASE_GCS_PATH = f"gs://auto-selectors-pipeline/auto_selectors/{TEST_ID}"
BASE_BACK_UP_PATH = f"gs://auto-selectors-pipeline/auto_selectors_back_up/{BACK_UP}"

PROJECT_ID = 'phia-prod-416420'
INSTANCE_ID = 'phia-dict'
TABLE_ID = 'phia-dict'
SITE_CONFIG_APPLICATION = "site_config"

MAP_PRODUCTS_OUTPUT = f"{BASE_GCS_PATH}/fetch_and_map_products.jsonl"
FILTERED_MAPPING_OUTPUT = f"{BASE_GCS_PATH}/filtered_mapping.jsonl"
HTML_SNIPPETS_OUTPUT = f"{BASE_GCS_PATH}/html_snippets.jsonl"
GENERATED_SELECTORS_OUTPUT = f"{BASE_GCS_PATH}/generated_selectors.jsonl"
CLEANED_SELECTORS_OUTPUT = f"{BASE_GCS_PATH}/cleaned_selectors.jsonl"
VALIDATION_RESULTS_OUTPUT = f"{BASE_GCS_PATH}/validation_results.jsonl"
FINAL_RESULTS_OUTPUT = f"{BASE_GCS_PATH}/final_validated_selectors.jsonl"

ZYTE_API_ENDPOINT = "https://api.zyte.com/v1/extract"
ZYTE_API_KEY = "7156bdf7b6254a7199ae30844cfa8313"

OPENAI_API_KEY = "**********************************************************************************************************************"
OPENAI_MODEL = "o3-mini"

PRICE_REGEX = re.compile(
    r'(?:\$|USD\s?)[\s]*\d+(?:\.\d{1,2})?|\d+(?:\.\d{1,2})?[\s]*(?:\$|USD)', 
    re.IGNORECASE
)

JS_REGEX = re.compile(r'\b(var|const|let|function|=>)\b|[{};]', re.IGNORECASE)

DEFAULT_STRATEGIES = [
    {
        "name": "SELECTOR",
        "priority": 1,
        "config": {
            "timeout": 1000,
            "scrapeDefinitions": []
        }
    },
    {
        "name": "SHOPIFY",
        "priority": 2,
        "config": None
    },
    {
        "name": "JSONLD",
        "priority": 3,
        "config": None
    },
    {
        "name": "RDFA",
        "priority": 4,
        "config": None
    },
    {
        "name": "MICRODATA",
        "priority": 5,
        "config": None
    },
    {
        "name": "OPENGRAPH",
        "priority": 6,
        "config": None
    }
]

START_WINDOW = 0.2
END_WINDOW = 1.0

def METADATA_PROMPT(html): 
    return f"""
        You are an expert web scraping and content analysis engine with deep expertise in product page structures.
        I will provide you with the full HTML content of a product page. Your task is to analyze this HTML carefully and extract key product metadata with high accuracy, focusing on the main product information and ignoring irrelevant content.

        Your objectives are as follows:
        1. Determine whether the page contains valid information for the product's title, color, price, and brand using the definitions below.
        2. Identify and extract the specific HTML tag (referred to as the **Product Tag**) that encapsulates all the main product details: Title, Price, Color, Image, and Brand which is technically the Product Container.

        Definitions:
        - **Title**: A valid title is a non-empty string that clearly identifies the main product on the page (e.g., a model name or descriptive title).
        - **Color**: A valid color is non-empty text representing the main product's color (e.g., "Red", "Blue", etc.).
        - **Price**: A valid price is a positive number representing the main product's price. It may include a currency symbol and is typically located near the product title (e.g., "$49.99", "USD 39.95", or "39.99").
        - **Brand**: A valid brand is a non-empty string representing the main product's brand.

        Instructions for Extraction:
        - For each field, if valid data exists, extract it and set the corresponding flag to true; otherwise, return an empty string and set the flag to false.
        - **Important:** When valid information is present in the HTML, do not default to an empty string. Only return an empty string if the information is not found or is invalid.

        Return your output strictly as a JSON object with exactly the following keys:
        - `"titlePresent"`: (boolean) True if a valid title is found, false otherwise.
        - `"title"`: (string) The extracted product title if valid, or an empty string if not.
        - `"colorPresent"`: (boolean) True if a valid color is found, false otherwise.
        - `"color"`: (string) The extracted product color if valid, or an empty string if not.
        - `"pricePresent"`: (boolean) True if a valid price is found, false otherwise.
        - `"price"`: (string) The extracted product price if valid, or an empty string if not.
        - `"brandPresent"`: (boolean) True if a valid brand is found, false otherwise.
        - `"brand"`: (string) The extracted product brand if valid, or an empty string if not.

        **Important Note**: Always consider discounted/reduced prices if present else go with the original price.
        Do not include any additional text or commentary; output only a valid JSON object exactly as specified.
        Also, DO NOT include any samples or examples for any of the attributes. your only source would be the HTML provided below.

        HTML:
        <<<
        {html}
        >>>
    """


def SELECTOR_PROMPT(html):
    return f"""
        You are a highly skilled web scraping engineer specializing in creating human-readable and robust CSS selectors.

        **Task:**  
        Analyze the HTML below that represents a complete product element on a webpage. Generate generic CSS selectors for the Main Product Details to extract the following information:
        - **title**
        - **price**
        - **imageURL**
        - **color**
        - **brand**

        **Instructions:**  
        1. **Output Format:**  
        - Output a single valid JSON object with exactly these keys: `"title"`, `"price"`, `"imageUrl"`, `"color"`, and `"brand"`.  
        - Each key must map to an object with exactly three keys:
            - `"selector"`: A valid, human-readable CSS selector that reliably locates the element. Use semantic tag names, clear class names, or data attributes. **Do not** use numbers, absolute paths, positional indexes, or unreadable strings.
            - `"attribute"`: Specify `"text"` if the desired information is within the text content, or provide the specific attribute name (for example, `"src"` for an image).
            - `"value"`: The actual content from the element as it appears in the HTML. **Ensure that this value is not empty if a selector is found.** If a selector targets an element with an empty value, selector must be an empty string as well.

        2. **Selector Quality & Reliability:**  
        DOs:
        - Ensure that each CSS selector is human-readable and semantically clear.  
        - Use relative DOM paths that work reliably across similar product pages and target the currently displayed values (e.g., user-selected color, main product image).
        DONTs: 
        - Please avoid considering numbers as a part of selectors.
        - Please don't consider `meta[properties]` as a part of selectors.
        - Please don't consider the selector if it contains unreadable strings.
        
        **Important Note to build Selectors:**
            - For example, consider : span.item-uikpbqs[data-id='product'], here you can observe that 'uikpbqs' is an unreadable string. We really don't want unreadable strings in selectors. So, instead of using this selector as it is.
            Instead, revamp it to : span[class*='item'][data-id='product']. This selector is more readable and reliable.
            Technically use * when strings are unreadable. 

        3. **Detail-Specific Requirements:**  
        - **Title:** The selector must target the main product's title.
        - **Price:** The selector must target the main product's price which is usually after product title's html tag.
        - **ImageUrl:** The selector must target an `<img>` tag whose `src` attribute ends with a common image extension (e.g., `.png`, `.jpg`).
        - **Color:** The selector must target the main product's color (e.g., blue, red).
        - **Brand:** The selector must target the main product's brand (e.g., Nike, Adidas).

        4. **Fallback:**  
        - If you cannot locate valid selectors for anay of the detils following the above instructions, then return an empty object for that detail.

        5. **Source Limitation:**  
        - Use only the HTML provided below. Do not use any external resources.
        - Please do not give any sample or example selectors/values in the objects. Only provide the selectors/values that you find in the HTML.

        **Important Note for Price Selectors/Values**: Always consider discounted/reduced price of main product if present, else go with the original price of the main product.
        DO NOT include any additional text or commentary; output only a valid JSON object exactly as specified.

        **HTML:**  
        <<<  
            " + {html} + "  
        >>>
    """
    
def SELECTOR_REVAMP_PROMPT(selector: str) -> str:
    return f"""
        You are an expert CSS selector engineer specializing in web scraping. Your task is to revamp the given CSS selector to make it more reliable, readable, and maintainable.

        Guidelines for revamping the selector:
        1. Replace unreadable strings (random characters like "uikpbqs") with wildcards using [attribute*="value"] syntax
        2. Remove any numeric indices in selectors as they make selectors brittle and prone to breaking
        3. Use semantic attributes and meaningful class name patterns where possible
        4. Prefer data-* attributes when available as they're typically more stable
        5. Simplify overly complex selectors while maintaining proper specificity
        6. Focus on creating selectors that will be robust across similar pages
        7. Avoid absolute paths that traverse the entire DOM hierarchy

        Examples:
        Original: span.item-uikpbqs[data-id='product']
        Improved: span[class*='item'][data-id='product']

        Original: div.product-container > div:nth-child(3) > span.price-xj29a
        Improved: div[class*='product-container'] span[class*='price']

        Original: #product-info-374928 .title
        Improved: [id*='product-info'] .title

        Please revamp this selector:
        {selector}

        *Important*: Return only the improved selector without any explanation.
    """
    