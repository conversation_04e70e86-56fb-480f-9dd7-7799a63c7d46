from google.cloud import bigtable
from google.auth import default

from dependencies.auto_selector.utils.constants import PROJECT_ID, INSTANCE_ID, TABLE_ID, SITE_CONFIG_APPLICATION
from dependencies.auto_selector.utils.logger import logger

def get_phia_dict():
    """
    Returns the Bigtable Table object for Phia Dict.
    """
    try:
        credentials, _ = default()
        client = bigtable.Client(project=PROJECT_ID, credentials=credentials, admin=True)
        instance = client.instance(INSTANCE_ID)
        table = instance.table(TABLE_ID)
        return table
    except Exception as e:
        logger.error("Error getting Phia Dict", exc_info=True)
        return None

def write_to_phia_dict(application: str, key: str, value: str) -> None:
    """
    Upserts a row in Bigtable.
    Parameters:
      - application: The column qualifier (e.g. "site_config").
      - key: The row key (format "siteConfig:{hostname}").
      - value: The JSON string representing the site config.
    This function always updates the row (even for small changes).
    """
    try:
        table = get_phia_dict()
        if not table:
            return

        new_row = table.row(key)
        new_row.set_cell(
            column_family_id="values",
            column=application,
            value=value
        )
        new_row.commit()
        logger.info(f"Upserted row {key} with value: {value}")
    except Exception as e:
        logger.error("Error writing to Phia Dict", exc_info=True)

def persist_site_config(key: str, value: str) -> None:
    """
    Persists the site configuration data to Bigtable.
    The key should be in the format "siteConfig:{hostname}" and value is the site config as a JSON string.
    """
    write_to_phia_dict(SITE_CONFIG_APPLICATION, key, value)