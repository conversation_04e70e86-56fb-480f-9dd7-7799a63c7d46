import gzip
import json
import base64
from google.cloud import storage

def load_jsonl_from_gcs(gcs_path):
    """Loads a JSONL file from Google Cloud Storage."""
    try:
        client = storage.Client()
        bucket_name, blob_name = gcs_path.replace("gs://", "").split("/", 1)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        data = blob.download_as_string().decode("utf-8")
        return [json.loads(line) for line in data.split("\n") if line]
    except Exception as e:
        print(f"An error occurred while loading JSONL from GCS: {e}")
        return None

def load_json_from_gcs(gcs_path):
    """Loads a JSON file from Google Cloud Storage."""
    try:
        client = storage.Client()
        bucket_name, blob_name = gcs_path.replace("gs://", "").split("/", 1)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        data = blob.download_as_string().decode("utf-8")
        return json.loads(data)
    except Exception as e:
        print(f"An error occurred while loading JSON from GCS: {e}")
        return None

def store_data_to_jsonl_in_gcs(gcs_path, data):
    """Stores data to a JSONL file in Google Cloud Storage."""
    try:
        client = storage.Client()
        bucket_name, blob_name = gcs_path.replace("gs://", "").split("/", 1)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        blob.upload_from_string("\n".join(json.dumps(line) for line in data))
    except Exception as e:
        print(f"An error occurred while storing data to JSONL in GCS: {e}")

def check_cache_in_gcs(gcs_path):
    """Check if the output already exists in the Google Cloud Storage bucket."""
    try:
        client = storage.Client()
        bucket_name, blob_name = gcs_path.replace("gs://", "").split("/", 1)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        return blob.exists()
    except Exception as e:
        print(f"An error occurred while checking cache in GCS: {e}")
        return False

def list_files_in_gcs(gcs_prefix: str):
    """
    List all blob names under the given GCS prefix.
    Example: gcs_prefix = "gs://auto_selectors-pipeline/auto_selectors/back-up_01"
    Returns a list of full GCS paths.
    """
    try:
        # Remove "gs://" and split into bucket and folder.
        parts = gcs_prefix.replace("gs://", "").split("/", 1)
        bucket_name = parts[0]
        prefix = parts[1] if len(parts) > 1 else ""
        client = storage.Client()
        bucket = client.bucket(bucket_name)
        blobs = bucket.list_blobs(prefix=prefix)
        file_paths = [f"gs://{bucket_name}/{blob.name}" for blob in blobs if not blob.name.endswith("/")]
        return file_paths
    except Exception as e:
        print(f"Error listing files in GCS for prefix {gcs_prefix}: {e}")
        return []


def decompress_html(input_str: str) -> str:
    """
    Given an input string that might be:
      - Plain text
      - Base64-encoded plain text, or
      - Base64-encoded gzip compressed data,
    this function returns the plain text version.
    """
    # Try to decode using Base64 with validation.
    try:
        # validate=True ensures that the input is valid Base64.
        decoded = base64.b64decode(input_str, validate=True)
    except Exception:
        # If decoding fails, assume input_str is plain text.
        return input_str

    # If the decoded data starts with the gzip magic header (b'\x1f\x8b'),
    if decoded.startswith(b'\x1f\x8b'):
        try:
            decompressed = gzip.decompress(decoded)
            return decompressed.decode("utf-8")
        except Exception as e:
            print("Gzip decompression failed:", e)
            return decoded.decode("utf-8", errors="replace")
    else:
        # If not gzip compressed, assume it's just Base64-encoded plain text.
        try:
            return decoded.decode("utf-8")
        except Exception as e:
            print("Decoding decoded data failed:", e)
            return decoded.decode("utf-8", errors="replace")


def get_html_from_gcs(domain, plat_key, index):
    """
    Given a domain and a platform key, attempt to load the corresponding HTML file from GCS.
    Assumes files are stored under:
      gs://auto-selectors-pipeline/html_contents/{folder}/{domain_replaced}/
    where folder is "ios" if plat_key contains "ios", otherwise "web".
    Returns a tuple (gcs_product, html_content) if found, else (None, None).
    """
    folder = "ios" if "ios" in plat_key.lower() else "web"
    domain_folder = domain.replace('.', '_')
    prefix = f"gs://auto-selectors-pipeline/html_contents/{folder}/{domain_folder}/"
    
    files = list_files_in_gcs(prefix)
    if files and index < len(files):
        data = load_json_from_gcs(files[index])
        if data:
            gcs_product = data
            html_content = gcs_product.get("htmlContent")
            if html_content:
                try:
                    html_content = decompress_html(html_content)
                except Exception as e:
                    print("Error processing htmlContent:", e)
            return gcs_product, html_content
    return None, None


def check_html_in_gcs(domain, plat_key):
    """
    Given a domain and a platform key, check if the corresponding HTML file exists in GCS.
    Assumes files are stored under:
      gs://auto-selectors-pipeline/html_contents/{folder}/{domain_replaced}/
    where folder is "ios" if plat_key contains "ios", otherwise "web".
    Returns True if the two or more html contents are present else false.
    """
    folder = "ios" if "ios" in plat_key.lower() else "web"
    domain_folder = domain.replace('.', '_')
    prefix = f"gs://auto-selectors-pipeline/html_contents/{folder}/{domain_folder}/"
    files = list_files_in_gcs(prefix)
    return (files and len(files) >= 2)