from bs4 import Tag

from dependencies.auto_selector.utils.constants import JS_REGEX

def prune_element(element):
    """
    Recursively prune the element tree so that:
      - All <script> and <svg> tags are removed.
      - For each tag:
            * If the tag is an <img>, check if it has a "srcset" attribute.
              If so, update it to keep only the first image entry.
            * Otherwise, remove inline JavaScript event attributes (those starting with "on"),
              remove the "style" and "href" attributes,
              and if the tag's own text (after stripping) contains inline JavaScript markers 
              (e.g. keywords like "var", "const", "let", "function", "=>" or symbols {, }, ;),
              remove the tag entirely.
            * If the tag does not have a "class" or "id" attribute, unwrap it (remove the tag but keep its content).
            * Otherwise, remove all attributes except "class" and "id".
      - Finally, at each level of the DOM tree, remove isolated sibling tags 
        (non-<img> tags that have no child tags and no non-whitespace text).
    Returns the pruned element.
    """
    # Remove all <script> and <svg> tags.
    for tag in element.find_all(["script", "svg"]):
        tag.decompose()
    
    # Process all descendant tags.
    for tag in list(element.find_all(True)):
        # Ensure tag.name is not None.
        if not tag.name:
            continue

        # If the tag is an <img>, process its srcset attribute if present.
        if tag.name.lower() == "img":
            if tag.attrs and "srcset" in tag.attrs:
                srcset_val = tag.get("srcset")
                entries = [entry.strip() for entry in srcset_val.split(",") if entry.strip()]
                if entries:
                    tag["srcset"] = entries[0]
            continue
        
        # Remove inline JavaScript event attributes.
        if tag.attrs:
            for attr in list(tag.attrs):
                if attr.lower().startswith("on"):
                    del tag.attrs[attr]
        
        # Remove the "style" and "href" attributes if present.
        if tag.attrs and "style" in tag.attrs:
            del tag.attrs["style"]
        if tag.attrs and "href" in tag.attrs:
            del tag.attrs["href"]
        
        # Check if the tag's text contains inline JS markers.
        tag_text = tag.get_text(strip=True)
        if tag_text and JS_REGEX.search(tag_text):
            tag.decompose()
            continue
    
    # Do not Remove isolated siblings, they are useful for selectors
    return element
