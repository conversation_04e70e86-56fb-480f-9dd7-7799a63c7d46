from bs4 import BeautifulSoup

from dependencies.auto_selector.utils.prune import prune_element
from dependencies.auto_selector.utils.constants import START_WINDOW, END_WINDOW
from dependencies.auto_selector.utils.logger import logger

def get_best_html_snippet(domain, plat_key, first_product, html_to_use):
  n = len(html_to_use)
  if n > 0:
    start_index = int(START_WINDOW * n)
    end_index = int(END_WINDOW * n)
    sliced_html = html_to_use[start_index:end_index]
    snippet_soup = BeautifulSoup(sliced_html, 'html.parser')

    logger.info(f"Returning pruned html {first_product.get('product_url')} (domain: {domain}, platform: {plat_key}).")
    pruned_html = str(prune_element(snippet_soup))
    if len(pruned_html) > 5000:
      return pruned_html
    else:
      return sliced_html