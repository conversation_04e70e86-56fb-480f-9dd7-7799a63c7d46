import json
from openai import OpenAI

from dependencies.auto_selector.utils.gcs import (
    load_jsonl_from_gcs, 
    store_data_to_jsonl_in_gcs, 
    get_html_from_gcs,
    check_cache_in_gcs
)
from dependencies.auto_selector.utils.constants import OPENAI_API_KEY, METADATA_PROMPT
from dependencies.auto_selector.utils.snippeter import get_best_html_snippet
from dependencies.auto_selector.utils.logger import logger

client = OpenAI(api_key=OPENAI_API_KEY)

def filter_product_urls_by_zyte(input_file: str, output_file: str, **kwargs):
    """
    Retrieve Html from GCS for each product and platform,
    Use Snippeting and Pruning to get the best HTML snippet,
    Add more attributes to failed scrape attributes leveraging GPT-4o-mini,
    Store the filtered mapping to GCS.
    """
    if check_cache_in_gcs(output_file):
        logger.info(f"Cache found at {output_file}. Skipping filter_product_urls_by_zyte.")
        return "Cache found, skipping computation."
    
    try:
        input_data = load_jsonl_from_gcs(input_file)
        if not input_data or len(input_data) == 0:
            logger.error("No product mapping data found in GCS.")
            return "No data"

        domain_to_products = input_data[0]
        filtered_mapping = {}

        for domain, platforms in list(domain_to_products.items())[:150]:
            filtered_platforms = {}
            # Process each platform separately.
            for plat_key in ["ios_extension", "web_extension"]:
                platform_details = platforms.get(plat_key, {})
                failed_scrape_attributes = platform_details.get("failed_scrape_attributes", [])

                # get HTML from GCS for this domain + platform
                gcs_product, gcs_html = get_html_from_gcs(domain, plat_key, 1)
                if gcs_product:
                    scraped_query = gcs_product.get("scraped_query", {})
                    urls = scraped_query.get("image", {}).get("urls", [])
                    first_product = {
                        "product_url": gcs_product.get("product_url", ""),
                        "title": scraped_query.get("scrapedName", ""),
                        "price": scraped_query.get("scrapedPrice", ""),
                        "brand": scraped_query.get("scrapedBrand", ""),
                        "imageUrl": urls[0] if len(urls) > 0 else "",
                        "color": scraped_query.get("scrapedColor", "")
                    }
                    html_to_use = gcs_html
                    logger.info("HTML FROM GCS")
                else:
                    logger.info("No GCS product, Skipping")
                    continue

                if not html_to_use:
                    logger.info(f"Skipping {first_product.get('product_url')} (domain: {domain}, platform: {plat_key}): No HTML retrieved.")
                    continue

                best_snippet_html = get_best_html_snippet(domain, plat_key, first_product, html_to_use)
                # best_snippet_html is the longest snippet found after pruning which has the main product in it
                if not best_snippet_html:
                    logger.error(f"Unable to snippet/prune {first_product.get('product_url')} (domain: {domain}, platform: {plat_key}).")
                    continue

                # Rare case of HTML exceeding huge amounts, so we truncate it to 380k characters
                prompt = METADATA_PROMPT(best_snippet_html[-380000:])
                try:
                    response = client.chat.completions.create(
                        model="gpt-4o-mini",
                        messages=[
                            {"role": "system", "content": "You are an expert web scraping and content analysis engineer."},
                            {"role": "user", "content": prompt}
                        ],
                    )
                    content = response.choices[0].message.content.strip()
                    # For sometimes, the response is in markdown format, so we need to strip the markdown
                    if content.startswith("```"):
                        content = content.strip("`").strip()
                        if content.lower().startswith("json"):
                            content = content[4:].strip()
                    if not content:
                        logger.error(f"Empty GPT response for {first_product.get('product_url')} (domain: {domain}, platform: {plat_key}).")
                        continue
                    try:
                        gpt_result = json.loads(content)
                        print(gpt_result)
                    except Exception as e:
                        logger.error(f"Error parsing GPT response for {first_product.get('product_url')} (domain: {domain}, platform: {plat_key}): {e}. Response content: {content}")
                        continue
                except Exception as e:
                    logger.error(f"Error calling GPT API for {first_product.get('product_url')} (domain: {domain}, platform: {plat_key}): {e}")
                    continue
                    
                if first_product.get("title") and first_product.get("brand") and first_product.get("imageUrl"):
                    # If GPT indicates missing color and price, skip this product
                    if not (gpt_result.get("colorPresent") or gpt_result.get("color") or gpt_result.get("pricePresent") or gpt_result.get("price")):
                        if ["color", "price"] in failed_scrape_attributes and len(failed_scrape_attributes) == 2:
                            logger.info(f"Skipping {first_product.get('product_url')} (domain: {domain}): GPT indicates missing color or price.")
                            continue
                    
                    # If title from gpt doesnt match with the title from scraped product, then add title to failed_scrape_attributes
                    if "title" not in failed_scrape_attributes and gpt_result.get("titlePresent") and gpt_result.get("title") and (gpt_result.get("title").lower() != first_product.get("title").lower()):
                        failed_scrape_attributes.append("title")
                logger.info(f"Filtered (domain: {domain}, platform: {plat_key}) with failed scrape attributes: {failed_scrape_attributes}")

                if failed_scrape_attributes:
                    filtered_platforms[plat_key] = {
                        "gpt_product": gpt_result,
                        "failed_scrape_attributes": failed_scrape_attributes
                    }
            if filtered_platforms:
                logger.info(f"Filtered {domain} for {len(filtered_platforms)} platforms.")
                filtered_mapping[domain] = filtered_platforms

        logger.info(f"Filtered down to {len(filtered_mapping)} domains after GPT validation via HTML.")
        store_data_to_jsonl_in_gcs(output_file, [filtered_mapping])
        return "Filtered product URLs via GPT-4o-mini and stored to GCS."
    except Exception as e:
        logger.error(f"Error in filter_product_urls_by_zyte: {e}")
        raise
