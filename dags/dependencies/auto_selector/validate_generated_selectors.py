from bs4 import BeautifulSoup

from dependencies.auto_selector.utils.gcs import (
    load_jsonl_from_gcs, 
    store_data_to_jsonl_in_gcs, 
    get_html_from_gcs,
    check_cache_in_gcs
)
from dependencies.auto_selector.utils.logger import logger

def validate_generated_selectors(input_file: str, output_file: str, **kwargs) -> dict:
    """
    Two step validation process:
    1. Validate GPT o3 mini product selectors
    2. Validate GPT 4o mini product selectors
    Include a Pass flag for each selector across both validations and store the results.
    Store the validation results to GCS.
    """
    if check_cache_in_gcs(output_file):
        logger.info(f"Cache found at {output_file}. Skipping validate_generated_selectors.")
        return "Cache found, skipping computation."
      
    try:
        input_data = load_jsonl_from_gcs(input_file)
        if not input_data or len(input_data) == 0:
            logger.error("No generated selectors data found in GCS.")
            return "No data"
        validation_data = input_data[0]
        final_results = {}

        for domain, domain_data in validation_data.items():
            domain_results = {}

            for plat_key in ["ios_extension", "web_extension"]:
                platform_data = domain_data.get(plat_key, {})
                selectors_dict = platform_data.get("selectors", {})
                _, gcs_html_o3 = get_html_from_gcs(domain, plat_key, 0)
                _, gcs_html_4o = get_html_from_gcs(domain, plat_key, 1)
                html_snippet_gpt_o3 = gcs_html_o3
                html_snippet_gpt_4o = gcs_html_4o
                gpt_4o_mini_product = platform_data.get("gpt_product", {})
                failed_scrape_attributes = platform_data.get("failed_scrape_attributes", [])
                
                # GPT o3 mini Validation
                snippet_results = []
                if html_snippet_gpt_o3:
                    soup = BeautifulSoup(html_snippet_gpt_o3, "html.parser")
                    for detail, sel_obj in selectors_dict.items():
                        if not isinstance(sel_obj, dict):
                            logger.error(f"For domain {domain} platform {plat_key}, selector for '{detail}' is not a dict. Skipping.")
                            continue
                        css = sel_obj.get("selector")
                        if not css:
                            logger.info(f"For domain {domain} platform {plat_key}, selector for '{detail}' is None. Skipping.")
                            continue
                        attr = sel_obj.get("attribute", "text")
                        expected_val = sel_obj.get("value", "")
                        try:
                            element = soup.select_one(css)
                        except Exception as e:
                            logger.error(f"Error applying selector {css} in snippet for domain {domain} platform {plat_key}: {e}")
                            snippet_results.append({
                                "detail": detail,
                                "selector": css,
                                "pass": False,
                                "error": f"Error applying selector: {e}"
                            })
                            continue
                        if not element:
                            snippet_results.append({
                                "detail": detail,
                                "selector": css,
                                "pass": False,
                                "error": "Element not found in snippet"
                            })
                        else:
                            if attr.lower() == "text":
                                actual_val = element.get_text(strip=True)
                            else:
                                actual_val = element.get(attr)
                            if detail == "imageUrl" and (attr.lower() != "src" or attr.lower() != "srcset"): 
                                sel_obj["attribute"] = "src"
                                attr = "src"
                                actual_val = element.get(attr)
                            if detail == "imageUrl" or detail == "title":
                                pass_flag = bool(actual_val and len(actual_val) <= 100) # to stop weird cases for images (encoded images)
                            else:
                                pass_flag = bool(actual_val and len(actual_val) <= 50) # to stop weird cases
                                
                            # Check with values from GPT o3 mini
                            pass_flag = bool(
                                (actual_val and isinstance(actual_val, str)) 
                                and (expected_val and isinstance(expected_val, str)) 
                                and (actual_val.lower() == expected_val.lower())
                            )

                            snippet_results.append({
                                "detail": detail,
                                "selector": css,
                                "pass": pass_flag,
                                "expected": expected_val,
                                "actual": actual_val
                            })

                # GPT 4o mini Validation
                meta_results = []
                if gpt_4o_mini_product and html_snippet_gpt_4o:
                    fetched_meta = gpt_4o_mini_product
                    soup_meta = BeautifulSoup(html_snippet_gpt_4o, "html.parser")
                    for detail, sel_obj in selectors_dict.items():
                        if not isinstance(sel_obj, dict):
                            logger.error(f"For domain {domain} platform {plat_key}, selector for '{detail}' is not a dict. Skipping.")
                            continue
                        css = sel_obj.get("selector")
                        if not css:
                            logger.info(f"For domain {domain} platform {plat_key}, selector for '{detail}' is None. Skipping.")
                            continue
                        attr = sel_obj.get("attribute", "text")
                        expected_meta = fetched_meta.get(detail, "")
                        try:
                            element = soup_meta.select_one(css)
                        except Exception as e:
                            logger.error(f"Error applying selector {css} in meta HTML for domain {domain} platform {plat_key}: {e}")
                            meta_results.append({
                                "detail": detail,
                                "selector": css,
                                "pass": False,
                                "error": f"Error applying selector: {e}"
                            })
                            continue
                        if not element:
                            meta_results.append({
                                "detail": detail,
                                "selector": css,
                                "pass": False,
                                "error": "Element not found in meta HTML"
                            })
                        else:
                            if attr.lower() == "text":
                                actual_meta = element.get_text(strip=True)
                            else:
                                actual_meta = element.get(attr)
                            if detail == "imageUrl" and (attr.lower() != "src" or attr.lower() != "srcset"): 
                                sel_obj["attribute"] = "src"
                                attr = "src"
                                actual_meta = element.get(attr)
                            if detail == "imageUrl" or detail == "title":
                                pass_flag = bool(actual_meta and len(actual_meta) <= 100) # to stop weird cases for images (encoded images)
                            else:
                                pass_flag = bool(actual_meta and len(actual_meta) <= 50) # to stop weird cases

                            # Check with values from GPT 4o mini
                            if detail != "imageUrl": 
                                pass_flag = bool(
                                    (actual_meta and isinstance(actual_meta, str)) 
                                    and (expected_meta and isinstance(expected_meta, str)) 
                                    and (actual_meta.lower() == expected_meta.lower())
                                )

                            meta_results.append({
                                "detail": detail,
                                "selector": css,
                                "pass": pass_flag,
                                "expected": expected_meta,
                                "actual": actual_meta
                            })
                
                # Save per-platform validation results
                domain_results[plat_key] = {
                    "gpt_o3": snippet_results,
                    "gpt_4o": meta_results,
                    "failed_scrape_attributes": failed_scrape_attributes
                }
            if domain_results:
                final_results[domain] = domain_results

        store_data_to_jsonl_in_gcs(output_file, [final_results])
        return "Validated selectors and stored results to GCS."
    except Exception as e:
        logger.error(f"Error in validate_generated_selectors: {e}")
        raise
