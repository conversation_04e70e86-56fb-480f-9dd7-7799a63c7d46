from airflow import DAG
from airflow.providers.google.cloud.operators.bigquery import (
    BigQueryCreateEmptyTableOperator,
    BigQueryInsertJobOperator,
)
from airflow.operators.python import PythonOperator
from dependencies.utils.misc import IS_DEV
from google.cloud import bigquery
from dependencies.resale_insights_stats.build_query import (
    build_clean_resale_insights_table_query, 
    build_latency_summary_query,
    build_delete_raw_processed_query
)
from dependencies.resale_insights_stats.build_schema import (
    build_clean_resale_insights_table_schema,
    build_latency_summary_table_schema,
)
from dependencies.resale_insights_stats.constants import (
    PROJECT_ID,
    DATASET_ID,
    CLEAN_TABLE_ID,
    LATENCY_TABLE_ID
)
from datetime import datetime

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 0,
    'email': '<EMAIL>',
}

with DAG(
    dag_id="resale_insights_stats_dag",
    default_args=default_args,
    description="Resale Insights Data flow for Stats",
    schedule="*/15 * * * *" if not IS_DEV() else None,
    start_date=datetime(2025, 5, 27),
    catchup=False,
    tags=["bigquery", "resale_insights", "platform", "latency"],
) as dag:
    
    # ensure clean table of resale insights exists
    create_clean_resale_insights = BigQueryCreateEmptyTableOperator(
        task_id="create_clean_resale_insights_table",
        project_id=PROJECT_ID,
        dataset_id=DATASET_ID,
        table_id=CLEAN_TABLE_ID,
        schema_fields=build_clean_resale_insights_table_schema(),
        time_partitioning={"type": "HOUR", "field": "timestamp"},
        exists_ok=True,
    )

    # udpate clean resale insights table with latest data
    updated_clean_resale_insights = BigQueryInsertJobOperator(
        task_id="update_clean_resale_insights_table",
        configuration={
            "query": {
                "query": build_clean_resale_insights_table_query(),
                "useLegacySql": False,
            }
        },
    )
    
    def delete_processed(**context):
        client = bigquery.Client()
        q = build_delete_raw_processed_query()
        client.query(q).result()

    # delete processed raw data based on stored resaleInsightsRequestId
    cleanup_raw_resale_insights = PythonOperator(
        task_id="delete_raw_processed",
        python_callable=delete_processed,
    )

    # Ensure latency‐P90 table exists
    create_latency = BigQueryCreateEmptyTableOperator(
        task_id="create_latency_table",
        project_id=PROJECT_ID,
        dataset_id=DATASET_ID,
        table_id=LATENCY_TABLE_ID,
        schema_fields=build_latency_summary_table_schema(),
        time_partitioning={"type":"HOUR","field":"timestamp"},
        exists_ok=True,
    )

    # Compute P90 and append into the latency table
    append_latency = BigQueryInsertJobOperator(
        task_id="compute_append_latency",
        configuration={
            "query": {
                "query": build_latency_summary_query(),
                "useLegacySql": False,
            }
        },
    )

    create_clean_resale_insights >> updated_clean_resale_insights
    updated_clean_resale_insights >> cleanup_raw_resale_insights
    updated_clean_resale_insights >> create_latency >> append_latency
