from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.http.sensors.http import HttpSensor
from datetime import datetime
from dependencies.brand_resale_value.utils.constants import (
    BRAND_RESALE_VALUE_CALCULATION_BUCKET,
    BRANDS_AND_CATEGORIES_FILE,
    EXACT_FIRSTHAND_PRODUCTS_BUCKET,
    EXACT_SECONDHAND_PRODUCTS_BUCKET,
    FIRSTHAND_BATCH_CHECK_IDS_BUCKET,
    FIRSTHAND_PRODUCTS_OF_SECONDHAND_BUCKET,
    OPENAI_API_KEY,
    OPENAI_BATCH_ENDPOINT,
    REFINED_QUERY_BATCH_CHECK_IDS_BUCKET,
    SECONDHAND_BATCH_CHECK_IDS_BUCKET,
    SECOND<PERSON>ND_PRODUCTS_OF_BRANDS_BUCKET,
    SECONDHAND_PRODUCTS_OF_FIRSTHAND_BUCKET,
    REFINED_SEARCH_QUERIES_BUCKET,
)
import logging
import os

test_id = "prod-7"


from dependencies.brand_resale_value.fetch_secondhand_from_brands import (
    fetch_secondhand_from_brands_task_group,
)
from dependencies.brand_resale_value.fetch_firsthand_from_secondhand import (
    fetch_firsthand_from_secondhand_task_group,
)
from dependencies.brand_resale_value.find_exact_match_firsthand import (
    find_exact_match_firsthand_products,
)
from dependencies.brand_resale_value.firsthand_batch_status import (
    firsthand_exact_match_batch_response_check,
)
from dependencies.brand_resale_value.collect_firsthand_products import (
    collect_exact_match_firsthand_products,
)
from dependencies.brand_resale_value.fetch_secondhand_from_firsthand import (
    fetch_secondhand_from_firsthand_task_group,
)
from dependencies.brand_resale_value.find_exact_match_secondhand import (
    find_exact_match_secondhand_products,
)
from dependencies.brand_resale_value.secondhand_batch_status import (
    secondhand_exact_match_batch_response_check,
)
from dependencies.brand_resale_value.collect_secondhand_products import (
    collect_exact_match_secondhand_products,
)
from dependencies.brand_resale_value.calculate_brand_resale_value import (
    calculate_brand_resale_value,
)

from dependencies.brand_resale_value.find_refined_search_queries import (
    create_refined_search_queries,
)
from dependencies.brand_resale_value.refined_query_batch_status import (
    refined_query_batch_response_check,
)
from dependencies.brand_resale_value.collect_refined_search_queries import (
    collect_refined_search_queries,
)

logger = logging.getLogger(__name__)

# DAG default arguments
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "email_on_failure": os.getenv("ENVIROMENT") == "prod",
    "email_on_retry": False,
    "retries": 0,
    "email": "<EMAIL>",
}

FILE_NAME = f"/{test_id}.jsonl"
# Create DAG
with DAG(
    "brand_resale_value_pipeline",
    default_args=default_args,
    description="Calculate brand resale value",
    schedule_interval=None,
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["brand resale value"],
) as dag:
    fetch_secondhand_from_brands_task = fetch_secondhand_from_brands_task_group(
        input_gcs_path=BRANDS_AND_CATEGORIES_FILE,
        output_gcs_path=SECONDHAND_PRODUCTS_OF_BRANDS_BUCKET + FILE_NAME,
        batch_size=2,
    )

    find_refined_search_queries_task = PythonOperator(
        task_id="find_refined_search_queries",
        python_callable=create_refined_search_queries,
        op_kwargs={
            "input_gcs_path": SECONDHAND_PRODUCTS_OF_BRANDS_BUCKET + FILE_NAME,
            "output_gcs_path": REFINED_QUERY_BATCH_CHECK_IDS_BUCKET + FILE_NAME,
        },
        do_xcom_push=True,
        dag=dag,
        trigger_rule="none_failed",
    )

    check_refined_search_queries_batch_status_task = HttpSensor(
        task_id="check_refined_search_queries_batch_status",
        http_conn_id="openai_api",
        endpoint=OPENAI_BATCH_ENDPOINT,
        headers={"Authorization": f"Bearer {OPENAI_API_KEY}"},
        method="GET",
        poke_interval=120,
        timeout=25 * 60 * 60,
        response_check=refined_query_batch_response_check,
        dag=dag,
        trigger_rule="none_failed",
    )

    collect_refined_search_queries_task = PythonOperator(
        task_id="collect_refined_search_queries",
        python_callable=collect_refined_search_queries,
        op_kwargs={
            "input_gcs_path": SECONDHAND_PRODUCTS_OF_BRANDS_BUCKET + FILE_NAME,
            "gpt_gcs_path": REFINED_QUERY_BATCH_CHECK_IDS_BUCKET + FILE_NAME,
            "output_gcs_path": REFINED_SEARCH_QUERIES_BUCKET + FILE_NAME,
        },
        dag=dag,
        trigger_rule="none_failed",
    )

    fetch_firsthand_from_secondhand_task = fetch_firsthand_from_secondhand_task_group(
        input_gcs_path=REFINED_SEARCH_QUERIES_BUCKET + FILE_NAME,
        output_gcs_path=FIRSTHAND_PRODUCTS_OF_SECONDHAND_BUCKET + FILE_NAME,
        batch_size=500,  # each batch will get 500 products
    )

    find_exact_match_firsthand_products_task = PythonOperator(
        task_id="find_exact_match_firsthand_products",
        python_callable=find_exact_match_firsthand_products,
        op_kwargs={
            "input_gcs_path": FIRSTHAND_PRODUCTS_OF_SECONDHAND_BUCKET + FILE_NAME,
            "output_gcs_path": FIRSTHAND_BATCH_CHECK_IDS_BUCKET + FILE_NAME,
        },
        # returns the batch id or job id from this method
        do_xcom_push=True,  # Enable XCom pushing
        dag=dag,
        trigger_rule="none_failed",
    )

    check_firsthand_batch_status_task = HttpSensor(
        task_id="check_firsthand_batch_status",
        http_conn_id="openai_api",  # NOTE: Configure this in Airflow connections
        endpoint=OPENAI_BATCH_ENDPOINT,
        headers={"Authorization": f"Bearer {OPENAI_API_KEY}"},
        method="GET",
        poke_interval=120,  # Check every 2 minutes
        timeout=25 * 60 * 60,  # 25 hours, batch has completion window of 24 hours
        response_check=firsthand_exact_match_batch_response_check,
        dag=dag,
        trigger_rule="none_failed",
    )

    # get_firsthand product
    collect_exact_match_firsthand_products_task = PythonOperator(
        task_id="collect_exact_match_firsthand_products",
        python_callable=collect_exact_match_firsthand_products,
        op_kwargs={
            "input_gcs_path": FIRSTHAND_BATCH_CHECK_IDS_BUCKET + FILE_NAME,
            "output_gcs_path": EXACT_FIRSTHAND_PRODUCTS_BUCKET + FILE_NAME,
        },
        dag=dag,
        trigger_rule="none_failed",
    )

    # collect the secondhand product based on the firsthand product
    fetch_secondhand_from_firsthand_task = fetch_secondhand_from_firsthand_task_group(
        input_gcs_path=EXACT_FIRSTHAND_PRODUCTS_BUCKET + FILE_NAME,
        output_gcs_path=SECONDHAND_PRODUCTS_OF_FIRSTHAND_BUCKET + FILE_NAME,
        batch_size=2000,
    )

    find_exact_match_secondhand_products_task = PythonOperator(
        task_id="find_exact_match_secondhand_products",
        python_callable=find_exact_match_secondhand_products,
        op_kwargs={
            "input_gcs_path": SECONDHAND_PRODUCTS_OF_FIRSTHAND_BUCKET + FILE_NAME,
            "output_gcs_path": SECONDHAND_BATCH_CHECK_IDS_BUCKET + FILE_NAME,
        },
        # returns the batch id or job id from this method
        do_xcom_push=True,  # Enable XCom pushing
        dag=dag,
        trigger_rule="none_failed",
    )

    check_secondhand_batch_status_task = HttpSensor(
        task_id="check_secondhand_batch_status",
        http_conn_id="openai_api",  # NOTE: Configure this in Airflow connections
        endpoint=OPENAI_BATCH_ENDPOINT,
        headers={"Authorization": f"Bearer {OPENAI_API_KEY}"},
        method="GET",
        poke_interval=120,  # Check every 2 minutes
        timeout=25 * 60 * 60,  # 25 hours, batch has completion window of 24 hours
        response_check=secondhand_exact_match_batch_response_check,
        dag=dag,
        trigger_rule="none_failed",
    )

    # collect the secondhand product based on the firsthand product
    collect_exact_match_secondhand_products_task = PythonOperator(
        task_id="collect_exact_match_secondhand_products",
        python_callable=collect_exact_match_secondhand_products,
        op_kwargs={
            "input_gcs_path": SECONDHAND_BATCH_CHECK_IDS_BUCKET + FILE_NAME,
            "output_gcs_path": EXACT_SECONDHAND_PRODUCTS_BUCKET + FILE_NAME,
        },
        dag=dag,
        trigger_rule="none_failed",
    )

    calculate_brand_resale_value_task = PythonOperator(
        task_id="calculate_brand_resale_value",
        python_callable=calculate_brand_resale_value,
        op_kwargs={
            "input_gcs_path": EXACT_SECONDHAND_PRODUCTS_BUCKET + FILE_NAME,
            "output_gcs_path": BRAND_RESALE_VALUE_CALCULATION_BUCKET + FILE_NAME,
        },
        dag=dag,
        trigger_rule="none_failed",
    )


(
    fetch_secondhand_from_brands_task
    >> find_refined_search_queries_task
    >> check_refined_search_queries_batch_status_task
    >> collect_refined_search_queries_task
    >> fetch_firsthand_from_secondhand_task
    >> find_exact_match_firsthand_products_task
    >> check_firsthand_batch_status_task
    >> collect_exact_match_firsthand_products_task
    >> fetch_secondhand_from_firsthand_task
    >> find_exact_match_secondhand_products_task
    >> check_secondhand_batch_status_task
    >> collect_exact_match_secondhand_products_task
    >> calculate_brand_resale_value_task
)
