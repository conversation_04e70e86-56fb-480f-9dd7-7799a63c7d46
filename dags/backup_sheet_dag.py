from airflow import DAG
from airflow.operators.python import PythonOperator
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from datetime import datetime
import csv
from google.oauth2 import service_account
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google.auth import default
from google.cloud import storage
from googleapiclient.discovery import build
import logging
import os
from airflow.utils.log.logging_mixin import LoggingMixin

from dependencies.utils.misc import IS_DEV

# Configure logging

logger = logging.getLogger(__name__)

# DAG default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 0,
    'email': '<EMAIL>',
}

# Constants
RANGE = "A:A"

# Check if the environment is development
credentials_path = '/home/<USER>/.config/gcloud/application_default_credentials.json'
# Define necessary scopes for Google Sheets and GCS
SCOPES = [
    'https://www.googleapis.com/auth/spreadsheets.readonly',
    'https://www.googleapis.com/auth/devstorage.read_write',
    'https://www.googleapis.com/auth/drive.readonly'
]


def backup_sheet_to_gcs(sheet_type, spreadsheet_id, data_range='A:A', **kwargs):
    IS_DEV = (os.getenv('ENVIRONMENT') != 'prod')
    logger.info(f"IS_DEV: {IS_DEV}")
    print(f"IS_DEV: {IS_DEV}")
    try:
        # Authenticate with Google Sheets API
        if IS_DEV:
            logger.info(
                f"Using service account credentials from {credentials_path}")
            creds = ServiceAccountCredentials.from_service_account_file(
                credentials_path)
        else:
            logger.info("Using default credentials")
            creds, _ = default(scopes=SCOPES)
        logger.info(f"Creds:******** {creds}")
        print("Service account email:", getattr(creds, "service_account_email", "No service account email available"))


        API_KEY = 'AIzaSyASKJ85LOT9QOlEhzvfaO7hyjPR_J6OH2w'  # Store this securely in a variable or secret

        sheets_service = build("sheets", "v4", developerKey=API_KEY)
        sheet = sheets_service.spreadsheets()
        result = sheet.values().get(spreadsheetId=spreadsheet_id, range=data_range).execute()
        rows = result.get("values", [])
        logger.info(f"Retrieved {len(rows)} rows from {sheet_type} sheet.")
        if not rows:
            logger.warning(f"No data found for {sheet_type} in range {data_range}.")
            return

        # Convert rows to CSV
        csv_content = convert_to_csv(rows)
        logger.info(f"*******CSV content: {csv_content}")
        # Generate filenames
        timestamp = datetime.now().strftime("%Y-%m-%d")
        base_filename = f"{sheet_type}_websites.csv"
        versioned_filename = f"{sheet_type}_websites-{timestamp}.csv"
        bucket_name = f"{sheet_type}-websites"

        # Upload files to GCS
        try:
            upload_to_gcs(bucket_name, base_filename, csv_content)
            logger.info(f"Uploaded {base_filename} to bucket {bucket_name}.")

        except Exception as e:
            return f"Error uploading {base_filename}: {str(e)}", 500

        try:
            upload_to_gcs(bucket_name, versioned_filename, csv_content)
            logger.info(f"Uploaded {versioned_filename} to bucket {bucket_name}.")

        except Exception as e:
            return f"Error uploading {versioned_filename}: {str(e)}", 500

        logger.info(f"Backup for {sheet_type} completed successfully.")
        return "Backup completed successfully.", 200

    except Exception as e:
        return f"Error in backup_sheet_to_gcs: {str(e)}", 500


def convert_to_csv(data):
    """Convert a list of lists to a CSV string."""
    from io import StringIO
    output = StringIO()
    writer = csv.writer(output)
    writer.writerows(data)
    return output.getvalue()

def upload_to_gcs(bucket_name, filename, content):
    """Upload a file to GCS."""
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(filename)
        blob.upload_from_string(content, content_type='text/csv')
        logger.info(f"Uploaded {filename} to bucket {bucket_name}.")
    except Exception as e:
        raise Exception(f"Failed to upload {filename} to GCS: {str(e)}")

with DAG(
    'backup_sheets_to_gcs',
    default_args=default_args,
    description='Backup first-hand,second-hand and brand-domain-alias Google Sheets to GCS',
    schedule='0 * * * *' if not IS_DEV() else None,
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['sheets', 'gcs', 'backup', 'firsthand', 'secondhand', 'brand-domain-alias'],
) as dag:

    backup_firsthand_task = PythonOperator(
        task_id='backup_firsthand_sheet_to_gcs',
        python_callable=backup_sheet_to_gcs,
        op_kwargs={
            'sheet_type': 'firsthand',
            'spreadsheet_id': '1jbXEZeif0hjCfuW4XF492S4IQ4_GBctuwsk5RJ3Dhdc',
            'data_range': 'A:A',
        }
    )

    backup_secondhand_task = PythonOperator(
        task_id='backup_secondhand_sheet_to_gcs',
        python_callable=backup_sheet_to_gcs,
        op_kwargs={
            'sheet_type': 'secondhand',
            'spreadsheet_id': '1xzOB8XqnwdntBsxNDYmjGiGjq8Y7QjHWpt8jucDkoBQ',
            'data_range': 'A:A',
        }
    )

    backup_brand_domain_task = PythonOperator(
        task_id='backup_brand_domain_alia_sheet_to_gcs',
        python_callable=backup_sheet_to_gcs,
        op_kwargs={
            'sheet_type': 'brand-domain-alias',
            'spreadsheet_id': '1aNUzzsQE54sbdTn31AoChmjbZ95fu04SxNMmGUeWz_Y',
            'data_range': 'A:C',
        },
        dag=dag,
    )
