"""
Mixpanel Analytics Pipeline DAG

This DAG fetches analytics data from Mixpanel using the new MixpanelService,
transforms it to normalized schemas, and stores the results in Supabase.

Key Metrics Collected:
1. Site activation metrics (phia_shown/phia_clicked by domain)
2. Permission funnel metrics (enable-phia → almost-finished conversion)
3. Safari extension metrics (IOS_SAFARI_EXTENSION platform filter)
4. Weekly active users (heartbeat events with OS breakdown)
5. Weekly retention metrics (cohort analysis)

Schedule: Daily at 3 AM UTC
Catchup: False (only process current data)
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import logging

# Import our custom functions
from dependencies.mixpanel.mixpanel_tasks import (
    fetch_mixpanel_analytics_task,
    transform_mixpanel_data_task,
    prepare_supabase_data_task,
)
from dependencies.transaction_reports.supabase_upload import create_upload_task_function
from dependencies.utils.misc import IS_DEV

logger = logging.getLogger(__name__)

# Configuration Constants
PROJECT_ID = "phia-prod-416420"
MIXPANEL_TABLE = "mixpanel_events"

# DAG Configuration
default_args = {
    "owner": "data-team",
    "depends_on_past": False,
    "start_date": datetime(2024, 1, 1),
    "email_on_failure": True if not IS_DEV() else False,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
    "email": ["<EMAIL>"],
}

# Create the DAG
dag = DAG(
    "mixpanel_events_pipeline",
    default_args=default_args,
    description="Fetch and process Mixpanel events data",
    schedule_interval="0 3 * * *" if not IS_DEV() else None,  # Daily at 3 AM UTC
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["mixpanel", "events", "analytics", "daily"],
    max_active_runs=1,  # Prevent overlapping runs
)

# Task 1: Fetch comprehensive analytics data from Mixpanel API using new MixpanelService
fetch_analytics = PythonOperator(
    task_id="fetch_mixpanel_analytics",
    python_callable=fetch_mixpanel_analytics_task,
    dag=dag,
    doc_md="""
    ## Fetch Mixpanel Analytics Data with New MixpanelService

    This task fetches the 5 key analytics metrics using the new MixpanelService.

    **What it does:**
    - Connects to Mixpanel API using service account credentials
    - Fetches 5 key metrics for the last 30 days
    - Uses production-level error handling and retry logic
    - Respects rate limits (60 queries/hour)
    - Returns structured analytics data

    **Key Metrics Fetched:**
    1. Site activation metrics (phia_shown/phia_clicked by domain)
    2. Permission funnel metrics (enable-phia → almost-finished conversion)
    3. Safari extension metrics (IOS_SAFARI_EXTENSION platform filter)
    4. Weekly active users (heartbeat events with OS breakdown)
    5. Weekly retention metrics (cohort analysis)

    **Output:**
    - Structured analytics data for all 5 metrics
    - Service health information
    - Processing statistics
    """,
)

# Task 2: Transform MixpanelService data to normalized Supabase schemas
transform_data = PythonOperator(
    task_id="transform_mixpanel_data",
    python_callable=transform_mixpanel_data_task,
    dag=dag,
    doc_md="""
    ## Transform MixpanelService Data

    This task transforms the 5 key metrics from MixpanelService to normalized Supabase schemas.

    **What it does:**
    - Takes structured analytics data from MixpanelService
    - Transforms each metric type to appropriate Supabase table schema
    - Validates data quality for each table
    - Handles data type conversions and cleaning
    - Generates transformation statistics

    **Tables Created:**
    - site_activation: Site activation rates by domain
    - onboarding_funnel: Permission funnel conversion metrics
    - safari_extension_metrics: Safari extension specific metrics
    - daily_metrics: Weekly active users data
    - cohort_retention: Weekly retention analysis

    **Output:**
    - Normalized data for each Supabase table
    - Validation results and statistics
    - Processing time and record counts
    """,
)

# Task 3: Prepare data for Supabase upload
prepare_supabase = PythonOperator(
    task_id="prepare_supabase_data",
    python_callable=prepare_supabase_data_task,
    dag=dag,
    doc_md="""
    ## Prepare Data for Supabase Upload

    This task prepares transformed data for batch upload to Supabase.

    **What it does:**
    - Takes transformed data from previous task
    - Cleans and validates data for Supabase compatibility
    - Removes null values and invalid records
    - Formats data for batch upload
    - Generates upload statistics

    **Output:**
    - Clean data ready for Supabase upload
    - Upload statistics per table
    - Final record counts
    - Data validation results
    """,
)

# Task 4: Upload to Supabase
upload_to_supabase = PythonOperator(
    task_id="upload_mixpanel_to_supabase",
    python_callable=create_upload_task_function("mixpanel"),
    dag=dag,
    doc_md="""
    ## Upload Mixpanel Analytics Data to Supabase

    This task uploads the prepared analytics data to multiple Supabase tables.

    **What it does:**
    - Takes prepared data from previous task
    - Uploads to multiple Supabase tables for the 5 key metrics
    - Handles batch uploads with conflict resolution
    - Manages upserts for existing records
    - Provides comprehensive upload statistics

    **Tables Updated:**
    - site_activation: Site activation rates by domain
    - onboarding_funnel: Permission funnel conversion metrics
    - safari_extension_metrics: Safari extension specific metrics
    - daily_metrics: Weekly active users data
    - cohort_retention: Weekly retention analysis

    **Output:**
    - Upload success/failure status per table
    - Record counts uploaded
    - Error details if any failures occur
    - Database connection status
    """,
)

# Define task dependencies
fetch_analytics >> transform_data >> prepare_supabase >> upload_to_supabase

# Add comprehensive DAG documentation
dag.doc_md = """
# Mixpanel Analytics Pipeline DAG

This DAG processes analytics data from Mixpanel using the new MixpanelService.

## Overview

This pipeline fetches the 5 key analytics metrics from Mixpanel, transforms them
to normalized Supabase schemas, and stores them for dashboard consumption.

## Key Metrics Collected

1. **Site Activation Metrics**: phia_shown/phia_clicked conversion rates by domain
2. **Permission Funnel Metrics**: enable-phia → almost-finished conversion rates
3. **Safari Extension Metrics**: IOS_SAFARI_EXTENSION platform specific data
4. **Weekly Active Users**: heartbeat events with OS breakdown
5. **Weekly Retention Metrics**: cohort analysis for user retention

## Data Flow

1. **Fetch**: Get analytics data from Mixpanel using MixpanelService (last 30 days)
2. **Transform**: Convert 5 key metrics to normalized Supabase schemas
3. **Prepare**: Clean and validate data for Supabase upload
4. **Upload**: Store normalized data in Supabase for dashboard access

## Schedule

- **Frequency**: Daily at 3 AM UTC
- **Catchup**: Disabled (only processes current data)
- **Retries**: 2 attempts with 5-minute delays

## Monitoring

- Email notifications <NAME_EMAIL> on failures
- Task logs available in Airflow UI
- Data quality metrics logged for each run

## Dependencies

- Mixpanel API credentials stored in environment variables
- Supabase database connection and credentials
- Network connectivity to Mixpanel API endpoints

## Database Tables

The pipeline populates these Supabase tables:
- **site_activation**: Site activation rates by domain
- **onboarding_funnel**: Permission funnel conversion metrics
- **safari_extension_metrics**: Safari extension specific metrics
- **daily_metrics**: Weekly active users data
- **cohort_retention**: Weekly retention analysis

## Troubleshooting

Common issues and solutions:
- **Authentication failures**: Check Mixpanel service account credentials
- **Network timeouts**: Verify connectivity to mixpanel.com API endpoints
- **Data validation errors**: Check MixpanelService response format
- **Database failures**: Verify Supabase connection and table schemas
"""

if __name__ == "__main__":
    dag.test()
