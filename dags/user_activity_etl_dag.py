from __future__ import annotations

import pendulum
import datetime
import os
import sys
import google.auth
import logging

from dependencies.utils.misc import IS_DEV

try:
    credentials, project_id = google.auth.default()
    print(f"Successfully loaded default credentials. Project ID: {project_id}")
    if hasattr(credentials, 'service_account_email'):
        print(f"Credentials are for service account: {credentials.service_account_email}")
    else:
        print(f"Credentials type: {type(credentials)}")
except Exception as e:
    logging.error(f"Could not load default credentials: {e}")

# --- Path Setup for Protobuf Schemas ---
_current_script_dir = os.path.dirname(os.path.abspath(__file__))
_dependencies_dir = os.path.join(_current_script_dir, "dependencies")
_generated_schema_dir = os.path.join(_dependencies_dir, "generated_schema")
_generated_python_dir = os.path.join(_generated_schema_dir, "python")

# --- Function to Download Schema Files if Missing ---
def _ensure_schema_files_are_present():
    """Checks for local schema files and downloads them from GCS if missing."""
    key_schema_file1 = os.path.join(_generated_python_dir, "log_data_stream_pb2.py")
    key_schema_file2 = os.path.join(_generated_python_dir, "common_pb2.py")

    if os.path.exists(key_schema_file1) and os.path.exists(key_schema_file2):
        print(f"Protobuf schema files found locally in {_generated_python_dir}. Skipping download.")
        return

    print(f"Protobuf schema files not found in {_generated_python_dir}. Attempting download from GCS...")
    
    # Ensure target directories exist
    os.makedirs(_generated_python_dir, exist_ok=True)
    # No need to create _generated_schema_dir separately if _generated_python_dir creation handles parents.

    try:
        from google.cloud import storage # Import client here to make it conditional
        
        client = storage.Client() # Assumes GOOGLE_APPLICATION_CREDENTIALS is set or worker has rights
        bucket = client.bucket("phia-schema")
        gcs_prefix = "generated_schema/python/"
        blobs = bucket.list_blobs(prefix=gcs_prefix)
        
        files_downloaded = 0
        for blob in blobs:
            if not blob.name.endswith('/'):  # Skip directory pseudo-folders
                relative_path = blob.name.replace(gcs_prefix, "")
                if not relative_path: # Skip the prefix folder itself if listed
                    continue
                target_file_path = os.path.join(_generated_python_dir, relative_path)
                
                # Ensure sub-directory for the file exists (if any)
                os.makedirs(os.path.dirname(target_file_path), exist_ok=True)
                
                blob.download_to_filename(target_file_path)
                files_downloaded += 1
                print(f"Downloaded: {target_file_path}")
        
        if files_downloaded == 0:
            print(f"Warning: No schema files found in GCS bucket 'phia-schema' under prefix '{gcs_prefix}'. Protobuf imports might fail.", file=sys.stderr)
            return # Allow DAG parsing to continue and fail on import if truly missing

        # Ensure __init__.py exists in generated_schema/python/
        init_python_path = os.path.join(_generated_python_dir, "__init__.py")
        if not os.path.exists(init_python_path):
            with open(init_python_path, 'w') as f:
                f.write("# Auto-generated by Airflow DAG: Ensures package importability\n")
            print(f"Created: {init_python_path}")

        # Ensure __init__.py exists in generated_schema/
        init_generated_schema_path = os.path.join(_generated_schema_dir, "__init__.py")
        if not os.path.exists(init_generated_schema_path):
            with open(init_generated_schema_path, 'w') as f:
                f.write("# Auto-generated by Airflow DAG: Ensures package importability\n")
            print(f"Created: {init_generated_schema_path}")
        
        print(f"Successfully downloaded {files_downloaded} schema files to {_generated_python_dir}.")

        # Add the parent of 'generated_schema' (i.e., _dependencies_dir) to sys.path
        # so 'from generated_schema.python import ...' works.
        if _dependencies_dir not in sys.path:
            sys.path.insert(0, _dependencies_dir) # Insert at the beginning to prioritize
            print(f"Added {_dependencies_dir} to sys.path for schema imports.")

    except ImportError:
        raise ImportError("'google-cloud-storage' library not found. Cannot download schemas.")
    except Exception as e:
        raise Exception(f"Error during schema download from GCS: {str(e)}")

# --- Execute Schema Download Check ---
_ensure_schema_files_are_present() # Attempt to download schemas if not present

# --- Adjust sys.path for Protobuf Imports ---
if _generated_python_dir not in sys.path:
    sys.path.insert(0, _generated_python_dir)

# --- Import Protobuf Modules (with error handling) ---
try:
    from dependencies.generated_schema.python import log_data_stream_pb2
    from dependencies.generated_schema.python import common_pb2
    PROTOBUF_AVAILABLE = True
except ImportError as e:
    print(f"Critical Error: Could not import protobuf generated files from {_generated_python_dir} after check/download attempt. Error: {e}", file=sys.stderr)
    print("Please ensure the path is correct and files exist. DAG may not function correctly.", file=sys.stderr)
    raise e

# --- Standard Library & Third-Party Imports for DAG Logic ---
import pandas as pd
import pyarrow as pa

from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

# --- Import New Callable Modules ---
from dependencies.user_activity_etl_dag.user_activity_etl_dag_fetch import fetch_gcp_logs_task
from dependencies.user_activity_etl_dag.user_activity_etl_dag_transform import transform_logs_task as transform_logs_callable
from dependencies.user_activity_etl_dag.user_activity_etl_dag_upload import upload_parquet_to_gcs_task
from dependencies.user_activity_etl_dag.user_activity_etl_dag_verify import verify_gcs_parquet_task

# --- Configuration (copied from process_user_activity.py) ---
PROJECT_ID = os.environ.get('GOOGLE_CLOUD_PROJECT', 'phia-prod-416420')
LOG_FILTER_PREFIX = "LogUserActivity:"
GCS_BUCKET_NAME = "phia-activity-tracking"

PARQUET_SCHEMA = pa.schema([
    pa.field('timestamp', pa.timestamp('us', tz='UTC')),
    pa.field('os', pa.int32()),
    pa.field('device_type', pa.int32()),
    pa.field('phia_id', pa.string()),
    pa.field('ip', pa.string()),
    pa.field('platform', pa.int32()),
    pa.field('url', pa.string()),
    pa.field('page_type', pa.int32()),
    pa.field('module', pa.int32()),
    pa.field('action', pa.int32()),
    pa.field('object_type', pa.int32()),
    pa.field('object_value', pa.string()),
    pa.field('action_id', pa.string())
])

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': pendulum.duration(minutes=5),
    'gcp_conn_id': 'google_cloud_default', # Example: specify GCP connection if needed explicitly for some operators/hooks
}

DAG_ID = "user_activity_data_etl"

with DAG(
    dag_id=DAG_ID,
    default_args=default_args,
    description='Self-contained DAG for User Activity ETL: fetches, processes, stores to GCS, and verifies.',
    schedule='@daily' if not IS_DEV() else None,
    start_date=pendulum.datetime(2024, 1, 1, tz="UTC"),
    catchup=False,
    tags=['data-pipeline', 'gcp', 'user-activity'],
    doc_md="""
    ### User Activity Data ETL DAG
    This DAG extracts user activity logs from Google Cloud Logging, transforms them,
    and stores them as Parquet files in Google Cloud Storage.
    It relies on protobuf definitions stored in `dependencies/generated_schema`.
    **Important:** The `PROTOBUF_AVAILABLE` flag must be true for transformation to succeed.
    The `PROJECT_ID` and `GCS_BUCKET_NAME` are critical configurations.
    """
) as dag:
    target_date_template = "{{ data_interval_start.strftime('%Y-%m-%d') }}"

    fetch_logs_task = PythonOperator(
        task_id='fetch_gcp_logs',
        python_callable=fetch_gcp_logs_task,
        op_kwargs={
            'target_date_str': target_date_template,
            'gcp_project_id': PROJECT_ID, 
            'gcp_log_filter_prefix': LOG_FILTER_PREFIX
        },
        doc_md="""
        #### Fetch GCP Logs Task
        Fetches raw logs from Google Cloud Logging for the target date.
        Logs are passed via XCom.
        Filter Prefix: `{LOG_FILTER_PREFIX}`
        Target Project: `{PROJECT_ID}`
        """
    )

    transform_logs_task_object = PythonOperator(
        task_id='transform_raw_logs',
        python_callable=transform_logs_callable,
        op_kwargs={
            'raw_logs': fetch_logs_task.output,
            'parquet_schema_fields': PARQUET_SCHEMA.names, 
            'protobuf_available_flag': PROTOBUF_AVAILABLE
        },
        doc_md="""
        #### Transform Raw Logs Task
        Transforms raw log data into a structure matching PARQUET_SCHEMA.
        Transformed logs are passed via XCom.
        Requires Protobuf schemas to be available.
        """
    )

    upload_parquet_task = PythonOperator(
        task_id='upload_parquet_to_gcs',
        python_callable=upload_parquet_to_gcs_task,
        op_kwargs={
            'transformed_logs': transform_logs_task_object.output, 
            'target_date_str': target_date_template,
            'gcs_bucket_name_config': GCS_BUCKET_NAME,
            'parquet_schema_config': PARQUET_SCHEMA
        },
        doc_md="""
        #### Upload Parquet to GCS Task
        Writes transformed data to potentially multiple Parquet files (partitioned by row count) in a GCS directory.
        The GCS directory path is passed via XCom.
        Target GCS Bucket: `{GCS_BUCKET_NAME}`
        Output Path: `gs://{GCS_BUCKET_NAME}/{{{{ data_interval_start.strftime('%Y-%m-%d') }}}}/user_activity.parquet`
        Output Directory Example: `gs://{GCS_BUCKET_NAME}/{{{{ data_interval_start.strftime('%Y-%m-%d') }}}}/` (containing `user_activity_part_*.parquet` files)
        """
    )

    verify_parquet_task = PythonOperator(
        task_id='verify_gcs_parquet_file',
        python_callable=verify_gcs_parquet_task,
        op_kwargs={
            'gcs_directory_path': upload_parquet_task.output, 
            'parquet_schema_config': PARQUET_SCHEMA
            },
        doc_md="""
        #### Verify GCS Parquet Files Task
        Reads Parquet files from the specified GCS directory to verify their integrity and content.
        """
    )

    fetch_logs_task >> transform_logs_task_object >> upload_parquet_task >> verify_parquet_task
