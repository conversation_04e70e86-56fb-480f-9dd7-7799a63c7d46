from datetime import datetime
from airflow import DAG
from airflow.operators.python import PythonOperator

from dependencies.auto_selector.utils.constants import (
    MAP_PRODUCTS_OUTPUT, 
    FILTERED_MAPPING_OUTPUT,
    GENERATED_SELECTORS_OUTPUT,
    CLEANED_SELECTORS_OUTPUT,
    VALIDATION_RESULTS_OUTPUT, 
    FINAL_RESULTS_OUTPUT, 
)
from dependencies.auto_selector.fetch_and_map_products import fetch_and_map_products
from dependencies.auto_selector.filter_product_urls import filter_product_urls_by_zyte
from dependencies.auto_selector.generate_selectors import generate_selectors
from dependencies.auto_selector.validate_generated_selectors import validate_generated_selectors
from dependencies.auto_selector.push_selectors import push_selectors_to_bigtable
from dependencies.auto_selector.clean_selectors import clean_selectors
from dependencies.utils.misc import IS_DEV

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'email': '<EMAIL>',
}

with DAG(
    'auto_selectors_dag',
    default_args=default_args,
    description='Automatically map, filter, and generate CSS selectors using AI for product pages using GCS for intermediate storage',
    schedule='@daily' if not IS_DEV() else None,
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['auto_selectors', 'AI', 'scraping']
) as dag:

    task_map_products = PythonOperator(
        task_id='fetch_and_map_products',
        python_callable=fetch_and_map_products,
        op_kwargs={
            "input_file": None,  # No input file needed for the first task, we use BigQuery directly
            "output_file": MAP_PRODUCTS_OUTPUT
        },
        provide_context=True
    )

    task_filter_products = PythonOperator(
        task_id='filter_product_urls_via_zyte',
        python_callable=filter_product_urls_by_zyte,
        op_kwargs={
            "input_file": MAP_PRODUCTS_OUTPUT,
            "output_file": FILTERED_MAPPING_OUTPUT
        },
        provide_context=True
    )

    task_generate_selectors = PythonOperator(
        task_id='generate_selectors',
        python_callable=generate_selectors,
        op_kwargs={
            "input_file": FILTERED_MAPPING_OUTPUT,
            "output_file": GENERATED_SELECTORS_OUTPUT
        },
        provide_context=True
    )
    
    task_clean_selectors = PythonOperator(
        task_id='clean_selectors',
        python_callable=clean_selectors,
        op_kwargs={
            "input_file": GENERATED_SELECTORS_OUTPUT,
            "output_file": CLEANED_SELECTORS_OUTPUT
        },
    )

    task_validate_selectors = PythonOperator(
        task_id='validate_selectors',
        python_callable=validate_generated_selectors,
        op_kwargs={
            "input_file": CLEANED_SELECTORS_OUTPUT,
            "output_file": VALIDATION_RESULTS_OUTPUT
        },
        provide_context=True
    )

    task_push_selectors = PythonOperator(
        task_id='push_selectors_to_db',
        python_callable=push_selectors_to_bigtable,
        op_kwargs={
            "input_file": VALIDATION_RESULTS_OUTPUT,
            "output_file": FINAL_RESULTS_OUTPUT
        },
        provide_context=True
    )

    task_map_products >> task_filter_products >> task_generate_selectors >> task_clean_selectors >> task_validate_selectors >> task_push_selectors
