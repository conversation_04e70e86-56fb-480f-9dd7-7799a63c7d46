from airflow import DAG
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import requests
import logging
from dependencies.detect_google_shopping_changes.google_shopping_native_search import GoogleShoppingNativeSearch
from dependencies.detect_google_shopping_changes.image_match import check_image_match
import asyncio
from fuzzywuzzy import fuzz
from dependencies.utils.misc import IS_DEV

logger = logging.getLogger(__name__)

SLACK_WEBHOOK_URL = "*********************************************************************************"

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 0,
    "email": "<EMAIL>",
}



def send_slack_alert(message, fields=None):
    """
    Sends a generic Slack alert using attachments with optional fields.
    """
    color = "#36a64f"  # default green (success)
    if "❌" in message:
        color = "#ff0000"  # red for errors

    payload = {
        "attachments": [
            {
                "color": color,
                "blocks": [
                    {
                        "type": "section",
                        "text": {"type": "mrkdwn", "text": message}
                    }
                ]
            }
        ]
    }
    if fields:
        payload["attachments"][0]["blocks"].append({
            "type": "section",
            "fields": fields
        })
    try:
        response = requests.post(SLACK_WEBHOOK_URL, json=payload)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to send Slack notification: {e}")


def format_product_blocks(product, label):
    """
    Formats product details into a list of Slack blocks, including a section for text details
    and an image block to display the product image.
    """
    name = product.get("name", "N/A")
    price = product.get("priceUsd", "N/A")
    product_url = product.get("productUrl", "N/A")
    text = f"*{label}*\n*Name:* {name}\n*Price:* {price}\n*Link:* <{product_url}|View Product>"
    blocks = [
        {
            "type": "section",
            "text": {"type": "mrkdwn", "text": text}
        }
    ]
    image_url = product.get("imgUrl")
    if image_url:
        blocks.append({
            "type": "image",
            "image_url": image_url,
            "alt_text": f"{name} image"
        })
    return blocks


def send_slack_product_comparison_alert(product, google_product, base_url):
    """
    Sends a Slack alert for cross validation failure.
    It includes separate blocks for the Google Shopping product and the Google Product page product,
    including image blocks for each.
    """
    message = f"❌ Validation failed (Cross Validation failed) in {base_url}"
    blocks = [
        {
            "type": "header",
            "text": {"type": "plain_text", "text": message, "emoji": True}
        },
        {"type": "divider"}
    ]
    # Append blocks for Google Shopping Product
    blocks.extend(format_product_blocks(product, "Google Shopping Product"))
    blocks.append({"type": "divider"})
    # Append blocks for Google Product Page Product
    blocks.extend(format_product_blocks(google_product, "Google Product Page Product"))
    
    payload = {"blocks": blocks}
    try:
        response = requests.post(SLACK_WEBHOOK_URL, json=payload)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to send Slack notification: {e}")


def send_slack_product_page_error_alert(product, missing_fields, product_url, error_prefix=None):
    """
    Sends a Slack alert for errors encountered during Google Product Page scraping.
    It displays the missing fields and includes blocks with the product details and its image.
    """
    if error_prefix:
        message = f"❌ {error_prefix}: Missing fields {missing_fields}"
    else:
        message = f"❌ Validation failed (Scraping failed in Google Product Page): Missing fields {missing_fields}"
    blocks = [
        {
            "type": "header",
            "text": {"type": "plain_text", "text": message, "emoji": True}
        },
        {"type": "divider"}
    ]
    blocks.extend(format_product_blocks(product, "Google Shopping Product"))
    blocks.append({"type": "divider"})
    blocks.append({
        "type": "section",
        "text": {"type": "mrkdwn", "text": f"*Product URL:* <{product_url}|View Product>"}
    })
    
    payload = {"blocks": blocks}
    try:
        response = requests.post(SLACK_WEBHOOK_URL, json=payload)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to send Slack notification: {e}")


def cross_validation_check(product, google_product):
    """
    Cross Validation Check between Product from Google Shopping page and Google Product.
    """
    if not fuzz.partial_ratio(product["name"].lower(), google_product["name"].lower()) > 60: # Because most of the times the name is not exactly the same but they are similar
        logger.info(f"Product Name Mismatch: {fuzz.partial_ratio(product['name'].lower(), google_product['name'].lower())}")
        return False
    if not check_image_match(product["imgUrl"], google_product["imgUrl"]) < 50:
        logger.info(f"Product Image Mismatch: {check_image_match(product['imgUrl'], google_product['imgUrl'])}")
        return False
    if product["priceUsd"] != google_product["priceUsd"]:
        return False
    return True


def validate_google_shopping_extraction():
    search_instance = GoogleShoppingNativeSearch()
    query = "used nike shoes"
    base_url, html, extracted_images, products = asyncio.run(search_instance.search(query))
    
    if html: 
        # Validation 0: Check if products were extracted    
        if len(products) == 0:
            send_slack_alert(f"❌ Validation failed (Scraping failed in Google Shopping Page): No products found in <{base_url}|Google Shopping Page>")
            return
        
        # Validation 1: Check if extractAndMapDimgImages has exactly the expected number of images
        if len(extracted_images) != len(products):
            send_slack_alert(f"❌ Validation failed (Image failed to map in Google Shopping Page): Extracted Images does not contain the same number of images as products in <{base_url}|Google Shopping Page>")
            return

        # Validation 2: Ensure every product contains required fields
        for product in products:
            required_fields = ["name", "priceUsd", "imgUrl", "id", "productUrl", "secondhandRetailerId"]
            missing_fields = [field for field in required_fields if not product.get(field)]
            if missing_fields:
                send_slack_alert(f"❌ Validation failed (Scraping failed in Google Shopping Page): Missing fields {missing_fields} in <{base_url}|Google Shopping Page>")
                return

        # Validation 3: Check Google Product Page Scraper for at least one product
        """
            if we have 0 missing fields:
                - then we can cross validate the product with google product
            if we have less than 4 missing fields and more than 0 missing fields: 
                - then we can declare that we are processing a valid google product URL and declare that Google UI is changed
            if we have missing fields equal to 4:
                - then we can declare that we are processing a broken google product URL and continue processing next consecutive product
                - after checking consecutive 3 products: 
                    - if we still have missing fields < 4, then we can declare that Google UI is changed
                    - if we still have missing fields = 4, then we can declare that we are processing consecutive repeated broken google product URLs
        
            NOTE: We check for consecutive 3 Google product URLs as we might get broken URLs in between valid URLs
        """
        max_cnt = 3
        cnt = 0
        if products:
            while True:
                product_to_check = products[cnt]
                html, google_product = asyncio.run(search_instance.get_google_product_by_id(
                    product_to_check["secondhandRetailerId"],
                    product_to_check["productUrl"]
                ))
                missing_fields_gp = 0
                if html: 
                    required_fields_gp = ["name", "imgUrl", "priceUsd", "productUrl"]
                    missing_fields_gp = [field for field in required_fields_gp if not google_product.get(field)]
                    if len(missing_fields_gp) == 0: 
                        # Validation 4: Cross Validation Check
                        if not cross_validation_check(product_to_check, google_product):
                            send_slack_product_comparison_alert(product_to_check, google_product, base_url)
                            return
                        else:
                            break
                    # Validation 5: Check if Google Product Page Scraper has missing fields
                    elif 0 < len(missing_fields_gp) < 4:
                        send_slack_product_page_error_alert(product_to_check, missing_fields_gp, product_to_check["productUrl"])
                        return
                    
                cnt += 1
                # Validation 6: Check if Google Product Page Scraper has missing fields for 3 consecutive products
                if cnt == max_cnt: 
                    send_slack_product_page_error_alert(
                        product_to_check,
                        missing_fields_gp,
                        google_product["productUrl"],
                        error_prefix="Processed three broken product URLs or Scraping failed in Google Product Page"
                    )
                    return
        send_slack_alert(f"✅ Validation passed in both Google Shopping Page and Google Product Page in <{base_url}|Google Shopping Page>")


with DAG(
    "validate_google_shopping_extraction",
    default_args=default_args,
    description="Validates Google Shopping Search Extraction",
    schedule="*/30 * * * *" if not IS_DEV() else None,
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["google_shopping", "validation"],
) as dag:

    validation_task = PythonOperator(
        task_id="validate_google_shopping_extraction",
        python_callable=validate_google_shopping_extraction,
        dag=dag
    )
