from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from datetime import datetime
from urllib.parse import urlparse
import json
from google.oauth2.credentials import Credentials as UserCredentials
from google.auth import default
from google.cloud import bigquery
import gspread
from google.auth.transport.requests import Request
import logging

from dependencies.utils.misc import IS_DEV

# Configure logging
logger = logging.getLogger(__name__)

# DAG default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 0,
    'email': '<EMAIL>',
}

# Configuration for Drive folders
FOLDER_VIEWS_ID = '18AktGsla8w2THIkoU8CUdvVjBDzvh780'
FOLDER_RESALE_ID = '1UI7TpeJlw5b6J3CgMgUXk8QaTYoKt1WF'

# USER_ADC_PATH = '/home/<USER>/.config/gcloud/application_default_credentials.json'

# Mapping for DealType enum values
DEAL_TYPE_MAP = {
    '0': 'BETTER_DEALS',
    '1': 'GOOD_FIND',
    '2': 'GREAT_DEAL',
    '3': 'RARE_FIND',
    '4': 'SIMILAR_DEALS',
    '5': 'SUPERIOR_DEAL'
}

def load_user_credentials():
    scopes = [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/cloud-platform',
    ]
    creds, _ = default(scopes=scopes)
    if creds.expired and creds.refresh_token:
        creds.refresh(Request())
    return creds

def extract_domain(url):
    try:
        return urlparse(url).netloc
    except Exception:
        return ''

def export_top_views_with_insights(**kwargs):
    creds = load_user_credentials()
    bq = bigquery.Client(credentials=creds, project='phia-prod-416420')
    gc = gspread.authorize(creds)

    query = f"""
    SELECT url, COUNT(*) AS view_count
    FROM `phia-prod-416420.mixpanel.extension_page_view`
    WHERE is_product_page = TRUE
      AND _PARTITIONTIME >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
    GROUP BY url
    ORDER BY view_count DESC
    LIMIT 100
    """
    rows = bq.query(query).result()

    header = [
        'ProductURL', 'Impressions',
    ]
    output = [header]

    for r in rows:
      url = r['url']
      count = r['view_count']
      row = [
          url, count,
      ]
      output.append(row)

    title = f"Top_100_Views_{datetime.utcnow().date().isoformat()}"
    sheet = gc.create(title, folder_id=FOLDER_VIEWS_ID)
    sheet.share('phia.com', perm_type='domain', role='writer')
    ws = sheet.get_worksheet(0)
    ws.update(output, value_input_option='USER_ENTERED')  # type: ignore
    logger.info(f"Created sheet '{title}' with {len(output)-1} records in folder {FOLDER_VIEWS_ID}")


def export_top_resale_insights(**kwargs):
    creds = load_user_credentials()
    bq = bigquery.Client(credentials=creds, project='phia-prod-416420')
    gc = gspread.authorize(creds)

    query = f"""
    SELECT productUrl, COUNT(*) AS view_count,
           topProducts, dealType,
           scrapedBrand, scrapedPrice, scrapedName
    FROM `phia-prod-416420.phia_api_prod.public_ResaleInsightsQuery`
    WHERE topProducts IS NOT NULL
      AND createdAt >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
    GROUP BY productUrl, topProducts, dealType,
             scrapedBrand, scrapedPrice, scrapedName
    ORDER BY view_count DESC
    LIMIT 100
    """
    rows = bq.query(query).result()

    header = [
        'ProductURL', 'Impressions',
        'scrapedBrand', 'scrapedPrice', 'scrapedName',
        'ProductCategory', 'ProductDomain', 'DealType',
        'top1Link', 'top1ExactMatch', 'top1ColorMatch', 'top1MaterialMatch',
        'top2Link', 'top2ExactMatch', 'top2ColorMatch', 'top2MaterialMatch',
        'top3Link', 'top3ExactMatch', 'top3ColorMatch', 'top3MaterialMatch',
        'Notes'
    ]
    output = [header]

    for ins in rows:
        url = ins.get('productUrl')
        count = ins.get('view_count')
        base = [
            url, count,
            ins.get('scrapedBrand',''),
            ins.get('scrapedPrice',''),
            ins.get('scrapedName',''),
            '', extract_domain(url)
        ]
        deal_str = DEAL_TYPE_MAP.get(str(ins.get('dealType','')),'')
        row = base + [deal_str]
        tp_list = []
        try:
            tp_list = json.loads(ins.get('topProducts') or '[]')
        except:
            tp_list = []
        for i in range(3):
            raw = tp_list[i].get('productUrl','') if i < len(tp_list) and isinstance(tp_list[i], dict) else ''
            link_formula = f'=HYPERLINK("{raw}", "link")' if raw else ''
            row.extend([link_formula, '', '', ''])
        row.append('')
        output.append(row)

    title = f"Top_100_Resale_{datetime.utcnow().date().isoformat()}"
    sheet = gc.create(title, folder_id=FOLDER_RESALE_ID)
    sheet.share('phia.com', perm_type='domain', role='writer')
    ws = sheet.get_worksheet(0)
    ws.update(output, value_input_option='USER_ENTERED')  # type: ignore
    logger.info(f"Created sheet '{title}' with {len(output)-1} records in folder {FOLDER_RESALE_ID}")


with DAG(
    'weekly_impression_sheets_to_drive',
    default_args=default_args,
    description="Weekly export of last week's top product views with insights and resale insights into Google Sheets folders",
    schedule='@weekly' if not IS_DEV() else None,
    start_date=datetime(2025, 5, 1),
    catchup=False,
    tags=['bigquery','sheets','resale','views'],
) as dag:

    task1 = PythonOperator(
        task_id='export_top_views_with_insights',
        python_callable=export_top_views_with_insights
    )

    task2 = PythonOperator(
        task_id='export_top_resale_insights',
        python_callable=export_top_resale_insights
    )