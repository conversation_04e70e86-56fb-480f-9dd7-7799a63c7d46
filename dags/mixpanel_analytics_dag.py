"""
Airflow DAG for Phia Mixpanel Analytics Pipeline.

This DAG orchestrates the collection of analytics data from Mixpanel
for the Phia extension and mobile app dashboard.

Metrics Collected:
1. New site activation rate broken down by site
2. Enabled permissions % (first time run)
3. Weekly active Safari extension users
4. Weekly active users since launch
5. Weekly retention metrics

The pipeline runs daily and stores results in Supabase for dashboard consumption.

Author: Phia Data Team
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import json
import os

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.exceptions import AirflowException

# Import our custom Mixpanel service
import sys
sys.path.append('/Users/<USER>/Documents/GitHub/phia/data-pipeline')

from dags.dependencies.mixpanel.mixpanel_service import MixpanelService

# Configure logging
logger = logging.getLogger(__name__)

# DAG configuration
DEFAULT_ARGS = {
    'owner': 'phia-data-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 6, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'max_active_runs': 1,
    'catchup': False
}

# DAG definition
dag = DAG(
    'mixpanel_analytics_pipeline',
    default_args=DEFAULT_ARGS,
    description='Phia Mixpanel Analytics Data Pipeline',
    schedule_interval='@daily',  # Run daily
    tags=['analytics', 'mixpanel', 'phia'],
    doc_md=__doc__
)


def get_mixpanel_credentials() -> Dict[str, str]:
    """
    Retrieve Mixpanel credentials from Airflow Variables or environment.
    
    Returns:
        Dictionary containing Mixpanel credentials
        
    Raises:
        AirflowException: If required credentials are missing
    """
    try:
        # Try to get from Airflow Variables first
        credentials = {
            'project_id': Variable.get('MIXPANEL_PROJECT_ID', default_var=None),
            'service_account_username': Variable.get('MIXPANEL_SERVICE_ACCOUNT_USERNAME', default_var=None),
            'service_account_secret': Variable.get('MIXPANEL_SERVICE_ACCOUNT_SECRET', default_var=None),
            'workspace_id': Variable.get('MIXPANEL_WORKSPACE_ID', default_var=None)
        }
        
        # Fall back to environment variables if not in Airflow Variables
        if not credentials['project_id']:
            credentials['project_id'] = os.getenv('MIXPANEL_PROJECT_ID')
        if not credentials['service_account_username']:
            credentials['service_account_username'] = os.getenv('MIXPANEL_SERVICE_ACCOUNT_USERNAME')
        if not credentials['service_account_secret']:
            credentials['service_account_secret'] = os.getenv('MIXPANEL_SERVICE_ACCOUNT_SECRET')
        if not credentials['workspace_id']:
            credentials['workspace_id'] = os.getenv('MIXPANEL_WORKSPACE_ID')
        
        # Validate required credentials
        required_fields = ['project_id', 'service_account_username', 'service_account_secret']
        missing_fields = [field for field in required_fields if not credentials[field]]
        
        if missing_fields:
            raise AirflowException(f"Missing required Mixpanel credentials: {missing_fields}")
        
        logger.info("✓ Successfully retrieved Mixpanel credentials")
        return credentials
        
    except Exception as e:
        raise AirflowException(f"Failed to retrieve Mixpanel credentials: {str(e)}")


def calculate_date_ranges(**context) -> Dict[str, str]:
    """
    Calculate date ranges for the analytics pipeline.
    
    Args:
        context: Airflow context containing execution date
        
    Returns:
        Dictionary containing date ranges for different metrics
    """
    execution_date = context['execution_date']
    
    # Main analytics range: last 30 days
    end_date = execution_date
    start_date = end_date - timedelta(days=30)
    
    # Weekly cohort for retention: previous week
    week_start = end_date - timedelta(days=7)
    week_end = end_date
    
    # Fixed cohort for specific retention analysis (June 19-25, 2024)
    fixed_cohort_start = "2024-06-19"
    fixed_cohort_end = "2024-06-25"
    
    date_ranges = {
        'main_from_date': start_date.strftime('%Y-%m-%d'),
        'main_to_date': end_date.strftime('%Y-%m-%d'),
        'weekly_cohort_start': week_start.strftime('%Y-%m-%d'),
        'weekly_cohort_end': week_end.strftime('%Y-%m-%d'),
        'fixed_cohort_start': fixed_cohort_start,
        'fixed_cohort_end': fixed_cohort_end,
        'execution_date': execution_date.strftime('%Y-%m-%d')
    }
    
    logger.info(f"Calculated date ranges: {date_ranges}")
    return date_ranges


def test_mixpanel_connection(**context) -> bool:
    """
    Test connection to Mixpanel API.
    
    Args:
        context: Airflow context
        
    Returns:
        True if connection successful
        
    Raises:
        AirflowException: If connection fails
    """
    logger.info("Testing Mixpanel API connection...")
    
    try:
        credentials = get_mixpanel_credentials()
        
        service = MixpanelService(
            project_id=credentials['project_id'],
            service_account_username=credentials['service_account_username'],
            service_account_secret=credentials['service_account_secret'],
            workspace_id=credentials['workspace_id'],
            respect_rate_limits=True
        )
        
        if service.test_connection():
            logger.info("✓ Mixpanel connection test successful")
            
            # Log service health
            health = service.get_service_health()
            logger.info(f"Service health: {health}")
            
            return True
        else:
            raise AirflowException("Mixpanel connection test failed")
            
    except Exception as e:
        raise AirflowException(f"Mixpanel connection test failed: {str(e)}")


def fetch_mixpanel_analytics(**context) -> Dict[str, Any]:
    """
    Fetch comprehensive analytics from Mixpanel.
    
    Args:
        context: Airflow context
        
    Returns:
        Dictionary containing all analytics data
        
    Raises:
        AirflowException: If data fetching fails
    """
    logger.info("Fetching comprehensive Mixpanel analytics...")
    
    try:
        # Get credentials and date ranges
        credentials = get_mixpanel_credentials()
        date_ranges = calculate_date_ranges(**context)
        
        # Initialize Mixpanel service
        service = MixpanelService(
            project_id=credentials['project_id'],
            service_account_username=credentials['service_account_username'],
            service_account_secret=credentials['service_account_secret'],
            workspace_id=credentials['workspace_id'],
            respect_rate_limits=True
        )
        
        # Fetch comprehensive analytics
        analytics = service.get_comprehensive_analytics(
            from_date=date_ranges['main_from_date'],
            to_date=date_ranges['main_to_date'],
            cohort_start_date=date_ranges['fixed_cohort_start'],
            cohort_end_date=date_ranges['fixed_cohort_end'],
            analysis_weeks=8
        )
        
        if not analytics['success']:
            raise AirflowException(f"Failed to fetch analytics: {analytics.get('error', 'Unknown error')}")
        
        # Add execution metadata
        analytics['execution_metadata'] = {
            'airflow_execution_date': context['execution_date'].isoformat(),
            'airflow_task_instance': str(context['task_instance']),
            'date_ranges': date_ranges,
            'pipeline_version': '1.0.0'
        }
        
        logger.info("✓ Successfully fetched comprehensive Mixpanel analytics")
        logger.info(f"Analytics data contains {len(analytics['data'])} metric types")
        
        # Store in XCom for next task
        context['task_instance'].xcom_push(key='analytics_data', value=analytics)
        
        return analytics
        
    except Exception as e:
        raise AirflowException(f"Failed to fetch Mixpanel analytics: {str(e)}")


def process_and_validate_analytics(**context) -> Dict[str, Any]:
    """
    Process and validate the fetched analytics data.
    
    Args:
        context: Airflow context
        
    Returns:
        Dictionary containing processed and validated data
        
    Raises:
        AirflowException: If processing or validation fails
    """
    logger.info("Processing and validating analytics data...")
    
    try:
        # Get analytics data from previous task
        analytics = context['task_instance'].xcom_pull(task_ids='fetch_mixpanel_analytics', key='analytics_data')
        
        if not analytics:
            raise AirflowException("No analytics data received from previous task")
        
        # Validate data structure
        required_sections = [
            'site_activation_metrics',
            'permission_funnel_metrics',
            'safari_extension_metrics',
            'weekly_active_users_metrics',
            'weekly_retention_metrics'
        ]
        
        data = analytics.get('data', {})
        missing_sections = [section for section in required_sections if section not in data]
        
        if missing_sections:
            raise AirflowException(f"Missing required data sections: {missing_sections}")
        
        # Validate specific metrics
        validation_results = {
            'site_activation_count': len(data.get('site_activation_metrics', [])),
            'permission_funnel_conversion_rate': data.get('permission_funnel_metrics', {}).get('conversion_rate', 0),
            'safari_active_users': data.get('safari_extension_metrics', {}).get('active_users', 0),
            'weekly_total_users': data.get('weekly_active_users_metrics', {}).get('total_active_users', 0),
            'retention_cohort_size': data.get('weekly_retention_metrics', {}).get('initial_cohort_size', 0)
        }
        
        # Log validation results
        logger.info("Data validation results:")
        for metric, value in validation_results.items():
            logger.info(f"  {metric}: {value}")
        
        # Add validation metadata
        analytics['validation_metadata'] = {
            'validation_timestamp': datetime.now().isoformat(),
            'validation_results': validation_results,
            'data_quality_score': 100.0,  # Could implement more sophisticated scoring
            'validation_passed': True
        }
        
        logger.info("✓ Analytics data processing and validation completed successfully")
        
        # Store processed data in XCom
        context['task_instance'].xcom_push(key='processed_analytics', value=analytics)
        
        return analytics
        
    except Exception as e:
        raise AirflowException(f"Failed to process and validate analytics: {str(e)}")


def store_analytics_in_database(**context) -> bool:
    """
    Store the analytics data in Supabase/PostgreSQL database.
    
    Args:
        context: Airflow context
        
    Returns:
        True if storage successful
        
    Raises:
        AirflowException: If database storage fails
    """
    logger.info("Storing analytics data in database...")
    
    try:
        # Get processed analytics data
        analytics = context['task_instance'].xcom_pull(task_ids='process_and_validate_analytics', key='processed_analytics')
        
        if not analytics:
            raise AirflowException("No processed analytics data received")
        
        # Get database connection (Supabase/PostgreSQL)
        postgres_hook = PostgresHook(postgres_conn_id='supabase_connection')
        
        # Prepare data for storage
        execution_date = context['execution_date'].strftime('%Y-%m-%d')
        data = analytics['data']
        
        # Store site activation metrics
        site_metrics = data.get('site_activation_metrics', [])
        for metric in site_metrics:
            postgres_hook.run("""
                INSERT INTO mixpanel_site_activation (
                    execution_date, site_domain, phia_shown_count, phia_clicked_count,
                    mobile_extension_shown, mobile_extension_activated, click_rate,
                    mobile_activation_rate, combined_activation_rate
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (execution_date, site_domain) 
                DO UPDATE SET
                    phia_shown_count = EXCLUDED.phia_shown_count,
                    phia_clicked_count = EXCLUDED.phia_clicked_count,
                    mobile_extension_shown = EXCLUDED.mobile_extension_shown,
                    mobile_extension_activated = EXCLUDED.mobile_extension_activated,
                    click_rate = EXCLUDED.click_rate,
                    mobile_activation_rate = EXCLUDED.mobile_activation_rate,
                    combined_activation_rate = EXCLUDED.combined_activation_rate
            """, parameters=(
                execution_date, metric['site_domain'], metric['phia_shown_count'],
                metric['phia_clicked_count'], metric['mobile_extension_shown'],
                metric['mobile_extension_activated'], metric['click_rate'],
                metric['mobile_activation_rate'], metric['combined_activation_rate']
            ))
        
        # Store permission funnel metrics
        funnel = data.get('permission_funnel_metrics', {})
        if funnel:
            postgres_hook.run("""
                INSERT INTO mixpanel_permission_funnel (
                    execution_date, enable_phia_views, almost_finished_views,
                    conversion_rate, funnel_completion_rate
                ) VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (execution_date)
                DO UPDATE SET
                    enable_phia_views = EXCLUDED.enable_phia_views,
                    almost_finished_views = EXCLUDED.almost_finished_views,
                    conversion_rate = EXCLUDED.conversion_rate,
                    funnel_completion_rate = EXCLUDED.funnel_completion_rate
            """, parameters=(
                execution_date, funnel['enable_phia_views'], funnel['almost_finished_views'],
                funnel['conversion_rate'], funnel['funnel_completion_rate']
            ))
        
        # Store Safari extension metrics
        safari = data.get('safari_extension_metrics', {})
        if safari:
            postgres_hook.run("""
                INSERT INTO mixpanel_safari_extension (
                    execution_date, active_users, total_clicks, filtered_clicks,
                    platform_breakdown_json
                ) VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (execution_date)
                DO UPDATE SET
                    active_users = EXCLUDED.active_users,
                    total_clicks = EXCLUDED.total_clicks,
                    filtered_clicks = EXCLUDED.filtered_clicks,
                    platform_breakdown_json = EXCLUDED.platform_breakdown_json
            """, parameters=(
                execution_date, safari['active_users'], safari['total_clicks'],
                safari['filtered_clicks'], json.dumps(safari['platform_breakdown'])
            ))
        
        # Store weekly active users metrics
        weekly = data.get('weekly_active_users_metrics', {})
        if weekly:
            postgres_hook.run("""
                INSERT INTO mixpanel_weekly_active_users (
                    execution_date, total_active_users, heartbeat_events,
                    os_breakdown_json, permission_state_breakdown_json
                ) VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (execution_date)
                DO UPDATE SET
                    total_active_users = EXCLUDED.total_active_users,
                    heartbeat_events = EXCLUDED.heartbeat_events,
                    os_breakdown_json = EXCLUDED.os_breakdown_json,
                    permission_state_breakdown_json = EXCLUDED.permission_state_breakdown_json
            """, parameters=(
                execution_date, weekly['total_active_users'], weekly['heartbeat_events'],
                json.dumps(weekly['os_breakdown']), json.dumps(weekly['permission_state_breakdown'])
            ))
        
        # Store retention metrics
        retention = data.get('weekly_retention_metrics', {})
        if retention:
            postgres_hook.run("""
                INSERT INTO mixpanel_weekly_retention (
                    execution_date, cohort_start_date, cohort_end_date,
                    initial_cohort_size, weekly_retention_rates_json,
                    heartbeat_retention_json, phia_clicked_retention_json
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (execution_date)
                DO UPDATE SET
                    cohort_start_date = EXCLUDED.cohort_start_date,
                    cohort_end_date = EXCLUDED.cohort_end_date,
                    initial_cohort_size = EXCLUDED.initial_cohort_size,
                    weekly_retention_rates_json = EXCLUDED.weekly_retention_rates_json,
                    heartbeat_retention_json = EXCLUDED.heartbeat_retention_json,
                    phia_clicked_retention_json = EXCLUDED.phia_clicked_retention_json
            """, parameters=(
                execution_date, retention['cohort_start_date'], retention['cohort_end_date'],
                retention['initial_cohort_size'], json.dumps(retention['weekly_retention_rates']),
                json.dumps(retention['heartbeat_retention']), json.dumps(retention['phia_clicked_retention'])
            ))
        
        # Store execution metadata
        postgres_hook.run("""
            INSERT INTO mixpanel_pipeline_executions (
                execution_date, success, analytics_json, validation_metadata_json,
                execution_metadata_json, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s)
            ON CONFLICT (execution_date)
            DO UPDATE SET
                success = EXCLUDED.success,
                analytics_json = EXCLUDED.analytics_json,
                validation_metadata_json = EXCLUDED.validation_metadata_json,
                execution_metadata_json = EXCLUDED.execution_metadata_json,
                updated_at = %s
        """, parameters=(
            execution_date, analytics['success'], json.dumps(analytics),
            json.dumps(analytics.get('validation_metadata', {})),
            json.dumps(analytics.get('execution_metadata', {})),
            datetime.now(),
            datetime.now()
        ))
        
        logger.info("✓ Successfully stored analytics data in database")
        logger.info(f"Stored {len(site_metrics)} site metrics, 1 funnel metric, 1 safari metric, 1 weekly metric, 1 retention metric")
        
        return True
        
    except Exception as e:
        raise AirflowException(f"Failed to store analytics in database: {str(e)}")


def send_success_notification(**context) -> bool:
    """
    Send success notification with analytics summary.
    
    Args:
        context: Airflow context
        
    Returns:
        True if notification sent successfully
    """
    logger.info("Sending success notification...")
    
    try:
        analytics = context['task_instance'].xcom_pull(task_ids='process_and_validate_analytics', key='processed_analytics')
        execution_date = context['execution_date'].strftime('%Y-%m-%d')
        
        # Create summary
        data = analytics.get('data', {})
        summary = {
            'execution_date': execution_date,
            'site_count': len(data.get('site_activation_metrics', [])),
            'conversion_rate': data.get('permission_funnel_metrics', {}).get('conversion_rate', 0),
            'safari_users': data.get('safari_extension_metrics', {}).get('active_users', 0),
            'total_active_users': data.get('weekly_active_users_metrics', {}).get('total_active_users', 0),
            'cohort_size': data.get('weekly_retention_metrics', {}).get('initial_cohort_size', 0)
        }
        
        logger.info(f"✓ Pipeline execution successful for {execution_date}")
        logger.info(f"Summary: {summary}")
        
        # Here you could integrate with Slack, email, or other notification systems
        # For now, we'll just log the success
        
        return True
        
    except Exception as e:
        logger.warning(f"Failed to send success notification: {str(e)}")
        return False


# Define tasks
test_connection_task = PythonOperator(
    task_id='test_mixpanel_connection',
    python_callable=test_mixpanel_connection,
    dag=dag,
    doc_md="Test connection to Mixpanel API"
)

fetch_analytics_task = PythonOperator(
    task_id='fetch_mixpanel_analytics',
    python_callable=fetch_mixpanel_analytics,
    dag=dag,
    doc_md="Fetch comprehensive analytics data from Mixpanel"
)

process_analytics_task = PythonOperator(
    task_id='process_and_validate_analytics',
    python_callable=process_and_validate_analytics,
    dag=dag,
    doc_md="Process and validate the fetched analytics data"
)

store_analytics_task = PythonOperator(
    task_id='store_analytics_in_database',
    python_callable=store_analytics_in_database,
    dag=dag,
    doc_md="Store analytics data in Supabase database"
)

notify_success_task = PythonOperator(
    task_id='send_success_notification',
    python_callable=send_success_notification,
    dag=dag,
    doc_md="Send success notification with analytics summary"
)

# Define task dependencies
test_connection_task >> fetch_analytics_task >> process_analytics_task >> store_analytics_task >> notify_success_task

# Task group documentation
dag.doc_md = """
# Phia Mixpanel Analytics Pipeline

This DAG orchestrates the daily collection and processing of Mixpanel analytics data for the Phia extension dashboard.

## Pipeline Flow
1. **Test Connection**: Verify Mixpanel API credentials and connectivity
2. **Fetch Analytics**: Retrieve comprehensive analytics data from Mixpanel
3. **Process & Validate**: Process and validate the fetched data
4. **Store in Database**: Store the processed data in Supabase
5. **Send Notification**: Send success notification with summary

## Metrics Collected
- Site activation rates by domain
- Permission funnel conversion rates
- Safari extension active users
- Weekly active users with OS breakdown
- Weekly retention metrics for cohorts

## Configuration
- **Schedule**: Daily at 2 AM UTC
- **Retries**: 2 attempts with 5-minute delays
- **Concurrency**: Max 1 active run

## Monitoring
- Check Airflow logs for detailed execution information
- Monitor Supabase tables for data quality
- Review notification summaries for key metrics
"""