from airflow import DAG
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
from google.cloud import bigquery
import logging
import os
from dependencies.utils.misc import IS_DEV
from dependencies.affiliate.common import get_bigquery_schema
import pandas as pd

logger = logging.getLogger(__name__)

from dependencies.affiliate.shopmy_transform import load_shopmy_affiliates
from dependencies.affiliate.wildfire_transform import load_wildfire_affiliates
from dependencies.affiliate.bigtable_transfer import transfer_to_bigtable

PROJECT_ID = 'phia-prod-416420'
DATASET_ID = 'affiliate'
SHOPMY_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.advertiser_shopmy'
WILDFIRE_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.advertiser_wildfire'
AFFILIATE_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.merchant_links'

BIGTABLE_INSTANCE = 'phia-dict'
BIGTABLE_TABLE = 'phia-dict'

def load_to_bigquery(df: pd.DataFrame, table_id: str):
    logger.info(f"Loading data to BigQuery table: {table_id}")
    client = bigquery.Client()
    
    try:
        table = client.get_table(table_id)
        current_schema = table.schema
        expected_schema = get_bigquery_schema()
        
        schema_matches = len(current_schema) == len(expected_schema)
        if schema_matches:
            for current_field, expected_field in zip(current_schema, expected_schema):
                if (current_field.name != expected_field.name or 
                    current_field.field_type != expected_field.field_type):
                    schema_matches = False
                    break
        
        if not schema_matches:
            logger.info(f"Schema mismatch detected for table {table_id}, dropping and recreating...")
            client.delete_table(table_id)
            table = bigquery.Table(table_id, schema=expected_schema)
            client.create_table(table)
        else:
            logger.info(f"Table {table_id} exists with matching schema, truncating...")
            truncate_query = f"TRUNCATE TABLE `{table_id}`"
            client.query(truncate_query).result()
    except Exception as e:
        logger.info(f"Table {table_id} does not exist or error occurred: {str(e)}, creating...")
        schema = get_bigquery_schema()
        table = bigquery.Table(table_id, schema=schema)
        client.create_table(table)
    
    job_config = bigquery.LoadJobConfig(
        write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
        schema=get_bigquery_schema()
    )
    
    job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
    job.result()
    logger.info(f"Loaded {len(df)} rows into {table_id}")

def load_shopmy_task(**kwargs):
    logger.info("Starting ShopMy task")
    df = load_shopmy_affiliates()
    load_to_bigquery(df, SHOPMY_TABLE_ID)
    logger.info("ShopMy task completed")

def load_wildfire_task(**kwargs):
    logger.info("Starting Wildfire task")
    df = load_wildfire_affiliates()
    load_to_bigquery(df, WILDFIRE_TABLE_ID)
    logger.info("Wildfire task completed")


default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True if not IS_DEV() else False,
    'email_on_retry': False,
    'retries': 3 if not IS_DEV() else 0,
    'retry_delay': timedelta(minutes=5),
    'email': '<EMAIL>',
}

with DAG(
    'affiliate_link_dag',
    default_args=default_args,
    description='Unified affiliate data pipeline for all networks',
    schedule='0 3 * * *' if not IS_DEV() else None,
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['affiliate', 'rates', 'bigquery'],
) as dag:

    load_shopmy = PythonOperator(
        task_id='load_shopmy',
        python_callable=load_shopmy_task,
    )

    load_wildfire = PythonOperator(
        task_id='load_wildfire',
        python_callable=load_wildfire_task,
    )

    run_merge_query = BigQueryExecuteQueryOperator(
        task_id="run_merge_query",
        sql="dependencies/affiliate/merge_affiliate_tables.sql",  # relative to the DAG file
        use_legacy_sql=False,
        params={
            "affiliate_table_id": AFFILIATE_TABLE_ID,
            "shopmy_table_id": SHOPMY_TABLE_ID,
            "wildfire_table_id": WILDFIRE_TABLE_ID,
            'encoded_url': '{{encoded_url}}'
        }
    )

    transfer_to_bigtable_task = PythonOperator(
        task_id='transfer_to_bigtable',
        python_callable=transfer_to_bigtable,
        op_kwargs={
            'project_id': PROJECT_ID,
            'bigquery_table_id': AFFILIATE_TABLE_ID,
            'bigtable_instance': BIGTABLE_INSTANCE,
            'bigtable_table': BIGTABLE_TABLE
        }
    )

    [load_shopmy, load_wildfire] >> run_merge_query >> transfer_to_bigtable_task 