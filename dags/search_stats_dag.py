from airflow import DAG
from airflow.providers.google.cloud.operators.bigquery import (
    BigQueryCreateEmptyTableOperator,
    BigQueryInsertJobOperator,
)
from airflow.operators.python import PythonOperator
from google.cloud import bigquery
from dependencies.search_stats.build_query import (
    build_clean_search_table_query,
    build_latency_summary_query,
    build_delete_raw_processed_query
)
from dependencies.search_stats.build_schema import (
    build_search_clean_table_schema,
    build_search_latency_table_schema
)
from dependencies.search_stats.constants import (
    PROJECT_ID,
    DATASET_ID,
    CLEAN_TABLE_ID,
    LATENCY_TABLE_ID
)
from dependencies.utils.misc import IS_DEV
from datetime import datetime, timedelta

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'email': '<EMAIL>',
}

with DAG(
    dag_id="search_events_stats_dag",
    default_args=default_args,
    description="Search Events Data flow for Stats",
    schedule="*/15 * * * *" if not IS_DEV() else None,
    start_date=datetime(2025, 5, 27),
    catchup=False,
    tags=["bigquery", "search", "latency"],
) as dag:

    # Ensure clean search table exists
    create_clean_search = BigQueryCreateEmptyTableOperator(
        task_id="create_search_clean_table",
        project_id=PROJECT_ID,
        dataset_id=DATASET_ID,
        table_id=CLEAN_TABLE_ID,
        schema_fields=build_search_clean_table_schema(),
        time_partitioning={"type": "HOUR", "field": "timestamp"},
        exists_ok=True,
    )

    # Insert the last 15 minutes of SEARCH events into that clean table
    udpated_clean_search = BigQueryInsertJobOperator(
        task_id="insert_search_clean_rows",
        configuration={
            "query": {
                "query": build_clean_search_table_query(),
                "useLegacySql": False,
            }
        },
    )

    # Delete old raw SEARCH rows (> 90 minutes old) from the sink
    def delete_old_search(**context):
        client = bigquery.Client()
        sql = build_delete_raw_processed_query()
        client.query(sql).result()

    cleanup_raw_search = PythonOperator(
        task_id="delete_old_search_rows",
        python_callable=delete_old_search,
    )

    # Ensure the P90‐latency summary table exists
    create_latency = BigQueryCreateEmptyTableOperator(
        task_id="create_search_latency_table",
        project_id=PROJECT_ID,
        dataset_id=DATASET_ID,
        table_id=LATENCY_TABLE_ID,
        schema_fields=build_search_latency_table_schema(),
        time_partitioning={"type": "HOUR", "field": "timestamp"},
        exists_ok=True,
    )

    # Compute and append P90 for all five latency metrics
    append_latency = BigQueryInsertJobOperator(
        task_id="insert_search_latency_summary",
        configuration={
            "query": {
                "query": build_latency_summary_query(),
                "useLegacySql": False,
            }
        },
    )

    create_clean_search >> udpated_clean_search
    udpated_clean_search >> cleanup_raw_search
    udpated_clean_search >> create_latency >> append_latency