# Local Cloud Composer Development Environment

This repository contains setup for local Cloud Composer development environment. It allows you to test and develop Airflow DAGs locally before deploying them to Cloud Composer.

## Prerequisites

- Python 3.7 or later
- Docker
- Git

## Add more python libraries and environment variables

Download the schema to your local place before proceeding:
python3 utils/add_schema_to_composer.py

add it to terraform for test and prod env respectively:
https://github.com/phiadev/terraform/blob/main/modules/cloud-composer/main.tf#L22-L27

to test the dag, push the changes to test branch.
Then go to https://console.cloud.google.com/composer/environments?referrer=search&hl=en&inv=1&invt=Abx56A&project=phia-prod-416420 
to find "phia-scheduler-test" to  test the dag.
